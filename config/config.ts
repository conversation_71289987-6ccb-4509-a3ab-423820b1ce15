import { defineConfig } from '@umijs/max';
import defaultSettings from './defaultSettings';
import proxy from './proxy';
import routes from './routes';
const { REACT_APP_ENV = 'dev' } = process.env;
export default defineConfig({
  qiankun: {
    slave: {},
  },
  // history: { type: 'hash' },
  // base:'/index.html',
  // exportStatic: {
  // },
  base: '/',
  headScripts: [
    // 解决首次加载时白屏的问题
    { src: '/scripts/loading.js', async: true },
  ],
  hash: true,
  theme: {
    'primary-color': defaultSettings.colorPrimary,
  },
  proxy: proxy[REACT_APP_ENV as keyof typeof proxy],
  antd: {},
  model: {},
  access: {},
  initialState: {},
  clickToComponent: {},
  request: {},
  fastRefresh: true,
  layout: {
    // title: '@umijs/max'
    locale: false,
    ...defaultSettings,
  },
  unocss: {
    watch: ['src/**/*.tsx']
  },
  moment2dayjs: {
    preset: 'antd',
    plugins: ['duration'],
  },
  plugins: [
    require.resolve('@umijs/plugins/dist/unocss')
  ],
  valtio: {},
  routes,
  mfsu: {
    strategy: 'normal',
  },
  npmClient: 'yarn',
  define: {
    'process.env': {
      REACT_APP_ENV: REACT_APP_ENV,
      UMI_ENV: process.env.UMI_ENV,
    },
  }
});
