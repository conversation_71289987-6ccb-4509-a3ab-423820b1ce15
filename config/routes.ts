/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */

export default [
  {
    path: '/',
    redirect: '/home',
  },
  // {
  //   name: '概览',
  //   path: '/home',
  //   icon: 'HomeOutlined',
  //   component: './Dashboard',
  //   // wrappers:['@/wrappers/Auth'],
  // },
  {
    name: '概览',
    path: '/home',
    icon: 'HomeOutlined',
    component: './frontHome',
    // wrappers:['@/wrappers/Auth'],
  },
  {
    path: '/waybillManagement',
    name: '运单管理',
    icon: 'OrderedListOutlined',
    access: 'Waybill',
    routes: [
      { path: '/waybillManagement', redirect: '/waybillManagement/waybill' },
      {
        name: '运单查询',
        path: '/waybillManagement/waybill',
        component: './WaybillManagement/Waybill',
        access: 'Waybill',
      },
      {
        name: '运单详情',
        path: '/waybillManagement/waybill/details',
        hideInMenu: true,
        component: './WaybillManagement/WaybillDetails',
        access: 'Waybill',
      },
      {
        name: '运单创建',
        path: '/waybillManagement/createWaybill',
        component: './WaybillManagement/CreateWaybill',
        access: 'Waybill',
      },
      {
        name: '卡派试算',
        path: 'waybillManagement/calculate',
        component: './WaybillManagement/Calculate',
        access: 'TruckFullAccess',
      },
      {
        path: '/waybillManagement/calculate/priceRecord/priceDetails',
        name: '报价记录详情',
        hideInMenu: true,
        access: 'TruckFullAccess',
        component: './WaybillManagement/Calculate/PriceRecord/PriceDetails',
      },
    ],
  },
  {
    name: '账号信息',
    path: '/account/settings',
    hideInMenu: true,
    component: './Account/Settings',
    // wrappers:['@/wrappers/Auth'],
  },
  {
    path: '/finance',
    name: '财务管理',
    icon: 'PropertySafetyOutlined',
    routes: [
      { path: '/finance', redirect: '/finance/recharge' },
      {
        name: '充值明细',
        path: '/finance/recharge',
        component: './Finance/Recharge',
        // wrappers:['@/wrappers/Auth'],
      },
      {
        name: '对账管理',
        path: '/finance/statement',
        component: './Finance/Statement',
        access: 'ReconciliationManagement',
      },
      {
        name: '消费明细',
        path: '/finance/consume',
        component: './Finance/Consume',
        access: 'ConsumptionDetails',
      },
      // {
      //   name: '报表明细',
      //   path: '/finance/report',
      //   component: './Finance/Report',
      //   // wrappers:['@/wrappers/Auth'],
      // },
      {
        path: '*',
        component: '404',
      },
    ],
  },
  {
    path: '/finance/statement/Details',
    name: '对账管理-详情',
    hideInMenu: true,
    access: 'ReconciliationManagement',
    component: './Finance/Details',
  },
  {
    path: '/dataManagement',
    name: '资料管理',
    icon: 'SnippetsOutlined',
    routes: [
      { path: '/dataManagement', redirect: '/dataManagement/address' },
      {
        path: '/dataManagement/address',
        name: '地址库',
        component: './DataManagement/Address',
        access: 'AddressLibrary',
      },
      {
        path: '/dataManagement/channelProducts',
        name: '渠道产品',
        component: './DataManagement/ChannelProducts',
      },
      {
        path: '/dataManagement/channelProducts/channelDetails',
        name: '渠道详情',
        hideInMenu: true,
        component: './DataManagement/ChannelProducts/ChannelDetails',
      },
    ],
  },
  // {
  //   path: '/OpenPlatform',
  //   name: '开放平台',
  //   icon: 'SplitCellsOutlined',
  //   component: './OpenPlatform',
  //   access: 'OpenAPI',
  // },
  // {
  //   path: '/channel',
  //   name: '渠道管理',
  //   icon: 'ClusterOutlined',
  //   component: './Channel',
  // },
  // {
  //   path: '/customer',
  //   name: '客户管理',
  //   icon: 'TeamOutlined',
  //   component: './Customer',
  // },

  {
    path: '/permission',
    name: '访问控制',
    icon: 'setting',
    access: 'RAM',
    routes: [
      {
        path: '/permission',
        redirect: '/Permission/Role',
      },
      {
        name: '角色管理',
        icon: 'smile',
        path: '/permission/role',
        component: './Permission/Role',
        access: 'RAM',
      },
      {
        name: '用户管理',
        icon: 'smile',
        path: '/permission/user',
        component: './Permission/User',
        access: 'RAM',
      },
    ],
  },
  {
    path: '/OpenPlatform',
    name: '开放平台',
    icon: 'ProfileOutlined',
    component: './OpenPlatform',
  },
  {
    path: '/logs',
    name: '操作日志',
    icon: 'ProfileOutlined',
    component: './List',
  },
  // {
  //   path: ()=>{
  //     console.log(111);
  //     return '/list'
  //   },
  //   name: '管理后台',
  //   icon:'chromeFilled',
  // },
  {
    path: '*',
    layout: false,
    component: './404',
  },
  {
    path: '/403',
    layout: false,
    component: './403',
  },
];
