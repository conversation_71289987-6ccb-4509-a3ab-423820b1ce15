// const CryptoJS = require('crypto-js');  //引用AES源码js
import CryptoJS from 'crypto-js';
const key = CryptoJS.enc.Utf8.parse('FaHbyipLI6LqXFbL'); //十六位十六进制数作为密钥
const iv = CryptoJS.enc.Utf8.parse(''); //十六位十六进制数作为密钥偏移量

//解密方法
function Decrypt(word: any) {
  let encryptedHexStr = CryptoJS.enc.Hex.parse(word);
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  let decrypt = CryptoJS.AES.decrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}

//加密方法
function Encrypt(word: any) {
  let srcs = CryptoJS.enc.Utf8.parse(word);
  let encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  // return encrypted.ciphertext.toString().toUpperCase();
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
}

export default {
  Decrypt,
  Encrypt,
};
/* 头像链接 */
const AVATA_URL = 'https://static.kmfba.com/'; // gravatar
/* 根据url字段 判断是否有http前缀 如果有 那么直接返回 如果没有那么拼接AVATA_URL */
export const getAvataUrl = (url: string) => {
  if (url?.includes('http')) {
    return url;
  }
  return AVATA_URL + url;
};
