// 示例方法，没有实际意义
import { TapMap } from '@/constants';

export function trim(str: string) {
  return str.trim();
}

/* 时间戳转YYYY-MM-DD HH:mm */
export const formatTime = (time: any) => {
  const date = new Date(time);
  const Y = date.getFullYear() + '-';
  const M =
    (date?.getMonth() + 1 < 10
      ? '0' + (date.getMonth() + 1)
      : date?.getMonth() + 1) + '-';
  const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
  const h =
    (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  const m =
    (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) +
    ':';
  const s =
    date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
  return Y + M + D + h + m + s;
};
// 秒转换
export function formatTimestamp(seconds: any) {
  // 注意：JavaScript 的 Date 使用的是毫秒，所以要乘以 1000
  const date = new Date(seconds * 1000);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const secondsFormatted = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${secondsFormatted}`;
}
/* 时间戳转YYYY-MM-DD HH*/
export const formatTimes = (time: any) => {
  const date = new Date(time);
  const Y = date.getFullYear() + '-';
  const M =
    (date.getMonth() + 1 < 10
      ? '0' + (date.getMonth() + 1)
      : date.getMonth() + 1) + '-';
  const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + '';
  return Y + M + D;
};

/* 时间戳转YYYY-MM-DD */
export const formatDate = (time: any) => {
  const date = new Date(time);
  const Y = date.getFullYear() + '-';
  const M =
    (date.getMonth() + 1 < 10
      ? '0' + (date.getMonth() + 1)
      : date.getMonth() + 1) + '-';
  const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
  return Y + M + D;
};
function padZero(num) {
  return num < 10 ? '0' + num : num;
}

function formatDates(date) {
  const year = date.getFullYear();
  const month = padZero(date.getMonth() + 1); // 月份从0开始，需要加1
  const day = padZero(date.getDate());
  const hours = padZero(date.getHours());
  const minutes = padZero(date.getMinutes());
  const seconds = padZero(date.getSeconds());
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
/*生成标签*/
export const GenerateTab = (v: any) => {
  if (v) {
    const index = v.charCodeAt(0) % 11;
    return TapMap[index];
  } else {
    return TapMap[1];
  }
  // @ts-ignore
};
// 获取上周开始时间和结束时间
export const getLastWeekRange = () => {
  const today = new Date();
  const currentDayOfWeek = today.getDay();
  const lastWeekStartDate = new Date();
  lastWeekStartDate.setDate(today.getDate() - currentDayOfWeek - 6);
  lastWeekStartDate.setHours(0, 0, 0, 0);
  const lastWeekEndDate = new Date();
  lastWeekEndDate.setDate(today.getDate() - currentDayOfWeek - 1);
  lastWeekEndDate.setHours(23, 59, 59, 999);
  return {
    startDate: lastWeekStartDate,
    endDate: lastWeekEndDate,
  };
};
// 获取上上周时间，并且计算是今年第多少周
export const getLastLastWeekRange = () => {
  const today = new Date();
  const currentDayOfWeek = today.getDay();

  const lastWeekStartDate: any = new Date();
  // 计算周的
  const weekDate: any = new Date(lastWeekStartDate.getFullYear(), 0, 1);
  lastWeekStartDate.setDate(today.getDate() - currentDayOfWeek - 6 - 7);
  lastWeekStartDate.setHours(0, 0, 0, 0);

  const lastWeekEndDate = new Date();
  lastWeekEndDate.setDate(today.getDate() - currentDayOfWeek - 1 - 7);
  lastWeekEndDate.setHours(23, 59, 59, 999);
  // 计算开始时间是今年的第几周
  const startWeekNumber = Math.ceil(
    (lastWeekStartDate - weekDate) / (1000 * 60 * 60 * 24 * 7),
  );
  return {
    startDate: lastWeekStartDate,
    endDate: lastWeekEndDate,
    startWeek: '-2周',
  };
};

export const getNextWeekRangeTime = () => {
  const now = new Date();
  const day = now.getDay();
  let daysUntilNextMonday = day === 1 ? 7 : (1 - day + 7) % 7;

  const nextMonday = new Date(now);
  nextMonday.setDate(now.getDate() + daysUntilNextMonday);
  nextMonday.setHours(0, 0, 0, 0);

  const startTimeDate = new Date(nextMonday);
  startTimeDate.setDate(nextMonday.getDate() + 1);

  const endTimeDate = new Date(nextMonday);
  endTimeDate.setDate(nextMonday.getDate() + 6);
  endTimeDate.setHours(23, 59, 59, 0);

  const format = (date: Date) => {
    const pad = (n: number) => n.toString().padStart(2, '0');
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(
      date.getDate(),
    )} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(
      date.getSeconds(),
    )}`;
  };

  return {
    startTime: format(startTimeDate),
    endTime: format(endTimeDate),
  };
};
// 获取上上上周开始时间结束时间，并且返回今年第多少周
export const getLastLastLastWeekRange = () => {
  const today = new Date();
  const currentDayOfWeek = today.getDay();

  const lastWeekStartDate: any = new Date();
  // 计算周的
  const weekDate: any = new Date(lastWeekStartDate.getFullYear(), 0, 1);
  lastWeekStartDate.setDate(today.getDate() - currentDayOfWeek - 6 - 14);
  lastWeekStartDate.setHours(0, 0, 0, 0);

  const lastWeekEndDate = new Date();
  lastWeekEndDate.setDate(today.getDate() - currentDayOfWeek - 1 - 14);
  lastWeekEndDate.setHours(23, 59, 59, 999);

  const startWeekNumber = Math.ceil(
    (lastWeekStartDate - weekDate) / (1000 * 60 * 60 * 24 * 7),
  );

  return {
    startDate: lastWeekStartDate,
    endDate: lastWeekEndDate,
    startWeek: '-3周',
  };
};
// 获取本周开始时间和结束时间
export const getTodayWeekRange = () => {
  // const today = new Date();
  // const currentDayOfWeek = today.getDay();
  // const currentWeekStartDate = new Date();
  // currentWeekStartDate.setDate(today.getDate() - 1);
  // currentWeekStartDate.setHours(0, 0, 0, 0);
  // const currentWeekEndDate = new Date();
  // currentWeekEndDate.setDate(today.getDate() + (7 - currentDayOfWeek));
  // currentWeekEndDate.setHours(23, 59, 59, 999);
  const today = new Date();
  const currentDayOfWeek = today.getDay(); // 0 表示周日, 1 表示周一, ..., 6 表示周六
  // 计算本周的周一日期（开始时间）
  let weekStartDate = new Date(today);
  weekStartDate.setDate(
    today.getDate() - currentDayOfWeek + (currentDayOfWeek === 0 ? -6 : 1),
  );
  weekStartDate.setHours(0, 0, 0, 0); // 设置为当天的00:00:00

  // 计算本周的周日日期（结束时间）
  let weekEndDate = new Date(weekStartDate);
  weekEndDate.setDate(weekStartDate.getDate() + 6);
  // weekEndDate.setHours(23, 59, 59, 999); // 设置为当天的23:59:59.999
  return {
    // startDate: `${formatTimes(currentWeekStartDate)} 00:00:00`,
    // endDate: `${formatTimes(currentWeekEndDate)} 23:59:59`,
    startDate: `${formatTimes(weekStartDate).split(' ')[0]} 00:00:00`,
    endDate: `${formatTimes(weekEndDate).split(' ')[0]} 23:59:59`,
  };
};
// 获取下周开始时间和结束时间
export const getNextWeekRange = () => {
  const today = new Date();
  const currentDayOfWeek = today.getDay();
  // const nextWeekStartDate: any = new Date(
  //   today.getTime() + 7 * 24 * 60 * 60 * 1000,
  // );
  // const nextWeekEndDate = new Date(
  //   nextWeekStartDate.getTime() + 6 * 24 * 60 * 60 * 1000,
  // );

  const nextWeekStartDate: any = new Date();
  // 计算周的
  // const weekDate: any = new Date(nextWeekStartDate.getFullYear(), 0, 1);
  nextWeekStartDate.setDate(today.getDate() - currentDayOfWeek + 7);
  nextWeekStartDate.setHours(0, 0, 0, 0);
  const nextWeekEndDate: any = new Date();
  // 计算周的
  // const weekDate: any = new Date(lastWeekStartDate.getFullYear(), 0, 1);
  // lastWeekStartDate.setDate(today.getDate() - currentDayOfWeek - 6 - 14);

  // const yearStart: any = new Date(today.getFullYear(), 0, 1);
  nextWeekEndDate.setDate(today.getDate() - currentDayOfWeek + 7 + 6);

  nextWeekEndDate.setHours(23, 59, 59, 999);
  // const millisecondsPerWeek = 7 * 24 * 60 * 60 * 1000;
  // const weekNumber = Math.ceil(
  //   (nextWeekStartDate - yearStart) / millisecondsPerWeek,
  // );
  return {
    startDate: nextWeekStartDate,
    endDate: nextWeekEndDate,
    startWeek: '1周',
  };
};
// 获取下下周开始时间和结束时间
export const getTheWeekRange = () => {
  // const today = new Date();
  // const nextNextWeekStartDate: any = new Date(
  //   today.getTime() + 14 * 24 * 60 * 60 * 1000,
  // );
  // const nextNextWeekEndDate = new Date(
  //   nextNextWeekStartDate.getTime() + 6 * 24 * 60 * 60 * 1000,
  // );
  // const yearStart: any = new Date(today.getFullYear(), 0, 1);
  // const millisecondsPerWeek = 7 * 24 * 60 * 60 * 1000;
  // const weekNumber = Math.ceil(
  //   (nextNextWeekStartDate - yearStart) / millisecondsPerWeek,
  // );
  const today = new Date();
  const currentDayOfWeek = today.getDay();
  // const nextWeekStartDate: any = new Date(
  //   today.getTime() + 7 * 24 * 60 * 60 * 1000,
  // );
  // const nextWeekEndDate = new Date(
  //   nextWeekStartDate.getTime() + 6 * 24 * 60 * 60 * 1000,
  // );

  const nextNextWeekStartDate: any = new Date();
  // 计算周的
  // const weekDate: any = new Date(nextNextWeekStartDate.getFullYear(), 0, 1);
  nextNextWeekStartDate.setDate(today.getDate() - currentDayOfWeek + 7 + 7);
  nextNextWeekStartDate.setHours(0, 0, 0, 0);
  const nextNextWeekEndDate: any = new Date();
  // 计算周的
  // const weekDate: any = new Date(lastWeekStartDate.getFullYear(), 0, 1);
  // lastWeekStartDate.setDate(today.getDate() - currentDayOfWeek - 6 - 14);

  // const yearStart: any = new Date(today.getFullYear(), 0, 1);
  nextNextWeekEndDate.setDate(today.getDate() - currentDayOfWeek + 7 + 13);

  nextNextWeekEndDate.setHours(23, 59, 59, 999);
  // const millisecondsPerWeek = 7 * 24 * 60 * 60 * 1000;
  // const weekNumber = Math.ceil(
  //   (nextWeekStartDate - yearStart) / millisecondsPerWeek,
  // );
  return {
    startDate: nextNextWeekStartDate,
    endDate: nextNextWeekEndDate,
    startWeek: '2周',
  };
};

/*获取oss的文件的名称*/
export function getLastPartAfterSlash(str: any) {
  if (typeof str !== 'string' || str.length === 0) {
    // 防止传入空字符串或非字符串参数
    return null;
  }

  const lastSlashIndex = str.lastIndexOf('/');
  if (lastSlashIndex === -1 || lastSlashIndex === str.length - 1) {
    // 如果没有找到斜杠，或者斜杠位于字符串的最后一个位置，返回null
    return null;
  }

  return str.substring(lastSlashIndex + 1); // 或者使用 str.slice(lastSlashIndex + 1);
}
export function getThisWeekDates() {
  let today = new Date();
  let dayOfWeek = today.getDay(); // 0 for Sunday, 1 for Monday, etc.
  let startDate = new Date(today);
  startDate.setDate(today.getDate() - dayOfWeek + 1); // Adjust to Monday of this week
  startDate.setHours(0, 0, 0, 0); // Set to beginning of the day

  let endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6); // Adjust to Sunday of this week
  endDate.setHours(23, 59, 59, 999); // Set to end of the day

  return {
    startDate,
    endDate,
  };
}
export function getYearBoundaries(yearsBack = 0) {
  // 获取当前日期
  const now = new Date();

  // 设置年份
  now.setFullYear(now.getFullYear() - yearsBack);

  // 设置年初的开始时间为00:00:00
  now.setMonth(0); // 0代表一月
  now.setDate(1);
  now.setHours(0);
  now.setMinutes(0);
  now.setSeconds(0);
  now.setMilliseconds(0); // 可选，如果你想要精确到毫秒

  // 获取年初的时间戳
  const startOfYear = now.getTime();

  // 复制开始时间对象以计算年末的结束时间
  const endOfYearDate = new Date(startOfYear);

  // 设置年末的结束时间为12月31日的23:59:59
  endOfYearDate.setMonth(11); // 11代表十二月
  endOfYearDate.setDate(31);
  endOfYearDate.setHours(23);
  endOfYearDate.setMinutes(59);
  endOfYearDate.setSeconds(59);
  endOfYearDate.setMilliseconds(999); // 可选，如果你想要精确到毫秒

  // 获取年末的时间戳
  const endOfYear = endOfYearDate.getTime();

  // 返回开始和结束时间戳（或者你可以选择返回格式化后的日期字符串）
  return {
    startOfYear: startOfYear,
    endOfYear: endOfYear,
  };
}

export function getWeekRange(date, offsetWeeks) {
  // // 复制date对象，避免影响原始对象
  // const cloneDate = new Date(date);

  // // 设置日期为当周的第一天（假设周一为第一天）
  // cloneDate.setDate(cloneDate.getDate() - cloneDate.getDay() + 1);

  // // 计算结束日期（当周的周日）
  // const endDate = new Date(cloneDate);
  // endDate.setDate(endDate.getDate() + 6);

  // // 如果需要偏移，则调整开始和结束日期
  // if (offsetWeeks) {
  //   cloneDate.setDate(cloneDate.getDate() - offsetWeeks * 7);
  //   endDate.setDate(endDate.getDate() - offsetWeeks * 7);
  // }

  // 复制date对象，避免影响原始对象
  const cloneDate = new Date(date);

  // 设置日期为当周的第一天（假设周一为第一天）
  const dayOfWeek = cloneDate.getDay(); // 获取星期几（0是周日，1是周一，...，6是周六）
  const diff = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // 如果是周日，需要减6天回到上周一，否则减到本周一
  cloneDate.setDate(cloneDate.getDate() + diff); // 设置到本周一

  // 计算结束日期（当周的周日）
  const endDate = new Date(cloneDate);
  endDate.setDate(endDate.getDate() + 6);

  // 如果需要偏移，则调整开始和结束日期
  if (offsetWeeks) {
    cloneDate.setDate(cloneDate.getDate() - offsetWeeks * 7);
    endDate.setDate(endDate.getDate() - offsetWeeks * 7);
  }

  return {
    start: cloneDate,
    end: endDate,
  };
}

export function getWeekNumber(date) {
  // 获取当前日期所在的年份和第一天的日期
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
  // 获取当前日期是一年中的第几天
  const dayOfYear = Math.floor(
    (date - firstDayOfYear + 86400000) / (1000 * 60 * 60 * 24),
  );
  // 获取第一天是一周中的第几天（周日为0，周一为1）
  const firstDayOfWeek = firstDayOfYear.getDay() || 7;
  // 计算周数（注意JavaScript中周日的值可能是0或1，这里假设是0）
  const weekNumber = Math.ceil((dayOfYear + firstDayOfWeek) / 7);
  return weekNumber;
}
export function getTodayRange() {
  const now = new Date();

  // 当天开始时间（00:00:00）
  const startOfDay = new Date(
    Date.UTC(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0),
  );
  // 当天结束时间（23:59:59）
  const endOfDay = new Date(
    Date.UTC(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59),
  );

  // 转换为本地时间（如果需要的话）
  // const localStartOfDay = new Date(startOfDay.getTime() + (now.getTimezoneOffset() * 60000));
  // const localEndOfDay = new Date(endOfDay.getTime() + (now.getTimezoneOffset() * 60000));

  // 使用formatDate函数格式化日期
  return {
    start: `${formatTimes(startOfDay)} 00:00:00`, // 或者使用 startOfDay.toISOString().slice(0, 19).replace('T', ' ')
    end: `${formatTimes(endOfDay)} 23:59:59`, // 或者使用 endOfDay.toISOString().slice(0, 19).replace('T', ' ').replace(/\.\d{3}Z$/, '')
  };
}

export function getMonthRange() {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth();

  // 本月开始时间（1号00:00:00）
  const startOfMonth = new Date(Date.UTC(year, month, 1, 0, 0, 0));
  // 本月结束时间（最后一天23:59:59）
  const endOfMonth = new Date(Date.UTC(year, month + 1, 0, 23, 59, 59));

  // 转换为本地时间（如果需要的话，否则可以直接使用UTC时间）
  // 注意：这里转换为本地时间可能会因为时区差异而引入问题，如果不需要考虑时区，可以直接使用UTC时间
  const localStartOfMonth = new Date(
    startOfMonth.getTime() + now.getTimezoneOffset() * 60000,
  );
  const localEndOfMonth = new Date(
    endOfMonth.getTime() + now.getTimezoneOffset() * 60000,
  );

  // 使用formatDate函数格式化日期
  return {
    start: `${formatTimes(localStartOfMonth)} 00:00:00`, // 或者使用 startOfMonth.toISOString().slice(0, 19).replace('T', ' ') 如果不需要时区转换
    end: `${formatTimes(localEndOfMonth)} 23:59:59`, // 或者使用 endOfMonth.toISOString().slice(0, 19).replace('T', ' ').replace(/\.\d{3}Z$/, '')
  };
}
export function getYearRange() {
  const now = new Date();
  const year = now.getFullYear();

  // 当前年开始时间（1月1号00:00:00）
  const startOfYear = new Date(Date.UTC(year, 0, 1, 0, 0, 0));
  // 更严谨地计算当前年结束时间（下一年的第一天减去1毫秒）
  const endOfYear = new Date(Date.UTC(year + 1, 0, 0, 0, 0, 0) - 1);

  // 转换为本地时间（如果需要的话）
  // const localStartOfYear = new Date(startOfYear.getTime() + (now.getTimezoneOffset() * 60000));
  // const localEndOfYear = new Date(endOfYear.getTime() + (now.getTimezoneOffset() * 60000));

  // 使用formatDate函数格式化日期
  return {
    start: `${formatTimes(startOfYear)} 00:00:00`, // 或者使用 startOfYear.toISOString().slice(0, 19).replace('T', ' ')
    end: `${formatTimes(endOfYear)} 23:59:59`, // 或者使用 endOfYear.toISOString().slice(0, 19).replace('T', ' ').replace(/\.\d{3}Z$/, '')
  };
}
export const convertToTimestamp=(dateString:any)=> {
  try {
    const dateObj = new Date(dateString);
    return dateObj.getTime();
  } catch (error) {
    return "Invalid date format";
  }
}