const {REACT_APP_ENV} = process.env;
const outLoginURL = async () => {
  const url = window.location.href;
  // 如果是本地 跳转到对应本地的 登录地址
  if(url.includes('localhost') || url.includes('3002')){
    location.replace('http://localhost:3000/')
  }
  //如果是dev环境 跳转到对应dev 的登录地址
  if(url.includes('dev-web-waybill')){
    location.replace('https://dev-web-home.kmfba.com/login')
  }
  if(REACT_APP_ENV==='pre'){
    location.replace('https://staging-web-home.kmfba.com')
  }
  if(REACT_APP_ENV==='production'){
    location.replace('https://home.kmfba.com')
  }
  //清除本地缓存
  localStorage.clear();
}

export default {
  outLoginURL,
}