const Decimal = require('decimal.js');

/* 每个单位之间来回转换 并解决精度问题 */
export function convert(value: any, fromUnit: string, toUnit: string) {
  if (fromUnit === toUnit) {
    return value;
  } else if (fromUnit === "kg" && toUnit === "lb") {
    return new Decimal(value).times(2.20462).toNumber();
  } else if (fromUnit === "lb" && toUnit === "kg") {
    return new Decimal(value).dividedBy(2.20462).toNumber();
  } else if (fromUnit === "cm" && toUnit === "in") {
    return new Decimal(value).dividedBy(2.54).toNumber();
  } else if (fromUnit === "in" && toUnit === "cm") {
    return new Decimal(value).times(2.54).toNumber();
  } else {
    throw new Error("Unsupported conversion");
  }
}