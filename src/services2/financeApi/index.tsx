import { request } from '@umijs/max';
import { REQUESTADDRESS_W } from '@/globalData';
import Qs from 'qs';

/* 查询应付账单列表 */
export const queryBillsPayableListAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listProviderBills`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 调整账单 */
export const adjustProviderBillAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/adjustProviderBill`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
/* 完成核对 */
export const batchCheckProviderBillsAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/batchCheckProviderBills`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
/* 查询应付核销记录 */
export const queryBillsPayableRecordAPI = async (params: any) => {
  return request(
    `${REQUESTADDRESS_W}/finance/listPaymentRecordDetailsByBillId`,
    {
      method: 'GET',
      params,
    },
  );
};
/*费用确认*/
export const addFeeManually = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/addFeeManually`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
};
/*重算费用*/
export const reFeeManually = (params: any) => {
  const { billId, keyword, ...rest } = params;
  return request(
    `${REQUESTADDRESS_W}/finance/resubmitReconciliation?billId=${billId}`,
    {
      method: 'POST',
      data: { ...rest?.costReconciliationFee },
      requestType: 'json',
    },
  );
};
/*应付账单*/
export const getListProviderBills = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listProviderBills`, {
    method: 'GET',
    params,
  });
};
/*账单详情*/
export const getBillDetailById = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getBillDetailById`, {
    method: 'GET',
    params,
  });
};
/*获取账单成本分摊*/
export const getCostDetailOfBill = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getCostDetailOfBill`, {
    method: 'GET',
    params,
  });
};
/*运单加收详情*/
export const getAdditionBillToWaybillOfBill = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getAdditionBillToWaybillOfBill`, {
    method: 'GET',
    params,
  });
};
/*撤销成本分摊*/
export const cancelCostDividedOfBill = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/cancelCostDividedOfBill`, {
    method: 'GET',
    params,
  });
};

/*修改提单 */
export const updateBillAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/configure/modifyAirSpacePaymentChargeInfo`,
    {
      method: 'POST',
      data,
    },
  );
};

/* 可选服务商列表 */
export const getServiceProviderListAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/provider/getConnectorList`, {
    method: 'GET',
    params,
  });
};

/* 删除备注 */
export const deleteRemarkAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/comment/remove`, {
    method: 'GET',
    params,
  });
};

/* 填写报价单 */
export const addQuotationAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/saveQuotation`, {
    method: 'POST',
    data,
  });
};

/* 查看报价 */
export const getQuotationAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/getQuotationDetail`, {
    method: 'GET',
    params,
  });
};

/* 提单操作服务商 */
export const getWaybillOperationProviderAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/triggerProviderBlnoOperation`, {
    method: 'POST',
    data,
  });
};

/* 打印标签列表 */
export const getPrintLabelListAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/label/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 自定义打印 */
export const printAsLabelAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/printAsLabel`, {
    method: 'get',
    params,
  });
};

/* 新增编辑标签模版 */
export const addPrintLabelTemplateAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/label/saveInvoice`, {
    method: 'POST',
    data,
  });
};

/* 删除标签模版 */
export const deletePrintLabelTemplateAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/label/removeLabel`, {
    method: 'POST',
    data,
  });
};

/* 对账记录列表 */
export const getReconciliationRecordListAPI = async (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/finance/listProviderReconciliationRecord`,
    {
      method: 'post',
      data,
      requestType: 'json',
    },
  );
};

/* 服务商查询 */
export const getServiceProviderAPI = async (params: {
  counterpartyType: string;
}) => {
  return request(`${REQUESTADDRESS_W}/finance/listProviderByType`, {
    method: 'GET',
    params,
  });
};

/* 导出待核对账单 */
export const exportBillsToBeCheckedAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/exportProviderBills`, {
    method: 'POST',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
    // transformRequest: [data => Qs.stringify(data, { indices: false })]
  });
};

/* 下载费用导入模版 */
export const downloadFeeImportTemplateAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/downloadImportTemplate`, {
    method: 'GET',
    params,
    responseType: 'blob',
    getResponse: true,
  });
};

/* 应收下载导入模版 */
export const downloadReceivableImportTemplateAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/importClientBill/download`, {
    method: 'GET',
    params,
    responseType: 'blob',
    getResponse: true,
  });
};

/* 上传对账单 */
export const uploadStatementAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/importReconciliationBill`, {
    method: 'POST',
    data,
  });
};

/* 导入费用 */
export const uploadFeeAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/importProviderBill`, {
    method: 'POST',
    data,
  });
};
export const uploadFeeAPI2 = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/importClientBill`, {
    method: 'POST',
    data,
  });
};

/* 查询对账记录详情 */
export const getReconciliationRecordDetailAPI = async (params: any) => {
  return request(
    `${REQUESTADDRESS_W}/finance/getProviderReconciliationRecordDetail`,
    {
      method: 'GET',
      params,
    },
  );
};

/* 撤销对账记录 */
export const cancelReconciliationRecordAPI = async (data: {
  recordId: string;
}) => {
  return request(
    `${REQUESTADDRESS_W}/finance/revertProviderReconciliationRecord`,
    {
      method: 'post',
      data,
    },
  );
};

/* 费用类型 */
export const getFeeTypeAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listBillNameByCounterpartyType`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 通用添加 */
export const addProviderBillAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/addProviderBill`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 单票费用确认导出 */
export const getDownloadFeesAPI = async (params: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/cabin/resale/order/downloadFees`,
    {
      method: 'get',
      params,
      responseType: 'blob',
      getResponse: true,
    },
  );
};

export const getCabinBlno = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/blno/search`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 查询财务主体详情 */
export const getFinanceSubjectDetailAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getFinanceEntityDetail`, {
    method: 'GET',
    params,
  });
};

/* 修改财务主体 */
export const updateFinanceSubjectAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/saveFinanceEntity`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查询运单汇总 */
export const getWaybillSummaryAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listWaybillSummary`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 批量运单重算 */
export const listReCalculatePriceRecordAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/listReCalculatePriceRecord`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 服务商欠费 */
export const provideDebtrAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/provider/debt`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 运单汇总导出
export const exportWaybillSummaryAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/exportWaybillSummary`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};
/* 查询运单汇总详情 */
export const getWaybillSummaryDetailAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getWaybillBillDetail`, {
    method: 'GET',
    params,
  });
};
/* 提单汇总详情 */
export const getBlnoBillDetailAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getBlnoDetail`, {
    method: 'post',
    data,
  });
};
/* 轨迹维护列表 */
export const getTrajectoryMaintenanceListAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/track/getTrackRecordList`, {
    method: 'GET',
    params,
  });
};
/* 轨迹维护列表 */
export const getModifyTrackRemark = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyTrackRemark`, {
    method: 'post',
    data,
  });
};
/* 轨迹手动抓取 */
export const syncLogisticsTrackAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/track/syncLogisticsTrack`, {
    method: 'post',
    data,
  });
};
/* 修改上网状态 */
export const updateTrackPickedAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/track/updateTrackPicked`, {
    method: 'post',
    data,
  });
};
/* 批量设置轨迹服务商 */
export const modifyShipmentTrackingExpressCodeAPI = async (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/track/modifyShipmentTrackingExpressCode`,
    {
      method: 'post',
      data,
    },
  );
};
/* 删除轨迹维护记录 */
export const deleteTrajectoryMaintenanceRecordAPI = async (data: {
  id: string;
}) => {
  return request(`${REQUESTADDRESS_W}/track/removeTackRecord`, {
    method: 'POST',
    data,
  });
};

/* 财务主体 员工列表 */
export const getFinanceSubjectStaffListAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listFinanceEntityUser`, {
    method: 'GET',
    params,
  });
};

/* 财务主体 添加用户 */
export const addFinanceSubjectStaffAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/addFinanceEntityUsers`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 财务主体 员工删除 */
export const deleteFinanceSubjectStaffAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/deleteFinanceEntityUser`, {
    method: 'GET',
    params,
  });
};

/* 偏远规则列表 */
export const getFarRuleListAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/listRemoteRule`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 提单汇总列表 */
export const getBillOfLadingSummaryListAPI = async (data: any) => {
  // return request(`${REQUESTADDRESS_W}/finance/listBlnoSummary`, {
  return request(`${REQUESTADDRESS_W}/finance/listBlnoSummaryByES`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 更新 isa */
export const updateIsaAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyISA`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};
/* 批量签出 */
export const batchSignOutAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batch/signOut`, {
    method: 'POST',
    data,
    // requestType: 'forms',
    // transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};
/* 导出 POD */
export const exportPODAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/exportPdfISA`, {
    method: 'POST',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 日志记录 */
export const getLogListAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/log/getList`, {
    method: 'GET',
    params,
  });
};

/* 修改收款单录入 */
export const updateReceiptEntryAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/modifyReceipt`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 添加服务商 */
export const addServiceProviderAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/addProvider`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 调整子单 */
export const adjustSubOrdersAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/adjustPiece`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查询费用 */
export const getFeeAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/calAddFee`, {
    method: 'POST',
    data,
  });
};
// 添加杂费
export const saveAddV2 = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/save/add/fee/v2`, {
    method: 'POST',
    data,
  });
};
/* 签入记录列表 */
export const getSignInRecordListAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/listMachineSignRecord`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 少单签入 */
export const signInAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/signWithLackPiece`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 报价申请 */
export const quoteApplicationAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/getPriceOfferList`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 部门 */
export const departmentAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/getDepartementList`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 获取报价单详情 */
export const getQuotationDetail = (params: any) => {
  return request(`${REQUESTADDRESS_W}/truck/getPriceOfferById`, {
    method: 'GET',
    params,
  });
};

/* 驳回 */
export const rejectAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/refusePriceOffer`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 报价 */
export const quoteAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/completePriceOffer`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 立即询价 */
export const fillInInquiryAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/createPriceOffer`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 舱单下载 */
export const downloadWaybillAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getBlnoFileList`, {
    method: 'POST',
    data,
  });
};
export const downloadWaybillAPI2 = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getStuffingDetailList`, {
    method: 'POST',
    data,
  });
};

/* 收款单作废 */
export const invalidReceiptAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/invalidReceipt`, {
    method: 'POST',
    data,
  });
};

/* 批量修改收款日期 */
export const batchModifyReceiptDateAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/batchModifyReceipt`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
export const batchModifyReceiptDate2API = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/batchModifyPaymentRequire`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 模版解析 */
export const templateAnalysisAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/parseCountry`, {
    method: 'POST',
    data,
  });
};

/* 服务标签列表 */
export const serviceTagListAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tag/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 修改服务标签 */
export const modifyServiceTagAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tag/saveTag`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 运单服务标签列表 */
export const waybillTagListAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/queryTags`, {
    method: 'POST',
    data,
  });
};

/* 修改运单服务标签 */
export const modifyWaybillTagAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyTags`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 账单名细服务商 */
export const billDetailAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/provider/getList?type=8`, {
    method: 'get',
    params,
  });
};
/* 账单名细服务商 */
export const billDetailsAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/provider/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 撤销费用添加 */
export const cancelAddAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/cancelAddFee`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
/*导入账单信息*/
export const importWabillList = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/utils/uploadFedexBills`, {
    method: 'POST',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};

/* 保存附加费率 */
export const saveSurchargeAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/fuelFee/insertRecord`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 费率列表 */
export const surchargeListAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/fuelFee/record/list`, {
    method: 'POST',
    data,
  });
};

/* 价格表复制 */
export const copyPriceTableAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/copyZonePrice`, {
    method: 'POST',
    data,
  });
};

/* 价格表审核 */
export const auditPriceTableAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/price/approve`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 批量重算价格 */
export const batchRecalculatePriceAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/reCalculatePrice/batch/v2`, {
    method: 'POST',
    data,
    requestType: 'json',
    // requestType: 'forms',
    // transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 货代打单批量重算价格 */
export const freightForwarderRecalculatePriceAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/reCalculateProviderPrice/batch`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 批量轮询重算价格 */
export const batchPollingRecalculatePriceAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/getBatchResult`, {
    method: 'POST',
    data,
    // requestType: 'json',
    // requestType: 'forms',
    // transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 应收导出 */
export const receivableExportAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/exportClientBills`, {
    method: 'post',
    data,
    responseType: 'blob',
    requestType: 'json',
    getResponse: true,
  });
};

/* 国家分区纠正 */
export const countryPartitionCorrectionAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/address/addCountryPattern`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 应收账单撤销 */
export const cancelClientBillsV2API = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/cancelClientBillsV2`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
/* 取消 */
export const cancelClientBills = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/cancelClientBills`, {
    method: 'POST',
    data,
  });
};

/* 速递日志 */
export const deliveryLogAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/it/getSpOperationLogList`, {
    method: 'GET',
    params,
  });
};

/* 请款单导出 */
export const paymentExportAPI = async (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/finance/getClientRequirePaymentExcelExportList`,
    {
      method: 'post',
      data,
      requestType: 'json',
    },
  );
};

/* 重推优惠 */
export const rePushDiscountAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/weaver/rePushOA`, {
    method: 'POST',
    data,
  });
};

/* 跳过优惠 */
export const skipDiscountAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/it/markSkip`, {
    method: 'POST',
    data,
  });
};

/* 账目类型 */
export const accountTypeAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getReceivableBillNamesByGroup`, {
    method: 'POST',
    data,
  });
};
/* 获货拉拉费用 */
export const listHuoLalaBillDetailAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getHuoLalaBillDetail`, {
    method: 'get',
    params,
  });
};
/* 服务商解绑 */
export const unbindServiceAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/provider/unbindConnector`, {
    method: 'POST',
    data,
  });
};

/* 新增收款认领 */
export const addReceiptClaimAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/createReceiptClaim`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 编辑收款认领 */
export const editReceiptClaimAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/modifyReceiptClaim`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 认领收款 */
export const claimReceiptAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/claimReceipt`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 收款认领审批 */
export const claimReceiptApprovalAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/modifyClaimState`, {
    method: 'POST',
    data,
    // requestType: 'json',
    // requestType: 'forms',
    // transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 导入收款认领 */
export const importReceiptClaimAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/importReceiptClaim`, {
    method: 'POST',
    data,
  });
};
/* 导入出货计费重 */
export const batchImportOuterChargeWeightAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchImportOuterChargeWeight`, {
    method: 'POST',
    data,
  });
};
/* 下载导出模版 */
export const downloadReceiptClaimTemplateAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/file/downloadTemplate`, {
    method: 'GET',
    params,
  });
};
/* 下载导入成本模版 */
export const downloadTemplateAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/file/downloadTemplate`, {
    method: 'GET',
    params,
  });
};
/* 导入成本文件 */
export const importSalesmanCostAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/importSalesmanCost`, {
    method: 'POST',
    data,
  });
};
