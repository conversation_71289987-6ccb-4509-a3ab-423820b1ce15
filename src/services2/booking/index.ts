import { request } from '@umijs/max';
import { REQUESTADDRESS_W, REQUESTADDRESS } from '@/globalData';
import Qs from 'qs';

/**
 *
 *
 * 订舱管理
 */
/* 船位列表 */
export const bookingListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
export const trailerPlanListExport = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/plan/download/v2`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    // requestType: 'json',
  });
};
/*打印装车码*/
export const printLabel = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/getLabel`, {
    method: 'get',
    params,
  });
};
/* 退订舱位 */
export const cancelShippingAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/cancel`, {
    method: 'POST',
    params,
  });
};
/* 统计 改需求了 ，海运 */
export const summaryAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/voyage/cabin/summary/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 统计 改需求了 ，空运 */
export const cabinAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 快捷订舱
export const fastBookAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/plan/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 订舱计划列表 */
export const shippingPlanAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/plan/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 卖柜订单列表 */
export const getCalinetIndent = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/resale/order/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 卖柜订单详情 */
export const getResaleOrderDetail = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/resale/order/detail`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
// 客户订单下载导入模版
export const clientImportTemplateAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/importClientBill/download/v2`, {
    method: 'GET',
    params,
    responseType: 'blob',
    getResponse: true,
  });
};
/* 卖柜订单详情相关文件 */
export const getResaleOrderFile = (data: any) => {
  return request(`${REQUESTADDRESS_W}/file/listRelatedFile`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
/* 卖柜订单详情删除文件· */
export const getResaleOrderDeleteFile = (data: any) => {
  return request(`${REQUESTADDRESS_W}/file/deleteRelatedFile`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
export const uploadReResaleOrder = (data: any) => {
  return request(`${REQUESTADDRESS_W}/file/uploadRelatedFile`, {
    method: 'POST',
    data,
    // requestType: 'json',
    // responseType: 'blob',
  });
};
/* 下载文件 */
export const downloadFileResaleOrderAPI = (params: any) => {
  // console.log('params: ', params);
  return request(`${REQUESTADDRESS_W}${params.url}`, {
    method: 'GET',
    // params,
    responseType: 'blob',
    getResponse: true,
  });
};
// 添加费用
export const getAddFeeAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/cabin/resale/order/addFee/batch`,
    {
      method: 'POST',
      data,
      requestType: 'json',
    },
  );
};
/* 添加应收 */
export const getAddFee2API = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/addClientBill`, {
    method: 'POST',
    data,
  });
};
/* 添加成本 */
export const getAddFee3API = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/addSalesmanCost`, {
    method: 'POST',
    data,
  });
};
// 创建客户订单
export const getCreateOrder = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/resale/order/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 卖柜订单列表 */
export const getCalinetSaveAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/resale/order/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 系统支持的船司代理 */
export const shippingAgencyAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/agent/properties/list`, {
    method: 'get',
    params,
  });
};
/* 船司列表 */
export const shippingListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/provider/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 新增航次
export const voyageListAddAPI = (params: any) => {
  // return request(`${REQUESTADDRESS_W}/transport/voyage/list`, {
  return request(`${REQUESTADDRESS_W}/transport/voyageProperties/list`, {
    method: 'get',
    params,
  });
};
// 航次列表
export const voyageListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/voyage/list`, {
    // return request(`${REQUESTADDRESS_W}/transport/voyageProperties/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 航次编辑
export const getUpdateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/voyage/update`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 航线列表
export const airLineListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/vessel/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 船司代理列表 */
export const shippingAgencyListAPI = (data: any) => {
  // return request(`${REQUESTADDRESS_W}/transport/agent/list`, {
  return request(`${REQUESTADDRESS_W}/provider/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 获取动态业务类型接口
export const listCounterpartyTypeAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listCounterpartyType`, {
    method: 'get',
    params,
    // requestType: 'json',
  });
};
/* 通用添加 分摊方式 */
export const getDivideMethodAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getDivideMethod`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
// 分摊成本操作
export const divideCostAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/divideCost`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
/* 船司代理详情 */
export const shippingParticularsAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/agent/getDetail`, {
    method: 'get',
    params,
  });
};
/* 箱规配置 */
export const shippingCartonAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabinet/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 保存配置 */
export const shippingCabinetAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabinet/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 保存航次 */
export const saveVoyageAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/voyage/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 保存船司配置
export const saveProviderAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/provider/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

// 启用/停用船司代理
export const shippingAgencyEnabledAPI = (data: any) => {
  // return request(`${REQUESTADDRESS_W}/transport/agent/enable`, {
  return request(`${REQUESTADDRESS_W}/provider/enable`, {
    method: 'post',
    data,
  });
};
// 针对船司代理启用/停用指定航线
export const shippingAgencyEnabledAirlineAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/agent/enableVessel`, {
    method: 'post',
    data,
  });
};
// 船司代理新增价格表
export const newlyPriceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/agent/price/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 船司代理删除价格表
export const priceDelete = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/agent/price/delete`, {
    method: 'post',
    data,
  });
};
// 应付类型查费用名目 list
export const getBillnamesByCounterpartyTypeAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getBillNamesByCounterpartyType`, {
    method: 'get',
    params,
  });
};
// 保存船司代理配置信息
export const agentSaveAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/agent/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 船司代理价格表发布
export const agentPriceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/agent/price/publish`, {
    method: 'post',
    data,
  });
};
// 航线属性列表
export const vesselPropertiesAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/vessel/properties/list`, {
    method: 'get',
    params,
  });
};
// 通过航线属性id获取航线数据
export const vesselPropertiesGetDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/vessel/properties/getDetail`, {
    method: 'get',
    params,
  });
};
// 航次统计详情
export const shippingPropertiesGetDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/getDetail`, {
    method: 'get',
    params,
  });
};
// 新建空运
export const transportationAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/air/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
// 修改提单号
export const updateBlnoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/updateBlno`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
// 保存航线
export const vesselSaveAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/vessel/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
// 下载托书
export const authorizationLetterAPI = (params: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/cabin/plan/authorizationLetter/download`,
    {
      method: 'get',
      params,
      responseType: 'blob',
      getResponse: true,
    },
  );
};
// 上传提单号
export const planAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/plan/blno/upload`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 订舱计划创建-舱位预览信息
export const previewAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/preview/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 查订舱计划航线对应的 船司代理与规格
export const getPreviewListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/preview/list`, {
    method: 'get',
    params,
  });
};

// 航次统计详情
export const cabinGetDetailAPI = (params: any) => {
  // /transport/cabin/getDetail
  return request(`${REQUESTADDRESS_W}/transport/voyage/cabin/getDetail`, {
    method: 'get',
    params,
  });
};
// 装柜地列表
export const getWarehouseListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/getWarehouseList`, {
    method: 'get',
    params,
  });
};
// 清除ISA及POD
export const clearIsaAndPodAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/clearIsaAndPod`, {
    method: 'post',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
    // requestType: 'json',
  });
};

// 保存装柜地
export const getWarehouseListCabinetAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/getWarehouse`, {
    method: 'get',
    params,
  });
};
// 保存航线代理映射关系
export const agentBindVesselAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/agent/bindVessel`, {
    method: 'post',
    data,
  });
};
// 价格表的区分查询
export const getCabinetPriceAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/vessel/cabinetPrice/list`, {
    method: 'get',
    params,
  });
};
// 复制价格表
export const getCopyPriceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/agent/price/copy`, {
    method: 'post',
    data,
  });
};
/* 用户列表 */
export const getUserListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/getUserList`, {
    method: 'get',
    params,
  });
};
/* 引用 */
export const copyAuthToUserAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/ram/copyAuthToUser`, {
    method: 'post',
    data,
  });
};

export const modifyPackageTypeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyPackageType`, {
    method: 'post',
    data,
  });
};
// 预报打单修改包裹出货类型
export const modifyOutPackageTypeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyOutPackageType`, {
    method: 'post',
    data,
  });
};
// 上传 SEO 文件
export const soFileUploadAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/plan/soFile/upload`, {
    method: 'post',
    data,
  });
};
// 拖车计划列表-拖车记录列表
export const trailerPlanListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/plan/list/v2`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 拖车代理列表
export const trailerAgentListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/agent/list`, {
    method: 'get',
    params,
  });
};
// 安排拖车的拖车代理，后面改的，
export const getAgentListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/plan/getAgentList`, {
    method: 'get',
    params,
  });
};
// 安排拖车
export const trailerplanSaveAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/plan/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 新增-编辑拖车代理两地服务费
export const agentTwoPlacePriceAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/trailer/agent/twoPlacePrice/save`,
    {
      method: 'post',
      data,
      requestType: 'json',
    },
  );
};
//  删除拖车代理两地服务费
export const agentTwoPlacePriceRemoveAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/trailer/agent/twoPlacePrice/remove`,
    {
      method: 'post',
      data,
    },
  );
};
// 通过id获取临时AccessToken
export const getTemporaryTokeneAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/getTemporaryToken`, {
    method: 'GET',
    params,
    requestType: 'application/x-www-form-urlencoded;charset=UTF-8',
  });
};
// 下载SO文件
export const getDownloadSoFileAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/plan/downloadSoFile`, {
    method: 'GET',
    params,
    responseType: 'blob',
    getResponse: true,
  });
};
// 新增/编辑拖车代理
export const trailerAgentSaveAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/agent/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 新增-编辑拖车代理港杂费
export const agentPortSurchargeAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/trailer/agent/portSurcharge/save`,
    {
      method: 'post',
      data,
      requestType: 'json',
    },
  );
};
//  删除拖车代理港杂费
export const portSurchargeRemoveAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/trailer/agent/portSurcharge/remove`,
    {
      method: 'POST',
      data,
      // requestType: 'application/x-www-form-urlencoded;charset=UTF-8'
    },
  );
};
// 新增-编辑拖车代理服务仓库
export const warehouseSaveAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/agent/warehouse/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 删除拖车代理服务仓库
export const agentWarehouseAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/trailer/agent/warehouse/remove`,
    {
      method: 'post',
      data,
      // requestType: 'application/x-www-form-urlencoded;charset=UTF-8'
    },
  );
};
// 启用停用拖车代理
export const agentEnableAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/provider/enable`, {
    method: 'post',
    data,
  });
};
// 拖车代理详情
export const agentgetDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/agent/getDetail`, {
    method: 'get',
    params,
  });
};
// 拖车记录详情
export const getDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/trailer/plan/getDetail`, {
    method: 'get',
    params,
  });
};
// 拖车计划-完成
export const updateTrailerInfoAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/trailer/plan/updateTrailerInfo`,
    {
      method: 'POST',
      data,
      requestType: 'json',
    },
  );
};
// 获取全部岗位信息
export const getDutyRulesAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/getDutyRules`, {
    method: 'get',
    params,
  });
};
// 获取全部岗位信息（用户管理）
export const getDutyListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/getDutyList`, {
    method: 'get',
    params,
  });
};
// 获取邀请码
export const getCodeAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/createInvitationCode`, {
    method: 'get',
    params,
  });
};
// 同步标签
export const syncTagAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/ram/syncUsrTag`, {
    method: 'post',
    data,
  });
};
// 从指定岗位，添加员工
export const addStaffAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/addStaff`, {
    method: 'get',
    params,
  });
};
// 从指定岗位，删除员工
export const removeStaffAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/removeStaff`, {
    method: 'get',
    params,
  });
};
// 获取 token
export const accessTokenAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/identity`, {
    method: 'get',
    params,
  });
};
// 订舱计划详情
export const dinglkBookingPlanAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/plan/getDetail`, {
    method: 'get',
    params,
  });
};
// 岗位人员修改类型
export const modifyStaffAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/modifyStaffData`, {
    method: 'get',
    params,
  });
};
/***
 * 销售预报
 */
// 发布预报
export const reportAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/report/submit`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 预报列表
export const reportGetListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/report/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 预报明细列表
export const reportDetailListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/report/getDetailList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 销售预报按产品
export const marketStatisticsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/report/getStatics`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 销售预报按航线
export const getStaticsGroupVesselClusterAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/report/getStaticsGroupVesselCluster`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 销售预报统计详情
export const getOverviewAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/report/getOverview`, {
    method: 'get',
    params,
  });
};
// 销售预报统计详情
export const getOverviewDetailsAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/report/getOverview`, {
    method: 'get',
    params,
  });
};
// 预报记录完成
export const getForecastCompleteAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/report/complete`, {
    // return request(`${REQUESTADDRESS_W}/report/noticeArrive`, {
    method: 'get',
    params,
  });
};
// 到货提醒
export const getArrivalAPI = (data: any) => {
  // return request(`${REQUESTADDRESS_W}/report/complete`, {
  return request(`${REQUESTADDRESS_W}/report/noticeArrive`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 预报取消
export const getForecastCancelAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/report/cancel`, {
    method: 'get',
    params,
  });
};
/**
 * 公用的组件接口临时放这里 仓库 /address/getWarehouseListByProviderId
 */
//  获取代理商、客户仓库列表
export const getWarehouseListByProviderIdAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/getWarehouseListByProviderId`, {
    method: 'get',
    params,
  });
};
export const getRemoveWarehouse = (data: any) => {
  return request(`${REQUESTADDRESS_W}/address/removeWarehouse`, {
    method: 'post',
    data,
  });
};
/**
 * 报表接口
 */
// 报表列表
export const getAllInstanceStatistics = (params: any) => {
  return request(`${REQUESTADDRESS_W}/statistics/getAllInstanceStatistics`, {
    method: 'get',
    params,
  });
};
export const getAllInstanceStatisticsFnV2 = (params: any) => {
  return request(`${REQUESTADDRESS_W}/statistics/getAllInstanceStatisticsV2`, {
    method: 'get',
    params,
  });
};
/* 置顶 */
export const setTopAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/statistics/putOnTop`, {
    method: 'post',
    data,
  });
};
/* 取消置顶 */
export const cancelTopAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/statistics/cancelOnTop`, {
    method: 'post',
    data,
  });
};
// 获取添加报表的接口
export const getServiceStatisticsAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/statistics/getServiceStatistics`, {
    method: 'get',
    params,
  });
};
// 报表详情
// export const getstatisticsAPI = (
//   data: any,
//   start: number,
//   len: number,
//   key: string,
// ) => {
//   return request(
//     `${REQUESTADDRESS_W}/statistics/getData?start=${start}&len=${len}&key=${key}`,
//     {
//       method: 'post',
//       data,
//       requestType: 'json',
//     },
//   );
// };
export const getstatisticsAPI = (
  data: any,
  // start: number,
  // len: number,
  // key: string,
) => {
  return request(`${REQUESTADDRESS_W}/statistics/getData`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 添加报表
export const addStatisticsToInstanceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/statistics/addStatisticsToInstance`, {
    method: 'post',
    data,
  });
};
// 查询条件报表接口
export const getInstanceStatisticsAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/statistics/getInstanceStatistics`, {
    method: 'get',
    params,
  });
};
// 删除报表
export const removeStatisticsFromInstanceAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/statistics/removeStatisticsFromInstance`,
    {
      method: 'post',
      data,
    },
  );
};
// 导出数据
export const getDownloadAPI = (data: any, key: string) => {
  return request(`${REQUESTADDRESS_W}/statistics/download?key=${key}`, {
    method: 'post',
    data,
    requestType: 'json',
    responseType: 'blob',
    getResponse: true,
  });
};

/* 获取搜索 */
export const getSearchAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/statistics/getInfo`, {
    method: 'get',
    params,
  });
};

// 服务商类型
export const getProviderType = (data: any) => {
  return request(`${REQUESTADDRESS_W}/provider/getTypeList`, {
    method: 'post',
    data,
  });
};

// 三方平台列表
export const getTripartitePlatform = (params: any) => {
  return request(`${REQUESTADDRESS_W}/config/getListByGroup`, {
    method: 'get',
    params,
  });
};

// 三方平台编辑保存
export const getTripartiteSave = (data: any, key: string) => {
  return request(`${REQUESTADDRESS_W}/config/save?key=${key}`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 新增自定义航线
export const AddVesselProperties = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/vesselProperties/add`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 删除自定义航线
export const DelVesselProperties = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/vesselProperties/delete`, {
    method: 'post',
    data,
  });
};
// 新增自定义航次
export const AddVoyageProperties = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/voyageProperties/add`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 删除自定义航次
export const DelVoyageProperties = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/voyageProperties/delete`, {
    method: 'post',
    data,
  });
};
// 舱位转卖
export const cabinResale = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/resale`, {
    method: 'post',
    data,
  });
};
// 撤销舱位转卖
export const CancelcabinResale = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/cancelResale`, {
    method: 'post',
    data,
  });
};
// 修改航次
export const editVoyage = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/updateVoyage`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*修改拖车信息*/
export const updateTrailerRecord = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/trailer/plan/updateTrailerRecord`,
    {
      method: 'post',
      data,
      requestType: 'json',
    },
  );
};
