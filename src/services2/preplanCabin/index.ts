import { request } from '@umijs/max';
import { REQUESTADDRESS_W, REQUESTADDRESS } from '@/globalData';
// 舱位
export const configureListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getList`, {
    method: 'get',
    params,
  });
};
// 运单
export const waybillListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getWaybillList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 内陆转运 按运单创建列表
export const getWaybillListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/inner/getWaybillList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 配舱详情
export const makingParticularsAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getConfigureDetail`, {
    method: 'get',
    params,
  });
};
// 将配舱添加到运单里
export const getputIntoConfigureAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/putIntoConfigure`, {
    method: 'post',
    data,
  });
};
// 导出配仓单
export const getConfigureExcelAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getConfigureExcel`, {
    method: 'get',
    params,
    responseType: 'blob',
    getResponse: true,
  });
};
// 完成配舱
export const getCompleteAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/configure/complete`, {
    method: 'get',
    params,
  });
};
//  从配仓中移除掉
export const getRemoveFromConfigureAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/removeFromConfigure`, {
    method: 'post',
    data,
  });
};
// // 打印舱单
// export const getConfigureExcelAPI = (params: any) => {
//   return request(`${REQUESTADDRESS_W}/configure/getConfigureExcel `, {
//     method: 'post',
//     params,
//   })
// }
// 签出
export const getsignOutAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/signOut`, {
    method: 'post',
    data,
  });
};
// 退签
export const getCancelSignOutAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/cancelSignOut`, {
    method: 'post',
    data,
  });
};
/* 获取揽货列表 */
export const getWarehouseListAPI = (params: {
  start?: string | number; //
  len?: string | number;
  keyword?: string;
  type: 0 | 1; //0:仓库 1:FBM仓
}) => {
  return request(`${REQUESTADDRESS_W}/address/getWarehouseList`, {
    method: 'get',
    params,
  });
};

/*获取运单列表*/
export const waybillListAPIList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tmsList`, {
    method: 'post',
    data,
  });
};
/*获取运单详情*/
export const waybillDetail = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tmsDetail`, {
    method: 'get',
    params,
  });
};
/*发票默认列表*/
export const getBoxDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/getBoxDetail`, {
    method: 'get',
    params,
  });
};
// 重新试算
export const modifyBoxDetailAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyBoxDetail`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 完成出库
export const getcompleteSignOutAPI = (params: any, errorFilter: Function) => {
  return request(`${REQUESTADDRESS_W}/configure/completeSignOut`, {
    method: 'get',
    params,
    errorFilter: errorFilter,
  });
};

/**
 *
 * 客户管理的 规则配置
 */
// 规则配置列表
export const getRuleListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/order/getRuleList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 系统配置数据
export const getServiceConditionListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/order/getServiceConditionList`, {
    method: 'get',
    params,
  });
};
// 规则配置保存
export const getSaveRuleAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/order/saveRule`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 规则配置列表启用停用
export const getDisableAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/order/disable`, {
    method: 'get',
    params,
  });
};
// 规则配置详情
export const getRuleAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/order/getRule`, {
    method: 'get',
    params,
  });
};
/*获取预配仓列表*/
export const getNewPreplanCabin = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getTempList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*修改预配仓名字*/
export const modifyConfigureNameAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/modifyConfigureName`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
/*新增预配仓*/
export const AddNewPreplanCabin = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/createTempConfigure`, {
    method: 'post',
    data,
  });
};
/*绑定提单*/
export const bindingCabin = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/bindSpaceToConfigure`, {
    method: 'post',
    data,
  });
};
/*删除*/
export const removeTempConfigure = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/removeTempConfigure`, {
    method: 'post',
    data,
  });
};
/*新舱位*/
export const newConfigureListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getList`, {
    method: 'post',
    requestType: 'json',
    data,
  });
};
/*导出配舱单*/
export const downloadConfigureDetailAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getConfigureDetailFiles`, {
    method: 'post',
    // requestType: 'json',
    data,
  });
};

/*交换舱位*/
export const configureExchangeAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/configure/exchange`, {
    method: 'get',
    // requestType: 'json',
    params,
  });
};
// 配舱装柜地
export const getStuffingWarehouseListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/address/getStuffingWarehouseList`, {
    method: 'post',
    requestType: 'json',
    data,
  });
};
