import { request } from '@umijs/max';
import { REQUESTADDRESS, REQUESTADDRESS_W } from '@/globalData';
/* 获取用户登录token  */

/* 账号信息 */
/* 获取用户信息 */
export const getCustomerListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/group/list`, {
    method: 'GET',
    params,
  });
};
/* 获取标签列表 */
export const getLabelListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/tag/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* Tume 订单 */
export const listTemuOrderAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/listTemuOrder`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* Tume 订单 */
export const temuOrderDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/temuOrderDetail`, {
    method: 'get',
    params,
    // requestType: 'json',
  });
};
/* Tume 接单 */
export const acceptTemuOrderAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/acceptTemuOrder`, {
    method: 'post',
    data,
  });
};
/* Tume 拒绝 */
export const refuseTemuOrderAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/refuseTemuOrder`, {
    method: 'post',
    data,
  });
};
/* 获取标签列表 */
export const getOldLabelListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/tag/list`, {
    method: 'get',
    params,
  });
};

/* 创建标签 */
export const addLabelAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/tag/save`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/* 删除标签 */
export const delLabelAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/tag/delete`, {
    method: 'post',
    data: params,
  });
};
/* 删除标签 */
export const editLabelAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/tag/delete`, {
    method: 'post',
    data: params,
  });
};

/*创建客户分组*/
export const addCustomGroupAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/group/save`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};

/*查询客户分组*/
export const getCustomGroupAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/group/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*删除客户*/
export const delGroupAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/group/delete`, {
    method: 'post',
    data: params,
  });
};
/*客户详情*/
export const getCustomDetailsAPI = (params: any) => {
  console.log(params);
  return request(`${REQUESTADDRESS_W}/client/group/detail`, {
    method: 'get',
    params,
  });
};
/*客户详情*/
export const setCustomLabelAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/setTag`, {
    method: 'post',
    data: params,
  });
};
/*获取清关列表*/
export const getClearanceList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/clearance/list`, {
    method: 'post',
    data: params,
  });
};
/*获取报关列表*/
export const getDeclarationList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/list`, {
    method: 'post',
    data: params,
  });
};
/*获取清关报关行列表 1-清关行；2-报关行*/
export const getBrokerList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/provider/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*下载文件*/
export const downloadAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/file/download`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    // requestType: 'json',
  });
};
/*报关行修改状态*/
export const editBrokerState = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/broker/changeEnabled`, {
    method: 'post',
    data: params,
  });
};
/*清提拆派取消*/
export const wmsBlnoCancel = (params: any) => {
  return request(`${REQUESTADDRESS_W}/configure/wms/blno/cancel`, {
    method: 'post',
    data: params,
  });
};
/*设置客户分组*/
export const setCustomGroup = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/setClientGroup`, {
    method: 'post',
    data: params,
  });
};
/*直接登录*/
export const DirectLogin = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/loginAsOMSClient`, {
    method: 'get',
    params,
  });
};
/*删除用户*/
export const deleteAccountApi = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/removeUserFromClient`, {
    method: 'get',
    params,
  });
};
/*添加用户和手机号*/
export const addPhoneToClientAsUserApi = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/addPhoneToClientAsUser`, {
    method: 'get',
    params,
  });
};
/*批量添加标签*/
export const batchAddTagsApi = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/batchAddTags`, {
    method: 'post',
    data: params,
  });
};
/*批量删除标签*/
export const batchDeleteTagsApi = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/batchDeleteTags`, {
    method: 'post',
    data: params,
  });
};
