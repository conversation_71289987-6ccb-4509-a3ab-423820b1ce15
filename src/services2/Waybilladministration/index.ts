import { request } from '@umijs/max';
import { REQUESTADDRESS_W, REQUESTADDRESS } from '@/globalData';
import Qs from 'qs';

/*获取单个运单费用详情*/
export const getWaybill = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/listWaybillFee`, {
    method: 'GET',
    params,
  });
};
/*价格重算*/
export const reCalculatePrice = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/reCalculatePrice`, {
    method: 'POST',
    params,
  });
};
/*内部试算列表*/
export const getInnerList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tmsTestPrice`, {
    method: 'POST',
    requestType: 'json',
    data,
  });
};
/* 成本试算 */
export const getCostListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/channelTestPrice`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*价格试算导出*/
export const tmsTestPriceExport = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tmsTestPrice/export`, {
    method: 'post',
    data,
    responseType: 'blob',
    requestType: 'json',
    getResponse: true,
  });
};
/*退件*/
export const ReturnsCommodity = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/refund`, {
    method: 'POST',
    data,
  });
};

/*撤销退件*/
export const RevertReturnsCommodity = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/revertRefund`, {
    method: 'POST',
    data,
  });
};
/*取消运单*/
export const CancelWaybill = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/cancelWaybill`, {
    method: 'POST',
    data,
  });
};
/*修改客户单号*/
export const editWaybill = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyOuterOrderId`, {
    method: 'POST',
    data,
  });
};
/*修改运单标签*/
export const editWaybillLabel = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyGoodsType`, {
    method: 'POST',
    data,
  });
};
/*修改运单号*/
export const editWaybillNo = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyWaybillNo`, {
    method: 'POST',
    data,
  });
};
/*下单可用产品*/
export const getAvailProductList = (data: any) => {
  const { hostId, ...other } = data;
  return request(
    `${REQUESTADDRESS_W}/product/listAvailableProducts?hostId=${hostId}`,
    {
      method: 'POST',
      data: other,
      requestType: 'json',
    },
  );
};
/*修改销售产品*/
export const editProductApi = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyProduct`, {
    method: 'POST',
    data,
  });
};
/*修改服务*/
export const modifyService = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyService`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/*导出运单体积*/
export const exportVolume = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/exportVolume`, {
    method: 'POST',
    responseType: 'blob',
    getResponse: true,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
    data,
  });
};
/*查询选择的费用详情*/
export const queryServiceInfo = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/queryServiceInfo`, {
    method: 'GET',
    params,
  });
};
/* 导出销售预报记录 */
export const exportMarketForecastRecord = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/report/download`, {
    method: 'post',
    data,
    responseType: 'blob',
    requestType: 'json',
    getResponse: true,
  });
};
/*运单同步接口*/
export const SyncWaybill = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/listWaybillSync`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 提单数据对比
export const getCompareBlnoDetailListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getCompareBlnoDetailList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*运单同步忽略接口*/
export const ignoreWaybill = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/ignoreCheckSync`, {
    method: 'POST',
    data,
  });
};
/*运单同步忽略接口*/
export const rePushBeforeSignIn = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/rePushBeforeSignIn`, {
    method: 'POST',
    data,
  });
};
/*运单同步接口*/
export const rePushWaybill = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/rePushWaybill`, {
    method: 'POST',
    data,
  });
};
/*流程编号推送*/
export const syncOaWorkflowAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/it/syncOaWorkflow`, {
    method: 'POST',
    data,
  });
};
//  应付重新分摊
export const batchResubmitReconciliationAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/batchResubmitReconciliation`, {
    method: 'POST',
    data,
  });
};

/* 运单费用试算 */
export const waybillCostCalculationAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchReCalculatePriceAndExport`, {
    method: 'POST',
    data,
    responseType: 'blob',
    getResponse: true,
  });
};
/* 推送ShipTrack */
export const reloadPushMappingAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/track/reloadPushMapping`, {
    method: 'POST',
    data,
  });
};
/* 更新运单用户标签 */
export const refreshUserTagsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tag/refreshUserTags`, {
    method: 'POST',
    data,
  });
};
/* 更新收款单业务员机构 */
export const refreshReceiptTagIdsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/refreshReceiptTagIds`, {
    method: 'POST',
    data,
  });
};
/*运单对比列表接口*/
export const MonitoringList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/listCompareData`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*运单对比接口*/
export const CompareDataApi = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/reCompare`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/*忽略对比*/
export const ignoreCompare = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/ignoreCompareData`, {
    method: 'POST',
    data,
    //requestType: 'json',
  });
};
/* 同步地址 */
export const rePushWaybillAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/rePushWaybill`, {
    method: 'POST',
    data,
  });
};
/*异常监控价格重算*/
export const CompareDataRecount = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchReCalculatePrice`, {
    method: 'POST',
    data,
    //requestType: 'forms',
    //transformRequest: [(data) => Qs.stringify(data, { indices: false })],
    requestType: 'json',
  });
};
/*异常监控重算体积*/
export const updateVolumeNew = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/updateVolumeNew`, {
    method: 'POST',
    data,
    //requestType: 'forms',
    //transformRequest: [(data) => Qs.stringify(data, { indices: false })],
    requestType: 'json',
  });
};
/*异常监控到货时间重算*/
export const batchUpdateArriveTime = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchUpdateArriveTime`, {
    method: 'POST',
    data,
    //requestType: 'forms',
    //transformRequest: [(data) => Qs.stringify(data, { indices: false })],
    requestType: 'json',
  });
};
/*运单同步接口*/
export const syncCustom = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/rePushFromSP`, {
    method: 'POST',
    data,
  });
};
/*同步到速递接口*/
export const rePushSudiWaybill = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/pushWaybillToSudi`, {
    method: 'POST',
    data,
  });
};
/*价格对比列表*/
export const CostComparisonList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/it/getReceivableComparison`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*费用手动对比*/
export const feeRePushWaybill = (data: any) => {
  return request(`${REQUESTADDRESS_W}/it/repushReceivable`, {
    method: 'POST',
    data,
  });
};
/*费用全部对比*/

export const feeAllRePushWaybill = (data: any) => {
  return request(`${REQUESTADDRESS_W}/it/reCompare`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/*费用忽略对比*/
export const feeIgnoreCompare = (data: any) => {
  return request(`${REQUESTADDRESS_W}/it/ignoreCompareData`, {
    method: 'POST',
    data,
    //requestType: 'json',
  });
};
/*获取费用对比详情*/
export const getFeeDetail = (data: any) => {
  return request(`${REQUESTADDRESS_W}/it/getReceivableComparisonDetail`, {
    method: 'POST',
    data,
    //requestType: 'json',
  });
};
