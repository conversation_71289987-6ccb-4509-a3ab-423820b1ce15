import { request } from '@umijs/max';
import { REQUESTADDRESS_W } from '@/globalData';

/* 清关报关保存*/
export const SaveClearGroup = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/broker/save`, {
    method: 'POST',
    params,
    requestType: 'json',
  });
};
/*清关或者报关行列表*/
export const GetCustomGroup = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/broker/list`, {
    method: 'GET',
    params,
  });
};
/*报关行费用更改状态*/
export const changeCustomGroupFee = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/broker/changeEnabled`, {
    method: 'POST',
    params,
    requestType: 'json',
  });
};

/*报关行详情*/
export const GetCustomGroupDetail = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/broker/getDetail`, {
    method: 'GET',
    params,
    requestType: 'json',
  });
};

/*报关单列表*/
export const GetDeclarationList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/list`, {
    method: 'GET',
    params,
  });
};
/*报关单详情*/
export const GetDeclarationDetail = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/getDetail`, {
    method: 'GET',
    params,
  });
};
/*报关单更新*/
export const UploadDeclarationDetail = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/update`, {
    method: 'POST',
    params,
  });
};
/*准备文件审批*/
export const ReadyFileApprove = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/prepareFile/audit`, {
    method: 'POST',
    params,
  });
};
/*添加备注*/
export const addRemark = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/addRemark`, {
    method: 'POST',
    params,
  });
};
/*增加费用列表展示*/
export const getFeePreViews = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/getFeePreViews`, {
    method: 'GET',
    params,
  });
};
/*报关单费用确认*/
export const SaveCustomFee = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/confirmFee`, {
    method: 'GET',
    params,
  });
};
/*获取报关单详情*/
export const getBrokerDetail = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/broker/getDetail`, {
    method: 'GET',
    params,
  });
};
/*获取清关单详情*/
export const getClearanceDetail = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/clearance/getDetail`, {
    method: 'GET',
    params,
  });
};
/*保存报关行详情*/
export const saveBrokerDetail = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/broker/save`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
};

/*删除清关报关附件*/
export const delCustomFile = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/broker/deleteReferenceFiles`, {
    method: 'POST',
    data: params,
  });
};
/*报关服务地及费用*/
export const changeServiceFee = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/broker/vessel/changeEnabled`, {
    method: 'POST',
    data: params,
  });
};
/*获取清关列表*/
export const getClearanceList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/customs/clearance/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/*获取报关列表*/
export const getDeclarationList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/list`, {
    method: 'POST',
    requestType: 'json',
    data,
  });
};

// 合并报关
export const getDeclarationAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/combine`, {
    method: 'POST',
    // requestType: 'json',
    data,
  });
};
// 批量下载报关文件
export const getDownloadAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/customs/declaration/downloadReceiptZipFiles`,
    {
      method: 'post',
      data,
      responseType: 'blob',
      getResponse: true,
    },
  );
};

// 报关资料提交通知
export const notifySubmitPrepareFileAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/customs/declaration/notifySubmitPrepareFile`,
    {
      method: 'post',
      data,
      // responseType: 'blob',
      // getResponse: true,
    },
  );
};
// 修改报关状态
export const updateStateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/updateState`, {
    method: 'post',
    data,
    // responseType: 'blob',
    // getResponse: true,
  });
};
// 修改清关行
export const updateBrokerAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/customs/clearance/updateBroker`, {
    method: 'post',
    data,
    // responseType: 'blob',
    // getResponse: true,
  });
};
// 批量修改服务商
export const batchUpdateProviderAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/batchUpdateProvider`, {
    method: 'post',
    data,
    // responseType: 'blob',
    // getResponse: true,
  });
};
// 客户订单批量修改服务商
export const orderbatchUpdateProviderAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/transport/cabin/resale/order/batchUpdateProvider`,
    {
      method: 'post',
      data,
      // responseType: 'blob',
      // getResponse: true,
    },
  );
};
/*获取轨迹列表*/
export const getTrackList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/track/getList`, {
    method: 'POST',
    data: params,
  });
};
/*清关单更新*/
export const SaveCustom = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/clearance/update`, {
    method: 'POST',
    requestType: 'json',
    data: params,
  });
};
/*报关单更新*/
export const SaveDeclaration = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/update`, {
    method: 'POST',
    requestType: 'json',
    data: params,
  });
};
/* 报关单删除运单 */
export const removeWaybillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/removeWaybill`, {
    method: 'POST',
    // requestType: 'json',
    data,
  });
};
/*清关回传文件删除*/
export const delClearanceReceiptFile = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/clearance/receiptFile/delete`, {
    method: 'POST',
    data: params,
  });
};

/*报关 回传文件 删除*/
export const delReceiptFileFile = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/receiptFile/delete`, {
    method: 'POST',
    data: params,
  });
};
/*报关 准备文件*/
export const delPrepareFileFile = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/prepareFile/delete`, {
    method: 'POST',
    data: params,
  });
};

/*成本分摊费用列表展示20.4*/
export const showFeePreViews = (params: any) => {
  return request(
    `${REQUESTADDRESS_W}/finance/costReconciliation/getFeePreViews`,
    {
      method: 'get',
      params,
    },
  );
};
/*运单加收费用列表展示20.3*/
export const showAddFeePreViews = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/waybill/getFeePreViews`, {
    method: 'get',
    params,
  });
};
/*报关费用列表展示20.4*/
export const showCleanFeePreViews = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/clearance/getFeePreViews`, {
    method: 'get',
    params,
  });
};

/*上传添加费用附件*/
export const UploadFeeFile = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/bill/uploadAttachmentFiles`, {
    method: 'POST',
    data,
  });
};
/*清关预计费用展示*/
export const GetClearanceFee = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/clearance/getFee`, {
    method: 'get',
    params,
  });
};
/*预计费用展示*/
export const GetCustomFee = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getPreviewFee`, {
    method: 'get',
    params,
  });
};

/*清关汇率展示*/
export const GetCustomRate = (params: any) => {
  return request(`${REQUESTADDRESS_W}/rate/queryExchangeRate`, {
    method: 'get',
    params,
  });
};
/*清关汇率展示*/
export const getPreviewBill = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getPreviewBill`, {
    method: 'get',
    params,
  });
};
/*批量修改报关行*/
export const updateBroker = (params: any) => {
  return request(`${REQUESTADDRESS_W}/customs/declaration/updateBroker`, {
    method: 'POST',
    params,
  });
};

/* 运单签入 附加费撤销 */
export const cancelAddFee = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/cancelAddFee`, {
    method: 'POST',
    params,
  });
};
