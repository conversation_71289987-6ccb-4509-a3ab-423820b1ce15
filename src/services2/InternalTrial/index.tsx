import { request } from '@umijs/max';
import { REQUESTADDRESS_W } from '@/globalData';

/*首页获取收货仓库列表*/
export const getWarehouseList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/getWarehouseList`, {
    method: 'get',
    params,
  });
};
/*查询首页目的地*/
export const getDestList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/dest/search`, {
    method: 'GET',
    params,
  });
};
/*查询最新航次*/

export const getNewVoyage = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/product/voyage/list`, {
    method: 'GET',
    params,
  });
};
