import { request } from '@umijs/max';
import { REQUESTADDRESS_W } from '@/globalData';
export const getWaybillInfoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/instance/getWaybillInfo`, {
    method: 'post',
    data,
  });
};
export const getWarehouseInfoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/instance/getWarehouseInfo`, {
    method: 'post',
    data,
  });
};
export const getSpaceInfoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/instance/getSpaceInfo`, {
    method: 'post',
    data,
  });
};
export const getOceanStatisticsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/instance/getOceanStatistics`, {
    method: 'post',
    data,
  });
};
export const getSalesRankingInfoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/instance/getSalesRankingInfo`, {
    method: 'post',
    data,
  });
};
// 部门收货计费重统计信息
export const getSalesSignInChargeWeightTopNAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/instance/getSalesSignInChargeWeightTopN`,
    {
      method: 'post',
      data,
    },
  );
};
// 应收应付
export const getBillInfoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/instance/getBillInfo`, {
    method: 'post',
    data,
  });
};
// 应收应付
export const getWaybillProblemInfoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/instance/getWaybillProblemInfo`, {
    method: 'post',
    data,
  });
};
// 海外仓预报
export const getOverseasAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/wms/blno/createOrder`, {
    method: 'post',
    data,
  });
};
// 清提拆派列表
export const getBlnoList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/wms/blno/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 清提拆派列表详情
export const getDetailAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/wms/blno/getDetail`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
// 清提派详情运单明细
export const getWaybillListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/blno/getWaybillList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 试算记录
export const listChannelOrderRecordAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/listTestPriceRecord`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 一件代发入库单
export const inboundGetListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/inbound/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 一件代发入库单预报详情
export const inboundGetDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/inbound/getDetail`, {
    method: 'get',
    params,
    // requestType: 'json',
  });
};
// 比价并预报
export const createProviderOrderAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/inbound/createProviderOrder`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
// SKU库存流水
export const getSummarizedGoodLogListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/good/getSummarizedGoodLogList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// SKU批次库存
export const getBatchGoodListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/good/getBatchGoodList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// SKU汇总库存
export const getSummarizedListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/good/getSummarizedList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// SKU库存查询
export const getBatchSlotGoodListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/good/getBatchSlotGoodList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 暂存货物入库单列表
export const cargoGetListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/warehouse/cargo/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 暂存货物入库单 新建
export const cargoCreateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/warehouse/cargo/create`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 暂存货物入库单 打印箱唛
export const getPrintLabelAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/warehouse/cargo/getPrintLabel`, {
    method: 'get',
    params,

    // requestType: 'json',
  });
};
