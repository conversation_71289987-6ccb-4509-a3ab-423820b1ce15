// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';
import { REQUESTADDRESS, REQUESTADDRESS_W } from '@/globalData';
import Qs from 'qs';
/* 附加费列表 */
export const getSurchargeList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/prodAddFee/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 保存产品附加费 */
export const saveSurcharge = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/prodAddFee/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 删除产品附加费 */
export const deleteSurcharge = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/prodAddFee/delete`, {
    method: 'POST',
    data,
  });
};
/* 复制附加费 */
export const prodAddFeeCopy = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/prodAddFee/copy`, {
    method: 'POST',
    data,
  });
};
/* 查询产品附加费详情 */
export const getSurchargeDetail = (params: {
  id?: string;
  globalFlag?: boolean;
}) => {
  return request(`${REQUESTADDRESS_W}/template/prodAddFee/detail`, {
    method: 'GET',
    params,
  });
};

/* 查询服务费列表 */
export const getServiceFeeList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/serviceFee/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查询服务费详情 */
export const getServiceFeeDetail = (params: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/template/serviceFee/detail`, {
    method: 'GET',
    params,
  });
};

/* 保存服务费 */
export const saveServiceFee = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/serviceFee/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 删除服务费模版 */
export const deleteServiceFee = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/serviceFee/delete`, {
    method: 'POST',
    data,
  });
};

/* 查询燃油费列表 */
export const getFuelFeeList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/fuelFee/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查询燃油附加费详情 */
export const getFuelFeeDetail = (params: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/template/fuelFee/detail`, {
    method: 'GET',
    params,
  });
};

/* 保存燃油附加费 */
export const saveFuelFee = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/fuelFee/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 删除 燃油附加费 */
export const deleteFuelFee = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/fuelFee/delete`, {
    method: 'POST',
    data,
  });
};

/* 查询价格表 */
export const getPriceList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/price/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 查询产品渠道列表 */
export const getProductChannelList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/product/channel/list`, {
    method: 'GET',
    params,
  });
};
export const getProductChannelList2 = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/channel/listv2`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
// 批量试算
export const getPriceBatchAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/getPrice/batch`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
/* 创建产品 */
export const copyToProductAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/channel/copyToProduct`, {
    method: 'post',
    data,
  });
};
/* 查询产品渠道关联信息 */
export const getProductChannelDetail = (params: any) => {
  return request(`${REQUESTADDRESS_W}/product/channel/item/list`, {
    method: 'GET',
    params,
  });
};

/* 查询自定义规则列表 */
export const getCustomRuleList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/charge/weight/rule/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 保存计费重规则 */
export const saveCustomRule = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/charge/weight/rule/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 删除计费重规则 */
export const deleteCustomRule = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/charge/weight/rule/delete`, {
    method: 'POST',
    data,
  });
};

/* 计费规则/计费重进位规则 详情 */
export const getCustomRuleDetail = (params: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/template/charge/weight/rule/detail`, {
    method: 'GET',
    params,
  });
};

/* 获取国家列表 */
export const getCountryList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/getCountryList`, {
    method: 'GET',
    params,
  });
};

/* 保存产品渠道信息  渠道关联基本信息保存 */
export const saveProductChannel = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/channel/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 保存产品渠道关联信息 */
export const saveProductChannelItem = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/channel/item/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 渠道修改状态 */
export const channelModificationStatusAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/channel/item/state/modify`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 删除产品渠道 */
export const deleteProductChannel = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/channel/delete`, {
    method: 'POST',
    data,
  });
};

/* 删除代理渠道价格表 */
export const deleteAgentChannelPrice = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/channel/item/delete`, {
    method: 'POST',
    data,
  });
};

/* 查询产品渠道详情 */
export const getProductChannelDetailById = (params: {
  id: any; //产品渠道id
}) => {
  return request(`${REQUESTADDRESS_W}/product/channel/detail`, {
    method: 'GET',
    params,
  });
};

/* 删除产品关联信息 */
export const deleteProductChannelItem = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/item/delete`, {
    method: 'POST',
    data,
  });
};

/* 保存分区价格表 */
export const savePriceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/save/zone/price`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查询分区价格表详情 */
export const getPriceDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/template/zone/price/detail`, {
    method: 'GET',
    params,
  });
};

/* 保存增量价格表 */
export const saveIncrementPriceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/incr/zone/price/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 批量修改销售产品 */

export const batchModifyProductAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchModifyProduct`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
/* 产品管理 列表 */
export const getProductList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 修改轨迹更新方式*/
export const changeTrackMethodAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/changeTrackMethod`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 保存产品基本信息 */
export const saveProduct = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 批量修改标签 */
export const batchModifyProductLabelAPI = (data: {
  alias: string;
  productIds: string[];
}) => {
  return request(`${REQUESTADDRESS_W}/product/batchSetAlias`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查询产品 关联信息 */
export const getProductItem = (params: any) => {
  return request(`${REQUESTADDRESS_W}/product/item/list`, {
    method: 'GET',
    params,
  });
};

/* 航线列表接口 */
export const getAirLineList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/transport/vessel/list`, {
    method: 'GET',
    params,
  });
};

/* 查询产品详情 */
export const getProductDetail = (params: any) => {
  return request(`${REQUESTADDRESS_W}/product/detail`, {
    method: 'GET',
    params,
  });
};

/* 保存产品关联信息 */
export const saveProductItem = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/item/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 签出渠道设置默认 */
export const setDefaultPeerLineChannelAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/item/setDefault`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 删除产品关联信息 */
export const deleteProductItem = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/item/delete`, {
    method: 'POST',
    data,
  });
};

/* id编码规则 */
export const getIDCodeRule = (params: any) => {
  return request(`${REQUESTADDRESS_W}/id/getList`, {
    method: 'GET',
    params,
  });
};

/* 超长超重扣件规则 */
export const getOverLengthRule = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/custom/rule/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 创建产品 */
/* 修改产品关联信息状态 */
export const updateProductItemStatus = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/item/state/modify`, {
    method: 'POST',
    data,
  });
};

/* 更换产品关联信息 */
export const changeProductItem = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/item/change`, {
    method: 'POST',
    data,
  });
};

/* 查询到货总单列表 */
export const getArrivalList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/arrive/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 到货总单导出 */
export const arriveExportAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/arrive/list/export`, {
    method: 'post',
    data,
    responseType: 'blob',
    requestType: 'json',
    getResponse: true,
  });
};
/* 到货总单转移到已有的   */
export const transferWaybillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/arrive/transferWaybill`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 设置司机 */
export const setDriverAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/arrive/setDriver`, {
    method: 'get',
    params,
    // requestType: 'json',
  });
};
/* 保存到货总单 */
export const saveArrival = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/arrive/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 操作到货总单 */
export const operateArrival = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/arrive/operate`, {
    method: 'POST',
    data,
  });
};
/* 批量操作到货总单 */
export const batchOperateArrival = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/arrive/batch/operate`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 查询到货总单 */
export const getArrivalDetail = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/arrive/detail`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 查询运单签入列表 */
export const getWaybillSignInList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tmsListV2`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 查询运单列表统计信息 */
export const getWaybillListCount = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/list/summary`, {
    method: 'GET',
    params,
  });
};

/* 查询运单的子运单信息 */
export const getWaybillSubListApi = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/piece/list`, {
    method: 'GET',
    params,
  });
};

/* 运单签入 手动签入 */
export const manualSignInApi = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/manual/sign`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 计算运材积信息 */
export const calculateVolumeApi = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/calWaybillChargeWeight`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 批量操作 扣件 放行 */
export const batchOperateWaybillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batch/operate`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 查询不可见岗位 */
export const getInvisiblePostAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/getDuties`, {
    method: 'GET',
    params,
  });
};

/* 修改收货计费重 */
export const modifyChargeWeightAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyChargeWeight`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 修改出货计费重 */
export const modifyOuterChargeWeightAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyOuterChargeWeight`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 修改体积 */
export const changeVolumeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyVolumeFromSp`, {
    method: 'POST',
    data,
  });
};
/* 重算 */
export const reCalculatePriceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/reCalculatePrice/batch`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 获取备注列表 */
export const getRemarkListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/comment/getList`, {
    method: 'GET',
    params,
  });
};

/* 添加备注 */
export const addRemarkAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/comment/add`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 备注置顶 */
export const remarkTopAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/comment/putOnTop`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 查询签入设备列表 */
export const getSignInDeviceListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/sign/machine/list`, {
    method: 'GET',
    params,
  });
};

/* 保存签入设备 */
export const saveSignInDeviceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/sign/machine/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 删除签入设备 */
export const deleteSignInDeviceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/sign/machine/delete`, {
    method: 'POST',
    data,
  });
};

/* 模拟签入 */
export const simulateSignInAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/testWebsocket/${params}`, {
    method: 'GET',
    // params,
  });
};

/* 操作员添加附加费 */
export const addAdditionalChargeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/save/add/fee`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
// 批量添加附加费
export const batchSaveAddFeeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchSaveAddFee`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 查询额外系数 */
export const getExtraCoefficientAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/calAddFee`, {
    method: 'GET',
    params,
  });
};

/* 查询运单的费用列表 */
export const getWaybillChargeListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/add/fee/list`, {
    method: 'GET',
    params,
  });
};

/* 运单确认 */
export const confirmWaybillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/confirm`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 修改收货材积信息 */
export const modifyVolumeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyVolume`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 暂存 */
export const modifyVolumeAPI2 = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyVolumeBeforeSignIn`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查询分票规则列表 */
export const getSplitRuleListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/split/rule/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 保存分票规则 */
export const saveSplitRuleAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/split/rule/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 删除分票规则 */
export const deleteSplitRuleAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/split/rule/delete`, {
    method: 'POST',
    data,
  });
};

/* 查询重量传输规则列表 */
export const getWeightRuleListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/weight/transfer/rule/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 保存重量传输规则 */
export const saveWeightRuleAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/weight/transfer/rule/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查询重量传输规则详情 */
export const getWeightRuleDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/template/weight/transfer/rule/detail`, {
    method: 'GET',
    params,
  });
};

/* 查询提单列表 */
export const getWaybillListByBillNoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getBlnoList`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
export const getWaybillLiInfoListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/wms/waybill/getInfoList`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
// 运单后端导出
export const exportInfoList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/wms/waybill/exportInfoList`, {
    method: 'post',
    data,
    responseType: 'blob',
    requestType: 'json',
    getResponse: true,
  });
};
/* 获取提单列表详情 */
export const getWaybillListDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getConfigureDetail`, {
    method: 'GET',
    params,
  });
};

/* 获取相关文件列表 */
export const getRelatedFileListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getFileList`, {
    method: 'GET',
    params,
  });
};
/* 获取相关文件列表2 */
export const getRelatedFileListAPI2 = (params: any) => {
  return request(`${REQUESTADDRESS_W}/provider/getFileList`, {
    method: 'GET',
    params,
  });
};

/* 上传参考文件 */
export const uploadReferenceFileAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/upload`, {
    method: 'POST',
    data,
    // requestType: 'json',
    // responseType: 'blob',
  });
};
/* 编辑文件 */
export const saveDraftFileInfoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/saveDraftFileInfo`, {
    method: 'POST',
    data,
    requestType: 'json',
    // responseType: 'blob',
  });
};
/* 上传参考文件2 */
export const uploadReferenceFileAPI2 = (data: any) => {
  return request(`${REQUESTADDRESS_W}/provider/uploadFile`, {
    method: 'POST',
    data,
    // requestType: 'json',
    // responseType: 'blob',
  });
};

/* 删除相关文件 */
export const deleteRelatedFileAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/removeConfigureFile`, {
    method: 'POST',
    data,
  });
};
/* 删除相关文件2 */
export const deleteRelatedFileAPI2 = (data: any) => {
  return request(`${REQUESTADDRESS_W}/provider/removeFile`, {
    method: 'POST',
    data,
  });
};

/* 下载文件 */
export const downloadFileAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}${params.url}`, {
    method: 'GET',
    // params,
    responseType: 'blob',
    getResponse: true,
  });
};
/* 下载文件 */
export const downloadFileAPI2 = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/exportClientRequirePayment/v4`, {
    method: 'post',
    data: params,
    // params,
    responseType: 'blob',
    getResponse: true,
  });
};
/* 更新发件人信息 */
export const updateSenderInfoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/modifySendAddress`, {
    method: 'POST',
    data,
  });
};

/* 更新收件人信息 */
export const updateReceiverInfoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/modifyRecipientAddress`, {
    method: 'POST',
    data,
  });
};

/* 更新通知人信息 */
export const updateNotifyInfoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/modifyNotificationAddress`, {
    method: 'POST',
    data,
  });
};

/* 启用禁用渠道列表 */
export const getChannelListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/channel/enable`, {
    method: 'POST',
    data,
  });
};

/* 查询分票统计数据列表 */
export const getSplitStatisticsListAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/configure/getGoodsInfoListByDeclaration`,
    {
      method: 'POST',
      data,
    },
  );
};

/* 查询分票详情 */
export const getSplitDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/configure/getGoodsInfoByDeclaration`, {
    method: 'GET',
    params,
  });
};

/* 更新分票信息 */
export const updateSplitInfoAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/configure/modifyConfigureGoodsInfoByDeclaration`,
    {
      method: 'POST',
      data,
      // requestType: 'json',
    },
  );
};

/* 查询运单列表 */
export const getWaybillListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tmsList`, {
    method: 'POST',
    data,
  });
};

/* 获取日志 */
export const getLogListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/log/getList`, {
    method: 'GET',
    params,
    requestType: 'json',
  });
};

/* 修改产品或者代理渠道关联的分区 */
export const updateProductZoneAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/product/changeSiteZone`, {
    method: 'POST',
    data,
  });
};
/* 历史分区数据 */
export const listHistorySiteZoneAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/product/listHistorySiteZone`, {
    method: 'get',
    params,
  });
};

/* 保存渠道服务商 */
export const saveChannelServiceProviderAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/provider/save`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查询渠道服务商 列表 */
export const getChannelServiceProviderListAPI = (params: any) => {
  return request(
    `${REQUESTADDRESS_W}/product/chanel/provider/properties/list`,
    {
      method: 'GET',
      params,
    },
  );
};

/* 查询服务商详情 */
export const getChannelServiceProviderDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/provider/getProvider`, {
    method: 'GET',
    params,
  });
};

/* 查询运单列表 尾程打单 */
export const getWaybillListAPI2 = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tmsList`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 渠道商试算比价 */
export const channelTrialPriceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/getPrice`, {
    method: 'POST',
    data,
  });
};
export const channelTrialPriceAPIV2 = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/getPrice/v2`, {
    method: 'POST',
    data,
  });
};
// 批量试算
export const getPrice = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/getPrice/batch`, {
    method: 'POST',
    data,
  });
};
export const getChannelTrialPriceAPIV2 = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/getProviderPriceResult`, {
    method: 'POST',
    data,
  });
};
// 批量预报
export const getOrderBatchAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/order/batch`, {
    method: 'POST',
    data,
  });
};
/* 提交预报打单 */
export const submitWaybillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/order`, {
    method: 'POST',
    data,
  });
};
export const submitWaybill2API = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/orderAndModifyAddr`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
// 预付关税
export const prepayCustomersAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/waybill/update_prepay_customers_fee_option`,
    {
      method: 'post',
      data,
      requestType: 'json',
    },
  );
};
// 付税规则设置
export const settingDutiesOptionAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/setting_duties_option`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 申报金额
export const modifyInsuranceAmountAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyInsuranceAmount`, {
    method: 'post',
    data,
  });
};
/* 修改运单收件地址 */
export const modifyWaybillAddressAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyRecipientAddr`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 打印面单 */
export const printWaybillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/getLabelUrl`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 打印发票 */
// export const printInvoiceAPI = (params: any) => {
//   return request(`${REQUESTADDRESS_W}/waybill/getInvoice`, {
//     method: 'GET',
//     params,
//     responseType: 'blob',
//     getResponse: true,
//   });
// };
// /* 打印发票 */ 韩前面换成下面这个接口
export const printInvoiceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/downloadPdfInvoices`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};
/* 下载接口 */
export const downloadAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/file/preview`, {
    method: 'GET',
    params,
  });
};

/* 保存渠道转单号 */
export const saveChannelWaybillNoAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/saveProviderWaybillNo`, {
    method: 'POST',
    data,
  });
};

/* 取消预报 */
export const cancelWaybillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/cancel`, {
    method: 'POST',
    data,
  });
};
/* 批量取消预报 */
export const cancelBatchAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/cancel/batch`, {
    method: 'POST',
    data,
  });
};
/* 增加轨迹 */
export const addWaybillTrackAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/track/add`, {
    method: 'POST',
    data,
  });
};
/* 轨迹分类 */
export const getCategoryAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/phrase/getCategory`, {
    method: 'get',
    params,
  });
};

/* 获取指定分类的常用语列表 */
export const getListByCategoryAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/phrase/getListByCategory`, {
    method: 'get',
    params,
  });
};
// 删除分类的常用语
export const phraseRemoveAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/phrase/remove`, {
    method: 'get',
    params,
  });
};
// 新建模版
export const phraseCreateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/phrase/create`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 获取 轨迹list */
export const getWaybillTrackListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/track/getList`, {
    method: 'GET',
    params,
  });
};

/* 删除轨迹 */
export const deleteWaybillTrackAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/track/remove`, {
    method: 'POST',
    data,
  });
};

/* 查询银行账户列表 */
export const getBankAccountListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listReceiptAccount`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
// 银行账户导出
export const exportReceiptAccountAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/exportReceiptAccount`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};
// 收款记录导出
// export const exportReceiptAPI = (data: any) => {
//   return request(
//     `${REQUESTADDRESS_W}/finance/exportReceipt`,
//     {
//       method: 'post',
//       data,
//       responseType: 'blob',
//       requestType: 'json',
//       getResponse: true,
//     },
//   );
// };
/* 创建银行账户 */
export const createBankAccountAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/saveReceiptAccount`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 启用禁用银行账户 */
export const enableBankAccountAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/enableReceiptAccount`, {
    method: 'POST',
    data,
  });
};

/* 查询应收账单列表 */
export const getReceivableBillListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listTMSBills`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 查询应收账单核销记录 */
export const getReceivableBillRecordAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listWriteOffDetailsByBillId`, {
    method: 'GET',
    params,
  });
};

/* 查询运单标签模版列表 */
export const getListOfWaybillLabelTemplatesAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/label/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 创建请款单 */
export const createPaymentBillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/createRequirePayment`, {
    method: 'POST',
    data,
    // requestType: 'json',
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

// /* 查询请款单列表 */
// export const getPaymentBillListAPI = (data: any) => {
//   return request(`${REQUESTADDRESS_W}/finance/listRequirePayment`, {
//     method: 'post',
//     data,
//     requestType: 'json',
//   });
// };
export const getPaymentBillListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listRequirePayment`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 作废
export const invalidRequirePaymentAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/invalidRequirePayment`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
/* 批量导出请款单 */
export const batchExportPaymentBillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/exportClientRequirePayment`, {
    method: 'POST',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 导出请款单 */
export const exportPaymentBillAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/exportClientRequirePayment`, {
    method: 'GET',
    params,
    responseType: 'blob',
    getResponse: true,
  });
};

/* 查询待我审批列表 */
export const getMyApprovalListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/approval/listMyApproval`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 查询增加费用及审批列表 */
export const getAddFeeAndApprovalListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getBillAndFeeByWaybill`, {
    method: 'GET',
    params,
  });
};
/* 根据币种拿汇率 */
export const queryExchangeRateAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/recharge/queryExchangeRate`, {
    method: 'GET',
    params,
  });
};
/* 作废 */
export const orderInvalidAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/transport/cabin/resale/order/invalid`, {
    method: 'POST',
    data,
  });
};

/* 修改提单状态 */
export const modifyBillOfLadingStatusAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/modifyState`, {
    method: 'POST',
    data,
  });
};

/* 获取用户列表 */
export const getUserListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/getUserList`, {
    method: 'GET',
    params,
  });
};

/* 增加优惠费前检查 */
export const checkAddDiscountFeeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/checkDiscount`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 添加优惠费 */
export const addDiscountFeeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/addDiscount`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 查看包含费用 */
export const getIncludeFeeAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getRequirePaymentDetail`, {
    method: 'GET',
    params,
  });
};

/* 上传文件 */
export const uploadFileAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/file/upload`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 业务员申请消账 */
export const applyWriteOffAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/applyWriteOff`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查看销账 信息 */
export const getWriteOffInfoAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getRequirePaymentWriteOffInfo`, {
    method: 'GET',
    params,
  });
};

/* 审批 */
export const approvalAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/approval/approve`, {
    method: 'POST',
    data,
  });
};

/* 批量审批 */
export const batchApprovalAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/approval/batchApprove`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 查询审批单详情 */
export const getApprovalDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/approval/getApprovalDetail`, {
    method: 'GET',
    params,
  });
};

/* 查询我的申请列表 */
export const getMyApplyListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/approval/listMyApply`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 撤回我的申请 */
export const withdrawMyApplyAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/approval/revertApproval`, {
    method: 'POST',
    data,
  });
};

/* 查询收款记录列表 */
export const getReceiptRecordListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listReceiptListV2`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 收款记录导出
export const exportReceiptAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/exportReceipt`, {
    method: 'post',
    data,
    responseType: 'blob',
    requestType: 'json',
    getResponse: true,
  });
};
/* 查询请款单审批列表 */
export const getPaymentBillApprovalListAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/finance/listRequirePaymentAfterWriteOff`,
    {
      method: 'post',
      data,
      requestType: 'json',
    },
  );
};

/* 财务 收款单录入 */
export const receiptEntryAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/createReceipt`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 财务 提现 */
export const createPayoutsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/createPayouts`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 付款单 提现 */
export const createProviderPayoutsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/createProviderPayouts`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/*付款管理 作废 */
export const invalidPaymentRequireAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/invalidPaymentRequire`, {
    method: 'POST',
    data,
  });
};

/* 财务  重新分配*/
export const financePayoutsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/financePayouts`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/*付款管理 财务操作付款提现*/
export const financeProviderPayoutsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/financeProviderPayouts`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 财务 审核请款单 */
export const auditPaymentBillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/approveRequirePayment`, {
    method: 'POST',
    data,
  });
};

/* 财务 审批收款单 */
export const approvalReceiptAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/approveReceipt`, {
    method: 'POST',
    requestType: 'forms',
    data,
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};
/* 财务 查看提现详情 */
export const listPayoutsDetailAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listPayoutsDetail`, {
    method: 'POST',
    data,
  });
};
/* 财务 查看付款提现详情 */
export const listProviderPayoutsDetailAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listProviderPayoutsDetail`, {
    method: 'POST',
    data,
  });
};

/* 财务 查看提现 撤销全部 */
export const revertPayoutsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/revertPayouts`, {
    method: 'POST',
    data,
  });
};
/* 财务 付款查看提现 撤销全部 */
export const revertProviderPayoutsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/revertProviderPayouts`, {
    method: 'POST',
    data,
  });
};

/* 查看财务主体列表 */
export const getFinanceSubjectListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listFinanceEntity`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 查看币种汇率列表 */
export const getListExchangeRateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listExchangeRate`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 币种汇率列表启用 */
export const getModifyExchangeRateStateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/modifyExchangeRateState`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
/* 币种汇率复制 */
export const copyExchangeRateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/copyExchangeRate`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
/* 币种汇率新建 */
export const saveExchangeRateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/saveExchangeRate`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 财务主体设置 */
export const financeSubjectSettingAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/saveFinanceEntity`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 数据维护 费用科目 */
export const dataMaintenanceExpenseAccountAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/tag/listTags`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 数据维护 费用科目 编辑 */
export const editExpenseAccountAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/tag/saveTag`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 数据维护 费用科目 删除 */
export const deleteExpenseAccountAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/tag/deleteTag`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 查看收款单核销列表 */
export const getReceiptWriteOffListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listWriteOffBills`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 标准应收 */
export const listWriteOffBillsV2 = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listWriteOffBills/v2`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 标准销账 */
export const getReceiptWriteOffListV2API = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listWriteOffBills/v2`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 财务销账 */
export const financeWriteOffAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/financeWriteOff`, {
    method: 'POST',
    data,
    requestType: 'forms',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};

/* 撤销销账 */
export const cancelWriteOffAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/revertWriteOff`, {
    method: 'POST',
    data,
  });
};

/* 销账 */
export const addBillsToProviderPaymentAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/addBillsToProviderPayment`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 删除 */
export const deleteReceiptAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/deleteBillsFromProviderPayment`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 查看收款单详情 */
export const getReceiptDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getReceiptDetail`, {
    method: 'GET',
    params,
  });
};
/* 申请付款*/
export const getAaymentAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/createProviderPayment`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 付款管理申请付款 */
export const getAaymentAPI2 = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/finance/createProviderPaymentWithoutBills`,
    {
      method: 'post',
      data,
      requestType: 'json',
    },
  );
};

/* 编辑付款管理申请付款 */
export const editAaymentAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/modifyPaymentRequire`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

export const getAaymentv2API = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/createProviderPayment/v2`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 查询付款申请列表 */
export const getlistPaymentRequireAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listPaymentRequire`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 批量审批接口 */
export const batchApprovalPaymentAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/approvePaymentRequire/batch`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};

/* 批量修改备注、时间、付款银行 */
export const batchUpdatePaymentRequireAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/batchModifyPaymentRequire`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 查询付款申请详情 */
export const getPaymentRequireDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/getPaymentRequireDetail`, {
    method: 'GET',
    params,
  });
};
/* 付款记录撤销 */
export const getRevertPaymentRecordAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/revertPaymentRecord`, {
    method: 'post',
    data,
  });
};
/* 创建付款记录 */
export const getCreatePaymentRecordAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/createPaymentRecord`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 服务商查询
export const getlistProviderByTypeAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listProviderByType`, {
    method: 'get',
    params,
  });
};
// 根据币种查汇率
export const getQueryExchangeRateAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/rate/queryExchangeRate`, {
    method: 'get',
    params,
  });
};
// 审批付款申请
export const getApprovePaymentRequireAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/approvePaymentRequire`, {
    method: 'post',
    data,
  });
};
/* 查看分区价格表详情 */
export const getZonePriceDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/template/zone/price/detail`, {
    method: 'GET',
    params,
  });
};

/* 删除重量传输规则 */
export const deleteWeightRuleAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/weight/transfer/rule/delete`, {
    method: 'POST',
    data,
  });
};
/*获取账单明细列表*/
export const getBillList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/utils/getBillList`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/*获取上传记录列表*/
export const getUploadList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/utils/getUploadRecordList`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/*取消账单上传*/
export const cancelUploadList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/utils/cancelRecord`, {
    method: 'POST',
    data,
  });
};
/*删除账单上传*/
export const deleteUploadList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/utils/deleteRecord`, {
    method: 'POST',
    data,
  });
};
/*导出账单*/
export const exportBillDetails = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/utils/export/billDetails`, {
    method: 'POST',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};

/* 修改轨迹时间 */
export const updateTrackTimeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/track/updateTrackTimeByRecordId`, {
    method: 'POST',
    data,
  });
};
/* 修改轨迹信息 */
export const modifyTrackByReferenceIdAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/track/modifyTrackByReferenceId`, {
    method: 'POST',
    data,
  });
};

/* 服务科目 */
export const getServiceSubjectAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/tag/listTags`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};

/* 保存科目 */
export const saveServiceSubjectAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/tag/saveTag`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 删除科目 */
export const deleteServiceSubjectAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/tag/deleteTag`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
/*撤销费用*/
export const cancelProviderBill = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/cancelProviderBill`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
//删除内陆转运运单
export const interRemoveWaybill = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/agent/removeWaybill`, {
    method: 'POST',
    data,
    // requestType: 'json',
  });
};
