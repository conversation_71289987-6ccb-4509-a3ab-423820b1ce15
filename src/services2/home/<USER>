import { request } from '@umijs/max';
import { REQUESTADDRESS, REQUESTADDRESS_W } from '@/globalData';
/* 获取用户登录token  */
export const getUserToken = (params: any) => {
  return request('https://dev-server-home.kmfba.com/api/authing/identity', {
    method: 'GET',
    params,
  });
};
/* 账号信息 */
/* 获取用户信息 */
export const getUserInfo = (params: any) => {
  return request(`${REQUESTADDRESS}/user/getProfile`, {
    method: 'GET',
    params,
  });
};
/* 上传头像 */
export const uploadAvatarAPI = (data: any) => {
  return request(`${REQUESTADDRESS}/home/<USER>/uploadAvatar`, {
    method: 'POST',
    data,
  });
};

/* 获取账号信息 */
export const getAccountInformationAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/getAccount`, {
    method: 'GET',
    params,
  });
};
/* 身份校验 */
export const identityCheckAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/getIdentityCheckTypes`, {
    method: 'GET',
    params,
  });
};
/* 重置密码 */
export const resetPasswordAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/modifyPassword`, {
    method: 'GET',
    params,
  });
};

/* 短信邮箱密码校验 */
export const checkCodeAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/check`, {
    method: 'post',
    data: params,
  });
};
/* 发送邮箱验证吗 */
export const sendEmailCodeAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/sendEmailCode`, {
    method: 'GET',
    params,
  });
};

/* 绑定手机号 */
export const bindPhoneAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/bindPhone`, {
    method: 'GET',
    params,
  });
};
/* 解绑手机 */
export const unbindPhoneAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/unbindPhone`, {
    method: 'GET',
    params,
  });
};
/* 绑定邮箱 */
export const bindEmailAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/bindEmail`, {
    method: 'GET',
    params,
  });
};
/* 解绑邮箱 */
export const unbindEmailAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/unbindEmail`, {
    method: 'GET',
    params,
  });
};

/* 获取手机验证码 */
export const getPhoneCodeAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/sendSMSCode`, {
    method: 'GET',
    params,
  });
};

/* 修改名字 */
export const changeNameAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/modifyName`, {
    method: 'GET',
    params,
  });
};

/* 修改默认企业 */
export const modifyDefaultEnterpriseAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/setDefaultTenant`, {
    method: 'GET',
    params,
  });
};
/* 退出登录 */
export const logoutAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/quit`, {
    method: 'GET',
    params,
  });
};
/* 获取操作日志 */
export const getOperationLog = (data: any) => {
  return request(`${REQUESTADDRESS_W}/log/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 获取操作日志模块 */
export const getModuleList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/log/getModuleList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 获取操作日志操作人 */
export const getOperationList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/log/getOperationList`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
//
// 日志操作维护列表
export const getOperationListGroupByModuleAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/log/getOperationListGroupByModule`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 日志操作维护修改
export const modifyLogNameDescAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/log/modifyLogNameDesc`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};

/* 后面空了 在抽出来 */
/* 资料管理 */
/* 前台获取地址库列表 */
export const getAddressInformationAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/address/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 省市区模糊搜索 */
export const getSearchAddressAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/city/search`, {
    method: 'GET',
    params,
  });
};
/* 获取省市区数据 US */
export const getUSAddressAPI = (params: any) => {
  return request(`https://web-common.kmfba.com/location/country.json`, {
    method: 'get',
    params,
  });
};

/* 地址库添加地址 */
export const addAddressAPI = (params: any) => {
  console.log(params);
  return request(`${REQUESTADDRESS_W}/address/add`, {
    method: 'GET',
    params,
  });
};

/* 地址库 删除地址 */
export const deleteAddressAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/address/remove`, {
    method: 'post',
    data,
  });
};
/* 修改地址库信息 */
export const modifyAddressAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/modify`, {
    method: 'GET',
    params,
  });
};
/* 后台获取地址库 获取系统地址库/FBA */
export const getAddressLibraryAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/address/getStreetList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 获取发件地址 */
export const getSenderAddressAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/getSendAddressList`, {
    method: 'get',
    params,
  });
};
/* 获取 区域 地址 */
export const getAreaAddressAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/getChannelGroupList`, {
    method: 'get',
    params,
  });
};

/* 获取渠道产品列表 */
export const getChannelProductAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/getClientChannelList`, {
    method: 'get',
    params,
  });
};

/* 获取渠道产品详情 */
export const getChannelProductDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/get`, {
    method: 'get',
    params,
  });
};

/* 获取消费明细列表 */
export const getConsumptionDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/bill/getList`, {
    method: 'get',
    params,
  });
};

/* 运单创建 立即试算 */
export const getWaybillEstimateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tryCalc`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 多票创建 */
export const getWaybillBatchCreateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/multiCreate`, {
    method: 'post',
    data,
  });
};

/* 客户渠道条数 */
export const getChannelCountAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/getClientChannelCount`, {
    method: 'post',
    data: params.Address,
    requestType: 'json',
  });
};

/* 运单导出 */
export const waybillExportAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/download`, {
    method: 'get',
    params,
    responseType: 'blob',
    getResponse: true,
  });
};

/* 运单创建 立即预报 */
export const getWaybillReportAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/create`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 获取运单列表 */
export const getWaybillListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/list`, {
    method: 'get',
    params,
  });
};
/* 运单列表详情 */
export const waybillListDetailsAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/one`, {
    method: 'get',
    params,
  });
};
/* 获取包裹信息 */
export const getPackageInformationAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/pieces/list`, {
    method: 'get',
    params,
  });
};
/* 取消运单 */
export const cancelWaybillAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/cancel`, {
    method: 'get',
    params,
  });
};
/* 城市查询 */
export const addressFuzzySearchAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/city/search`, {
    method: 'get',
    params,
  });
};
/* 修改运单回填 */
export const getWaybillListByWaybillIdsAPI = (params: any) => {
  return request(
    `${REQUESTADDRESS_W}/intermodal/agent/getWaybillListByWaybillIds`,
    {
      method: 'get',
      params,
    },
  );
};
/* 根据id搜索城市 */
export const addressFuzzyGetAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/city/get`, {
    method: 'get',
    params,
  });
};
/* 支付金额接口 */
export const payMoneyAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/recharge/service/alipay`, {
    method: 'post',
    data: params,
    requestType: 'json',
  });
};

/* 对账管理查询列表 */
export const getReconciliationListAPI = (params: {
  start: string | number;
  len: string | number;
  insertTime?: any;
  endTime?: any;
  key?: string | number; //运单id 子运单号
  keyword?: string;
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/waybill/list`, {
    method: 'get',
    params,
  });
};

/* 对账管理 配置列表 */
export const getReconciliationConfigListAPI = (params: {
  start: string | number;
  len: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/list`, {
    method: 'get',
    params,
  });
};

/* 对账管理 新增配置 */
export const addReconciliationConfigAPI = (params: {
  name: string; //配置名称
  issueNo: 'A'; //期号
  subExpressCode: 'B'; //ups
  outerOrderId: 'C'; //关联订单号
  amount: 'D'; //金额
  reason: 'E'; //原因
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/create`, {
    method: 'post',
    data: params,
  });
};

/* 对账管理 删除配置 */
export const deleteReconciliationConfigAPI = (params: {
  id: string; //配置id
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/delete`, {
    method: 'post',
    data: params,
  });
};

/* 对账管理 修改配置 */
export const updateReconciliationConfigAPI = (params: {
  id: string; //配置id
  name: string; //配置名称
  issueNo: string; //期号
  subExpressCode: string; //ups
  outerOrderId: string; //关联订单号
  amount: string; //金额
  reason: string; //原因
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/update`, {
    method: 'post',
    data: params,
  });
};

/* 上传Excel */
export const uploadExcelAPI = (params: {
  multiFile: any;
  configId: string; //配置id
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/upload`, {
    method: 'post',
    data: params,
  });
};

/* 对账运单详情 */
export const getReconciliationWaybillDetailAPI = (params: {
  id: string; //运单id
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/waybill/one`, {
    method: 'get',
    params,
  });
};

/* 运单详情明细 */
export const getWaybillDetailAPI = (params: {
  id: string; //运单id
  len: string | number;
  start: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/waybill/getBills`, {
    method: 'get',
    params,
  });
};

/* 面单下载 */
export const downloadWaybillAPI = (params: {
  id: string; //运单id
}) => {
  return request(`${REQUESTADDRESS_W}/waybill/getLabel`, {
    method: 'get',
    params,
  });
};

/* 权限管理 */
/* 获取角色列表 */
export const getRoleListAPI = (data: any) => {
  return request(`${REQUESTADDRESS}/ram/getRoleList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 复制 */
export const cloneRoleAPI = (data: any) => {
  return request(`${REQUESTADDRESS}/ram/cloneRole`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
/* 是否启用角色 */
export const enableRoleAPI = (params: {
  roleId: string | number;
  enabled: number | string;
}) => {
  return request(`${REQUESTADDRESS}/ram/enableRole`, {
    method: 'post',
    data: params,
  });
};

/* 获取角色权限 */
export const getRolePermissionAPI = (params: { roleId?: string | number }) => {
  return request(`${REQUESTADDRESS}/ram/getRole`, {
    method: 'get',
    params,
  });
};

/* 获取当前实例权限目录 */
export const getPermissionCatalogAPI = (params: {
  roleId?: string | number;
}) => {
  return request(`${REQUESTADDRESS}/ram/getInstanceRuleList`, {
    method: 'get',
    params,
  });
};

/* 创建角色 */
export const createRoleAPI = (params: {
  name: string;
  authorities: string;
}) => {
  return request(`${REQUESTADDRESS}/ram/createRole`, {
    method: 'post',
    data: params,
  });
};

/* 删除角色 */
export const deleteRoleAPI = (params: { roleId: string | number }) => {
  return request(`${REQUESTADDRESS}/ram/deleteRole`, {
    method: 'post',
    data: params,
  });
};

/* 编辑角色获取权限 */
export const getEditRolePermissionAPI = (params: {
  roleId: string | number;
}) => {
  return request(`${REQUESTADDRESS}/ram/getRoleRuleList`, {
    method: 'get',
    params,
  });
};
/* 编辑角色权限 */
export const editRolePermissionAPI = (params: {
  roleId: string | number;
  authorities: string;
  name?: string;
}) => {
  return request(`${REQUESTADDRESS}/ram/modifyRoleAuthorities`, {
    method: 'post',
    data: params,
  });
};
/* 用户列表 */
export const getUserListAPI = (data: any) => {
  return request(`${REQUESTADDRESS}/ram/getUserList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 添加用户 */
export const addUserAPI = (params: {
  name: string;
  roleIds: string;
  email: string;
}) => {
  return request(`${REQUESTADDRESS}/ram/addUser`, {
    method: 'post',
    data: params,
  });
};

/* 删除用户 */
export const deleteUserAPI = (params: { uid: string | number }) => {
  return request(`${REQUESTADDRESS}/ram/removeUser`, {
    method: 'post',
    data: params,
  });
};

/* 财务管理 充值明细 */
export const getRechargeDetailAPI = (params: {
  start: string | number;
  len: string | number;
  keyword?: string;
}) => {
  return request(`${REQUESTADDRESS_W}/recharge/service/query`, {
    method: 'get',
    params,
  });
};

/* 首页数据 */
export const getHomeDataAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/instance/getOverview`, {
    method: 'get',
    params,
  });
};

/* 获取开放平台 */
export const getOpenPlatformAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/open/get`, {
    method: 'post',
    params,
  });
};

/* 设置开放平台 */
export const setOpenPlatformAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/open/refresh`, {
    method: 'post',
    params,
  });
};
/* 开放品台 配置列表 */
export const getOpenList = (params: any) => {
  return request(`${REQUESTADDRESS}/open/getList`, {
    method: 'get',
    params,
  });
};
/* 提单列表 运单后端 配置列表 */
export const forbiddenRuleListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/wms/forbiddenRule/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 提单列表 运单后端 配置新增 */
export const forbiddenRuleSaveAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/configure/wms/forbiddenRule/save`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
/* 提单列表 运单后端 配置停用 */
export const forbiddenRuleDisableAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/configure/wms/forbiddenRule/disable`, {
    method: 'get',
    params,
    // requestType: 'json',
  });
};

/* 开放品台 新增配置 */
export const getOpenAdd = (data: any) => {
  return request(`${REQUESTADDRESS}/open/add`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 开放品台 重置 */
export const getOpenRefresh = (params: any) => {
  return request(`${REQUESTADDRESS}/open/refresh`, {
    method: 'get',
    params,
  });
};

/* 开放品台 状态操作 */
export const getOpenEnable = (params: any) => {
  return request(`${REQUESTADDRESS}/open/enable`, {
    method: 'get',
    params,
    // requestType: 'json',
  });
};
/* 客户管理 */
/* 获取客户列表 */
export const getClientListAPI = async (data: any) => {
  let res = await request(`${REQUESTADDRESS_W}/client/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
  return res;
};
/* 业务类型 */
export const getConfigListAPI = async (data: any) => {
  let res = await request(`${REQUESTADDRESS_W}/client/getConfigList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
  return res;
};

/* 更改OMS轨迹 */
export const modifyOmsTrackAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/config/modifyOmsTrack`, {
    method: 'post',
    data,
  });
};

/* 更改OMS试算 */
export const modifyOmsTrialAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/config/modifyOmsTrial`, {
    method: 'post',
    data,
  });
};
/* 更改OMS财务 */
export const modifyOmsFinanceAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/config/modifyOmsFinance`, {
    method: 'post',
    data,
  });
};
/* 更改确认策略 */
export const modifyWaybillConfirmMethodAPI = async (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/client/config/modifyWaybillConfirmMethod`,
    {
      method: 'post',
      data,
    },
  );
};
/* 更改pod发件人 */
export const modifyPodSendInfoAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/config/modifyPodSendInfo`, {
    method: 'post',
    data,
  });
};
/* 获取公告列表 */
export const noticeListAPI = async (data: any) => {
  let res = await request(`${REQUESTADDRESS_W}/notice/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
  return res;
};
export const downloadAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/download`, {
    method: 'post',
    data,
    responseType: 'blob',
    requestType: 'json',
    getResponse: true,
  });
};
// 指定票件收款
export const listUnpaidWaybillSummaryAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/listUnpaidWaybillSummary`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 公告状态修改 */
export const changeStatusAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/notice/change_status`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 新建公告 */
export const saveAPI = async (data: any) => {
  let res = await request(`${REQUESTADDRESS_W}/notice/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
  return res;
};
/* 修改客服 */
export const batchModifyCustomerServiceAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/batchModifyCustomerService`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
/* 修改业务员 */
export const batchModifySalesmanAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/batchModifySalesman`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
/* 新建客户 */
export const addClientAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/createOMSClient`, {
    method: 'post',
    data: params,
    requestType: 'json',
  });
};

/* 调整记录 */
export const getAdjustRecordAPI = (params: {
  // moduleName: string;
  name?: string; //
  extraId_1: string;
  start: string | number;
  len: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/log/getList`, {
    method: 'post',
    data: params,
    requestType: 'json',
  });
};

/* 调整客户余额 */
export const adjustClientBalanceAPI = (params: {
  id: string; //客户id
  balance: string | number; //调整金额
  reason: string; //调整原因
}) => {
  return request(`${REQUESTADDRESS_W}/client/increaseBalance`, {
    method: 'post',
    data: params,
  });
};

/* 调整客户押金 */
export const adjustClientDepositAPI = (params: {
  id: string; //客户id
  deposit: string | number; //调整押金
  reason: string; //调整原因
}) => {
  return request(`${REQUESTADDRESS_W}/client/modifyDeposit`, {
    method: 'post',
    data: params,
  });
};

/* 调整客户信用值 */
export const adjustClientCreditAPI = (params: {
  id: string; //客户id
  credit: string | number; //调整信用值
  reason: string; //调整原因
}) => {
  return request(`${REQUESTADDRESS_W}/client/modifyCredit`, {
    method: 'post',
    data: params,
  });
};

/* 调整客户冻结资金 */
export const adjustClientFrozenAPI = (params: {
  id: string; //客户id
  frozenAmount: string | number; //调整冻结资金
  reason: string; //调整原因
}) => {
  return request(`${REQUESTADDRESS_W}/client/modifyFrozenAmount`, {
    method: 'post',
    data: params,
  });
};

/* 调整客户汇率损兑 */
export const adjustClientExchangeAPI = (params: {
  id: string; //客户id
  loss: string | number; //调整汇率损兑
  reason: string; //调整原因
}) => {
  return request(`${REQUESTADDRESS_W}/client/modifyExchangeloss`, {
    method: 'post',
    data: params,
  });
};

/* 获取客户管理实时信息 */
export const getClientRealTimeInfoAPI = (params: {
  id: string; //客户id
}) => {
  return request(`${REQUESTADDRESS_W}/client/get`, {
    method: 'get',
    params,
  });
};

/* 获取 功能开通目录 */
export const getFunctionOpenAPI = (params: {
  service: string; // WaybillManagement  Waybill
  isConfig: string | number; //1 仅获取可配置
}) => {
  return request(`${REQUESTADDRESS}/ram/getServiceRuleList`, {
    method: 'get',
    params,
  });
};
/* 获取客户渠道列表 */
export const getClientChannelListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/getClientChannelList`, {
    method: 'get',
    params,
  });
};
/* 获取客户渠道列表 */
export const queryLabelsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/track/management/queryLabels`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 开启或关闭渠道 */
export const openOrCloseChannelAPI = (
  params: {
    clientId: string; //客户id
    channelId: string; //渠道id
    enabled: number; //是否开启 0：关闭 1：开启
  },
  options?: any,
) => {
  return request(`${REQUESTADDRESS_W}/channel/enableChannelToClient`, {
    method: 'post',
    data: params,
    ...(options || {}),
  });
};

/* 设置客户佣金点数 */
export const setClientCommissionAPI = (
  params: {
    clientId: string; //客户id
    channelId: string; //渠道id
    cut_percent: number; //佣金点数
  },
  options?: any,
) => {
  return request(`${REQUESTADDRESS_W}/channel/setCutPercentToClient`, {
    method: 'post',
    data: params,
    ...(options || {}),
  });
};
/* 解绑客户渠道绑定 */
export const unbindClientChannelAPI = (
  params: {
    clientId: string; //客户id
    channelId: string; //渠道id
  },
  options?: any,
) => {
  return request(`${REQUESTADDRESS_W}/channel/unbindAddressToClient`, {
    method: 'post',
    data: params,
    ...(options || {}),
  });
};
/* 绑定地址给到客户渠道 */
export const bindAddressToClientChannelAPI = (
  params: {
    clientId: string; //客户id
    channelId: string; //渠道id
    addressId: string; //地址id
  },
  options?: any,
) => {
  return request(`${REQUESTADDRESS_W}/channel/bindAddressToClient`, {
    method: 'post',
    data: params,
    ...(options || {}),
  });
};

/* 获取偏远邮编库 */
export const getRemoteZipCodeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/address/getRemoteZipcodeList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 模版管理 */
/* 分区价格列表 */
export const getPartitionPriceListAPI = (params: {
  start?: string | number; //后台没做分页
  len?: string | number;
  keyword?: string;
}) => {
  return request(`${REQUESTADDRESS_W}/template/zone/price/list`, {
    method: 'get',
    params,
  });
};

/* 分区模版列表 */
export const getPartitionTemplateListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/zone/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 获取分区价格表详情 */
export const getPartitionPriceDetailAPI = (params: {
  zonePriceId: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/template/zone/price/detail`, {
    method: 'get',
    params,
  });
};

/* 重量模版列表 */
export const getWeightTemplateListAPI = (data: {
  start?: string | number; //后台没做分页
  len?: string | number;
  keyword?: string;
}) => {
  return request(`${REQUESTADDRESS_W}/template/weight/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 超长超重规则 */
export const getLongAndHeavyRuleListAPI = (data: {
  start?: string | number; //后台没做分页
  len?: string | number;
  keyword?: string;
}) => {
  return request(`${REQUESTADDRESS_W}/template/custom/rule/list`, {
    method: 'POST',
    data,
    requestType: 'json',
  });
};
/* 超长超重规则详情 */
export const getLongAndHeavyRuleDetailAPI = (params: {
  ruleId: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/template/custom/rule/detail`, {
    method: 'get',
    params,
  });
};
/* 语法检测接口 */
export const checkRuleAPI = (data: { ruleExp: string }) => {
  return request(`${REQUESTADDRESS_W}/template/validateRuleExp`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 超长超重接口 保存新增 */
export const addLongAndHeavyRuleAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/save/custom/rule`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 超长超重 删除接口 */
export const deleteLongAndHeavyRuleAPI = (data: {
  ruleId: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/template/deleteCustomRule`, {
    method: 'post',
    data,
  });
};
/* 复制分区模版接口 */
export const copyPartitionTemplateAPI = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/zone/copy`, {
    method: 'POST',
    data,
  });
};

/* 附加费列表 */
export const getAdditionalFeeListAPI = (params: {
  start?: string | number; //后台没做分页
  len?: string | number;
  keyword?: string;
}) => {
  return request(`${REQUESTADDRESS_W}/template/additional/fee/list`, {
    method: 'get',
    params,
  });
};

/* 分区价格表模版下载 */
export const downloadPartitionPriceTemplateAPI = (data: {
  zoneId: string; //分区模版id
  weightId: string; //重量模版id
}) => {
  return request(`${REQUESTADDRESS_W}/template/zone/price/download`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
  });
};

/* 分区价格表导入 */
export const importPartitionPriceAPI = (data: {
  zoneId: string; //分区模版id
  weightId: string; //重量模版id
  multipartFile: File;
  name: string; //分区价格表名称
  senderAddress: string; //发件人地址id
  priceBy?: 0 | 1; //0:按包裹 1:按票算
  id: any; //分区价格表id
}) => {
  return request(`${REQUESTADDRESS_W}/template/zone/price/import`, {
    method: 'post',
    data,
    requestType: 'form',
  });
};

/* 分区价格表删除 */
export const deletePartitionPriceAPI = (data: {
  zonePriceId: string; //分区价格表id
}) => {
  return request(`${REQUESTADDRESS_W}/template/deleteZonePrice`, {
    method: 'post',
    data,
  });
};

/* 分区模版删除 */
export const deletePartitionTemplateAPI = (data: {
  zoneId: string; //分区模版id
  zoneType?: 0 | 1; //0:分区 1:地区
}) => {
  return request(`${REQUESTADDRESS_W}/template/deleteZone`, {
    method: 'post',
    data,
  });
};

/* 重量模版删除 */
export const deleteWeightTemplateAPI = (data: {
  weightId: string; //重量模版id
}) => {
  return request(`${REQUESTADDRESS_W}/template/deleteWeight`, {
    method: 'post',
    data,
  });
};
/* 复制模版 */
export const copyWeightAPI = (data: {
  weightId: string; //重量模版id
}) => {
  return request(`${REQUESTADDRESS_W}/template/copy/weight`, {
    method: 'post',
    data,
  });
};
/* 附加费模版删除 */
export const deleteAdditionalFeeTemplateAPI = (data: {
  additionalFeeId: string; //附加费模版id
}) => {
  return request(`${REQUESTADDRESS_W}/template/deleteAdditionalFee`, {
    method: 'post',
    data,
  });
};

/* 分区模版详情 */
export const getPartitionTemplateDetailAPI = (params: {
  zoneId: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/template/zone/detail`, {
    method: 'get',
    params,
  });
};
export const getPartitionTemplateDetail2API = (params: any) => {
  return request(`${REQUESTADDRESS_W}/template/listRemoteRuleDetail`, {
    method: 'get',
    params,
  });
};
/* 保存分区模版 */
export const savePartitionTemplateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/save/zone`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
export const savePartitionTemplate2API = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/saveRemoteRule`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 偏远规则删除 */
export const deleteFarRuleAPI = (data: { ruleId: string | number }) => {
  return request(`${REQUESTADDRESS_W}/template/deleteRemoteRule`, {
    method: 'post',
    data,
  });
};

/* 重量模版详情 */
export const getWeightTemplateDetailAPI = (params: {
  weightId: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/template/weight/detail`, {
    method: 'get',
    params,
  });
};

/* 保存重量模版 */
export const saveWeightTemplateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/save/weight`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 附加费表详情 */
export const getAdditionalFeeDetailAPI = (params: {
  additionalFeeId: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/template/additional/fee/detail`, {
    method: 'get',
    params,
  });
};

/* 附加费模版下载 */
export const downloadAdditionalFeeTemplateAPI = (data: {
  zoneId: string; //附加费模版id
}) => {
  return request(`${REQUESTADDRESS_W}/template/additional/fee/download`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
  });
};

/* 附加费模版导入 */
export const importAdditionalFeeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/additional/fee/import`, {
    method: 'post',
    data,
    requestType: 'form',
  });
};

/* 获取模版数据 */
export const getTemplateDataAPI = (params: {
  zoneId: string | number;
  weightId?: string | number;
  templateType: string | number; //0 分区价格模版 1 附加费模版
}) => {
  return request(`${REQUESTADDRESS_W}/template/getTemplate`, {
    method: 'get',
    params,
  });
};

/* 获取附加费 */
export const getAdditionalFeeAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/template/additional/fee/types`, {
    method: 'get',
    params,
  });
};

/* 分区价格表详情保存 */
export const savePartitionPriceDetailAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/save/zone/price`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 附加费表 保存 */
export const saveAdditionalFeeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/template/save/additional/fee`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 获取仓库列表 */
export const getWarehouseListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/address/getWarehouseList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
export const getWarehouseListAPIJSON = (data: any) => {
  return request(`${REQUESTADDRESS_W}/address/getWarehouseList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*添加仓库地址*/
export const addWareHouseAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/addWarehouse`, {
    method: 'get',
    params,
  });
};

/*添加fba地址*/
export const addFbaAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/addFBAWarehouse`, {
    method: 'get',
    params,
  });
};
/*删除仓库*/
export const deleteWareHouseAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/removeWarehouse`, {
    method: 'GET',
    params,
  });
};
/*修改仓库*/
export const editWareHouseAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/modifyWarehouse`, {
    method: 'post',
    params,
    requestType: 'json',
  });
};
/*修改客户信息*/
export const editCustomerAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/modify`, {
    method: 'POST',
    data: params,
    requestType: 'json',
  });
};
/*上传用户附件*/
export const uploadClientPic = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/uploadClientPic`, {
    method: 'POST',
    data: params,
  });
};
/*获取标签列表*/
export const getTagListApi = (params: any) => {
  return request(`${REQUESTADDRESS_W}/tag/getList`, {
    method: 'GET',
    params,
  });
};
/*添加邮编*/
export const addZipCodeApi = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/saveRemoteZipcode`, {
    method: 'POST',
    data: params,
  });
};
/*添加邮编*/
export const DeleteZipCodeApi = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/deleteRemoteZipcode`, {
    method: 'POST',
    data: params,
  });
};
//
export const getTagListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/getTagList`, {
    method: 'get',
    params,
  });
};

/* 批量修改运单子单号 */
export const batchModifySubOrderAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchModifySubWaybillNo`, {
    method: 'post',
    data: params,
    requestType: 'json',
  });
};

/* 批量删除子单号 */
export const batchDeleteSubOrderAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchDeleteSubWaybill`, {
    method: 'post',
    data: params,
    requestType: 'json',
  });
};

/* 添加新子单 */
export const addNewSubOrderAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchAddSubWaybill`, {
    method: 'post',
    data: params,
    requestType: 'json',
  });
};

/* 转移子单 */
export const transferSubOrderAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchTransferSubWaybill`, {
    method: 'post',
    data: params,
    requestType: 'json',
  });
};

/* 批量修改 FBAcode */
export const batchModifyFbaCodeAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchModifyFbaCode`, {
    method: 'post',
    data: params,
    requestType: 'json',
  });
};

/* 批量修改品名信息 */
export const batchModifyProductNameAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchModifyBoxDetail`, {
    method: 'post',
    data: params,
    requestType: 'json',
  });
};
