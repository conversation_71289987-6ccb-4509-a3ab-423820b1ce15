import { request } from '@umijs/max';
import { REQUESTADDRESS_W, REQUESTADDRESS } from '@/globalData';
// 添加权限列表数据
export const getInstanceRuleListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/getInstanceRuleList`, {
    method: 'get',
    params,
  });
};
// 权限列表数据
export const getUserRuleListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/getUserRuleList`, {
    method: 'post',
    params,
  });
};
// 用户管理详情
export const getUserDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/getUserDetail`, {
    method: 'get',
    params,
  });
};
// 删除用户信息
export const getRemoveUserAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/removeUser`, {
    method: 'get',
    params,
  });
};
// 修改密码
export const modifyUserPasswordAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/modifyUserPassword`, {
    method: 'get',
    params,
  });
};
// 客户管理详情修改密码
export const clientIdModifyUserPasswordAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/client/modifyUserPassword`, {
    method: 'get',
    params,
  });
};

// 添加用户
export const getAddUserAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/ram/modifyUserRoles`, {
    method: 'post',
    data,
  });
};
// 添加权限（角色）
export const getAddUserAuthorityAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/ram/addUserAuthority`, {
    method: 'post',
    data,
  });
};
// 添加权限（用户）
export const getModifyRoleAuthoritiesAPI = (data: any, bool: boolean) => {
  return request(
    `${REQUESTADDRESS_W}${
      bool ? '/ram/modifyDutyAuthorities' : '/ram/modifyRoleAuthorities'
    }`,
    {
      method: 'post',
      data,
    },
  );
};
// 删除角色
export const getRemoveUserRoleAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/ram/removeUserRole`, {
    method: 'post',
    data,
  });
};
// 删除权限（角色）
export const getRemoveUserAuthorityAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/ram/removeUserAuthority`, {
    method: 'post',
    data,
  });
};
// 删除权限（用户）
export const getRemoveRoleAuthorityAPI = (data: any, bool: boolean) => {
  return request(
    `${REQUESTADDRESS_W}${
      bool ? '/ram/removeDutyAuthority' : '/ram/removeRoleAuthority'
    }`,
    {
      method: 'post',
      data,
    },
  );
};
// 配置权限
export const getModifyUserAuthorityDataAPI = (
  id: string,
  path: string,
  data: any,
) => {
  return request(
    `${REQUESTADDRESS_W}/ram/modifyUserAuthorityData?uid=${id}&path=${path}`,
    {
      method: 'post',
      data,
      requestType: 'json',
    },
  );
};
// 角色管理详情
export const getRoleAPI = (params: any, bool: boolean) => {
  return request(
    `${REQUESTADDRESS_W}${bool ? '/ram/getDuty' : '/ram/getRole'}`,
    {
      method: 'get',
      params,
    },
  );
};
// 修改角色名字
export const getModifyRoleNameAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/modifyRoleName`, {
    method: 'get',
    params,
    // requestType: 'json'
  });
};
// 配置角色
export const getModifyRoleAuthorityDataAPI = (
  roleId: string,
  path: string,
  data: any,
  bool: boolean,
) => {
  return request(
    `${REQUESTADDRESS_W}${
      bool
        ? `/ram/modifyDutyAuthorityData?dutyId=${roleId}&path=${path}`
        : `/ram/modifyRoleAuthorityData?roleId=${roleId}&path=${path}`
    }`,
    {
      method: 'post',
      data,
      requestType: 'json',
    },
  );
};
export const getAllGroupListData = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/getAllGroupListData`, {
    method: 'get',
    params,
    // requestType: 'json'
  });
};
/*添加标签分组*/
export const addGroupData = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/addTagGroup`, {
    method: 'post',
    params,
    // requestType: 'json'
  });
};
/*删除分组*/
export const deleteGroupData = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/removeTagGroup`, {
    method: 'post',
    params,
    // requestType: 'json'
  });
};
/*添加标签*/
export const addTagData = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/addTag`, {
    method: 'post',
    params,
    // requestType: 'json'
  });
};
/*删除标签*/
export const deleteTag = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/removeTag`, {
    method: 'post',
    params,
    // requestType: 'json'
  });
};
/*修改标签*/
export const editLabel = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/updateTag`, {
    method: 'post',
    params,
  });
};
/*修改分组*/
export const editGroup = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/updateTagGroup`, {
    method: 'post',
    params,
  });
};
/*批量添加业务标签*/
export const batchAddTag = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/addTagToUser`, {
    method: 'post',
    params,
    requestType: 'json',
  });
};
/*批量删除标签*/
export const batchDelateTag = (params: any) => {
  return request(`${REQUESTADDRESS_W}/ram/removeTagFromUser`, {
    method: 'post',
    params,
    requestType: 'json',
  });
};
