import { request } from '@umijs/max';
import { REQUESTADDRESS_W } from '@/globalData';
/* 获取用户登录token  */
export const getIdRuleList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/id/getList`, {
    method: 'GET',
    params,
  });
};
export const delIdRule = (params: any) => {
  return request(`${REQUESTADDRESS_W}/id/remove`, {
    method: 'GET',
    params,
  });
};

export const createIdRule = (data: any) => {
  return request(`${REQUESTADDRESS_W}/id/create`, {
    method: 'POST',
    data,
  });
};
export const updateIdRule = (params: any) => {
  return request(`${REQUESTADDRESS_W}/id/update`, {
    method: 'POST',
    data: params,
  });
};
export const testIdRule = (params: any) => {
  return request(`${REQUESTADDRESS_W}/id/test`, {
    method: 'POST',
    data:params,
  });
};
