import { request } from '@umijs/max';
import { REQUESTADDRESS_W } from '@/globalData';
/**
 * 内陆转运
 */
// 内部仓库转运单列表
export const getInnerListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/inner/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 内部仓库转运单 按运单创建
export const innerAddWaybillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/inner/addWaybill`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 内部仓库转运保存
export const getInnerSaveAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/inner/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 确认收货
export const getConfirmReceiptAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/inner/confirmReceipt`, {
    method: 'post',
    params,
  });
};
// 新建/修改
export const getRevokeAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/inner/revoke`, {
    method: 'post',
    params,
  });
};
// 内部仓库转运单详情
export const getInnerDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/inner/getDetail`, {
    method: 'get',
    params,
  });
};
// 提货记录到货/异常/更改状态
export const getConfirmDriverRecordAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/intermodal/inner/record/confirmDriverRecord`,
    {
      method: 'post',
      data,
    },
  );
};
// 服务商列表
export const getLogisticsProviderList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/logisticsProvider/list`, {
    method: 'get',
    params,
  });
};
// 安排-修改送货/提货
export const getRecordSave = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/inner/record/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 内部仓库转运单导出
export const getDownloadAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/inner/download`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};
// 代理仓库转运单列表
export const getAgentListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/agent/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
//  查询服务商（同行专线/商业快递）
export const getListProviderAndProductAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/product/listProviderAndProduct`, {
    method: 'get',
    params,
  });
};
//  批量导入运单号
export const checkExistsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/checkExists`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
//  批量导入提单号
export const checkBlnoExistsAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/blno/checkExists`, {
    method: 'post',
    data,
  });
};

//  代理仓库转运单运单选择器
export const getWaybillListAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/agent/getWaybillList`, {
    method: 'get',
    params,
  });
};

//  代理仓库转运单运单选择器（配仓那边过去的）
export const getgetWaybillListByWaybillIdsAPI = (params: any) => {
  return request(
    `${REQUESTADDRESS_W}/intermodal/agent/getWaybillListByWaybillIds`,
    {
      method: 'get',
      params,
    },
  );
};

// return request(`${REQUESTADDRESS_W}/intermodal/agent/getWaybillListByWaybillIds`, {
//   method: 'get',
//   params
// })
// }
// 代理仓库转运保存
export const getAgentSave = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/agent/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 代理仓库转运 安排司机
export const getDriver = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/agent/setDriver`, {
    method: 'post',
    data,
  });
};
// 代理仓库转运安排/修改送货
export const getSave = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/agent/record/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 代理仓库撤销
export const getAgentRevokeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/agent/revoke`, {
    method: 'post',
    data,
  });
};
// 代理仓库 确认收货
export const getAgentConfirmReceiptAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/agent/confirmReceipt`, {
    method: 'post',
    data,
  });
};
// 快递货物交接单
export const getHandoverDownload = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/agent/handover/download`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};
// 专线货物交接单
export const getDedicatedLineDownload = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/intermodal/agent/dedicatedLine/download`,
    {
      method: 'post',
      data,
      responseType: 'blob',
      getResponse: true,
      requestType: 'json',
    },
  );
};
// 代理仓库转运单详情
export const getAgentDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/agent/getDetail`, {
    method: 'get',
    params,
  });
};
// 物流服务商保存
export const getLogisticsProviderSaveAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/logisticsProvider/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 物流服务商删除
export const getLogisticsProviderDeleteAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/logisticsProvider/delete`, {
    method: 'post',
    data,
  });
};
// 物流服务商状态改变
export const getLogisticsProviderChangeEnabledAPI = (data: any) => {
  return request(
    `${REQUESTADDRESS_W}/intermodal/logisticsProvider/changeEnabled`,
    {
      method: 'post',
      data,
    },
  );
};
// 物流服务商详情
export const getLogisticsProviderDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/intermodal/logisticsProvider/getDetail`, {
    method: 'get',
    params,
  });
};
