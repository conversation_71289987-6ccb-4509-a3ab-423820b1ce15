import { request } from '@umijs/max';
import { REQUESTADDRESS_W, REQUESTADDRESS } from '@/globalData';
/**
 *
 *
 * 取货单
 */
/* 取货单列表 */
export const carriersListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/list`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 取货单详情
export const carriersDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/getDetail`, {
    method: 'get',
    params,
  });
};
// 接受变更
export const carriersAcceptChangeAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/acceptChange`, {
    method: 'post',
    data,
  });
};
// 转交业务员
export const carrierStransferSalesmanAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/transferSalesman`, {
    method: 'post',
    data,
  });
};
// 撤销取货单
export const carrierCancelOrderAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/cancelOrder`, {
    method: 'post',
    data,
  });
};
// 司机列表
export const carrierDriverAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/picking/driver/list`, {
    method: 'get',
    params,
  });
};
// 调度部-司机管理-操作
export const carrierChangeStateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/driver/changeState`, {
    method: 'post',
    data,
  });
};
// 创建取货单
export const carrierOrderSaveAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/save`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 获取客户列表 */
export const getClientListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/client/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 查询运单列表 */
// export const getWaybillListAPI = (data: any) => {
//   return request(`${REQUESTADDRESS_W}/waybill/tmsList`, {
//     method: 'post',
//     data,
//   });
// }
export const getWaybillListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tmsListV2`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

export const getWaybillListV3API = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tmsListV3`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
// 轨迹筛选
export const getSameTrackListByWaybillNosAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/track/getSameTrackListByWaybillNos`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
// 修改报关类型
export const batchModifyDeclarationAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchModifyDeclaration`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
// 轨迹筛选后的删除
export const removeTrackByGroupIdsAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/track/removeTrackByGroupIds`, {
    method: 'get',
    params,
    // requestType: 'json',
  });
};
// 批量打印发票
export const downloadInvoicesAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/downloadPdfInvoices`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};
//批量修改渠道签出
export const batchModifyChannelAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifySignOutChannel`, {
    method: 'post',
    data,
  });
};
// 修改客户
export const modifyClientAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifyClient`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
// 修改业务员
export const modifySalesmanAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/modifySalesman`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
export const getPieceAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/listFbaSummary/batch`, {
    method: 'get',
    params,
    // requestType: 'json'
  });
};
// 运单列表导出
export const exportWaybillListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/exportWaybill`, {
    method: 'post',
    data,
    responseType: 'blob',
    requestType: 'json',
    getResponse: true,
  });
};
//付款管理导出
export const exportPaymentRecordAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/finance/exportPaymentRequire`, {
    method: 'post',
    data,
    responseType: 'blob',
    requestType: 'json',
    getResponse: true,
  });
};
// 审核
export const auditAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/audit`, {
    method: 'post',
    data,
  });
};
// 仓库地址列表
export const identityAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/identity`, {
    method: 'get',
    params,
  });
};
// 增派司机-更改司机-分配司机
export const assignDriverAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/assignDriver`, {
    method: 'post',
    data,
  });
};
// 确认到货-货物异常-更改状态
export const confirmDriverRecordAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/confirmDriverRecord`, {
    method: 'post',
    data,
  });
};
// 取货完成
export const confirmOrderAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/confirmOrder`, {
    method: 'post',
    data,
  });
};
// 绑定运单，与 修改运单
export const bindWaybillAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/picking/order/bindWaybill`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 卡派试算迁移过来的接口*/
/* 报价记录列表 */
export const getPriceList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/getPriceRecordList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 报价记录详情 */
export const getPriceDetail = (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/getPriceRecord`, {
    method: 'post',
    data,
  });
};
/* 修改记录的成功状态 */
export const getRecordSucceeded = (params: any) => {
  return request(`${REQUESTADDRESS_W}/truck/recordSucceeded`, {
    method: 'get',
    params,
  });
};
/* 获取渠道 */
export const getChannelAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/truck/getChannelList`, {
    method: 'get',
    params,
  });
};
/* 卡派试算 */
export const getCardEstimateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/tryCal`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 保存报价记录 */
export const saveQuoteRecordAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/savePriceRecord`, {
    method: 'post',
    data,
  });
};
