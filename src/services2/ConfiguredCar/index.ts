import { request } from '@umijs/max';
import { REQUESTADDRESS_W } from '@/globalData';
import AssignSuppliersModal from '@/pages/PreConfiguredCar/Modal/AssignSuppliersModal';
/* 获取预配车列表  */
export const getUnconfiguredWaybillList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/blno/getUnconfiguredWaybillList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 获取配车列表  */
export const getPreList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/shipment/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 获取派送列表  */
export const getReadyList = (data: any) => {
  return request(`${REQUESTADDRESS_W}/shipment/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*创建预配车*/
export const creatShipment = (data: any) => {
  return request(`${REQUESTADDRESS_W}/shipment/create`, {
    method: 'post',
    data,
  });
};

/*获取预配车详情*/
export const getShipmentDetail = (data: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/shipment/getDetail`, {
    method: 'post',
    data,
  });
};
/*获取明细里货物信息*/
export const getWaybillList = (data: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/shipment/getWaybillList`, {
    method: 'post',
    data,
  });
};
/*获取明细里托盘信息*/
export const getPalletsList = (data: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/shipment/getPalletsList`, {
    method: 'post',
    data,
  });
};
/*获取明细未到货明细*/
export const getUnreceivedWaybillList = (data: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/shipment/getUnreceivedWaybillList`, {
    method: 'post',
    data,
  });
};
/*获取货物列表*/
export const getBoxList = (data: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/blno/getWaybillList`, {
    method: 'post',
    requestType: 'json',
    data,
  });
};
/*获取托盘列表*/
export const getPallets = (data: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/pallets/getList`, {
    method: 'post',
    requestType: 'json',
    data,
  });
};
/*获取托盘详情*/
export const getPalletsDetail = (data: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/pallets/get`, {
    method: 'post',
    requestType: 'json',
    data,
  });
};
/*获取货物详情*/
export const getCargoDetail = (data: { id: string }) => {
  return request(`${REQUESTADDRESS_W}/blno/getWaybill`, {
    method: 'post',
    data,
  });
};

/*预配配车*/
export const appointTimeApi = (data: {
  id: string;
  appointTime: string;
  remark?: string;
  isa?: string;
}) => {
  return request(`${REQUESTADDRESS_W}/shipment/appointTime`, {
    method: 'post',
    data,
  });
};
/*指派承运商*/
export const AssignSuppliers = (data: {
  id: string;
  appointTime: string;
  remark?: string;
  isa?: string;
}) => {
  return request(`${REQUESTADDRESS_W}/shipment/arrangeDelivery`, {
    method: 'post',
    data,
  });
};
/*详情添加包裹*/
export const appendPieces = (data: { shipmentId: any; pieceIds: string }) => {
  return request(`${REQUESTADDRESS_W}/shipment/appendPieces`, {
    method: 'post',
    data,
  });
};
/*详情预配车中移除包裹*/
export const removePieces = (data: { shipmentId: any; pieceIds: string }) => {
  return request(`${REQUESTADDRESS_W}/shipment/removePieces`, {
    method: 'post',
    data,
  });
};
/*一键移除包裹*/
export const clearUnreceivedPieces = (data: { shipmentId: string }) => {
  return request(`${REQUESTADDRESS_W}/shipment/clearUnreceivedPieces`, {
    method: 'post',
    data,
  });
};

/*追加托盘*/
export const appendPallets = (data: {
  shipmentId: any;
  palletsIds: string;
}) => {
  return request(`${REQUESTADDRESS_W}/shipment/appendPallets`, {
    method: 'post',
    data,
  });
};
/*移除托盘*/
export const removePallets = (data: {
  shipmentId: any;
  palletsIds: string;
}) => {
  return request(`${REQUESTADDRESS_W}/shipment/removePallets`, {
    method: 'post',
    data,
  });
};
/*移除托盘*/
export const createPallets = (data: { pieceIds: string }) => {
  return request(`${REQUESTADDRESS_W}/pallets/create`, {
    method: 'post',
    data,
  });
};
/*货物出库*/
export const outboardApi = (data: { pieceIds: string }) => {
  return request(`${REQUESTADDRESS_W}/shipment/outboard`, {
    method: 'post',
    data,
  });
};
/*确认送达*/
export const determineDeliveried = (data: { pieceIds: string }) => {
  return request(`${REQUESTADDRESS_W}/shipment/deliveried`, {
    method: 'post',
    requestType: 'json',
    data,
  });
};
/*打印装车码*/
export const printLabel = (params) => {
  return request(`${REQUESTADDRESS_W}/shipment/getLabel`, {
    method: 'get',
    params,
  });
};
/*获取派送详情*/
export const getPieceList = (params) => {
  return request(`${REQUESTADDRESS_W}/blno/getPieceList`, {
    method: 'post',
    params,
  });
};

/* 导出派送单，装车单 */
export const exportShipmentDetail = async (data: any) => {
  return request(`${REQUESTADDRESS_W}/shipment/exportShipmentDetail`, {
    method: 'POST',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
    transformRequest: [(data) => Qs.stringify(data, { indices: false })],
  });
};
/*合并托盘*/
export const mergePallets = (params) => {
  return request(`${REQUESTADDRESS_W}/pallets/merge`, {
    method: 'post',
    params,
  });
};

/*获取地铁*/
export const getWarehouseMap = (params) => {
  return request(`${REQUESTADDRESS_W}/warehouse/getSlotList`, {
    method: 'post',
    params,
  });
};
/*批量上架*/
export const mergeListing = (params) => {
  return request(`${REQUESTADDRESS_W}/pallets/store`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/*sku汇总*/
export const getSummarizedList = (params) => {
  return request(`${REQUESTADDRESS_W}/good/getSummarizedList`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/*sku汇总*/
export const getPurchaseOrderListAPI = (params) => {
  return request(`${REQUESTADDRESS_W}/material/getPurchaseOrderList`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};

/*物料管理列表*/
export const getSummarizedListSelf = (params) => {
  return request(`${REQUESTADDRESS_W}/good/getSummarizedList?self=1`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/*出库列表*/
export const getOutBoundOrderListAPI = (params) => {
  return request(`${REQUESTADDRESS_W}/material/getOutBoundOrderList`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/*sku批次库存*/
export const getBatchGoodList = (params) => {
  return request(`${REQUESTADDRESS_W}/good/getBatchGoodList`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/*sku批次库位库存*/
export const getBatchSlotGoodList = (params) => {
  return request(`${REQUESTADDRESS_W}/good/getBatchSlotGoodList`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/*Sku批次库存流水*/
export const getSummarizedGoodLogList = (params) => {
  return request(`${REQUESTADDRESS_W}/good/getSummarizedGoodLogList`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/*Sku库位库存*/
export const getSlotGoodList = (params) => {
  return request(`${REQUESTADDRESS_W}/good/getSlotGoodList`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/*上架列表*/
export const getReceiveList = (params) => {
  return request(`${REQUESTADDRESS_W}/inbound/getReceiveList`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/*包材列表*/
export const getPackList = (params) => {
  return request(`${REQUESTADDRESS_W}/material/pack/getList`, {
    method: 'post',
    requestType: 'json',
    data: params,
  });
};
/*  SKU 列表 */
export const getListSku = (data: any) => {
  return request(`${REQUESTADDRESS_W}/sku/getList`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*  删除SKU */
export const removeSKUAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/sku/removeSku`, {
    method: 'get',
    params,
    // requestType: 'json',
  });
};
// 创建 SKU
export const saveSkuAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/sku/saveSku`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*  客户SKU 列表 */
export const getListOfClient = (data: any) => {
  return request(`${REQUESTADDRESS_W}/sku/getListOfClient`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*新增采购单*/
export const createPurchaseOrder = (data: any) => {
  return request(`${REQUESTADDRESS_W}/material/createPurchaseOrder`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/*新增出库*/
export const createOutboundOrder = (data: any) => {
  return request(`${REQUESTADDRESS_W}/material/createOutboundOrder`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
