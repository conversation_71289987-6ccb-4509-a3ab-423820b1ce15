import { useLocation } from 'umi';
import DetailsCard from '../../components/DetailsCard';
import service from '@/services/home';
import { useEffect, useState } from 'react';
const { getChannelProductDetailAPI } = service.UserHome;
const ChannelDetails = () => {
  const location = useLocation();
  const { state }: any = location;
  // console.log('详情接收的参数: ', location);
  /* 类型枚举 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const enumMa: any = {
  type: {
    0: 'GROUND',
    1: 'HWT',
    2: 'GFP',
  },
  measure:{
    0:'KG',
    1:'LB',
    2:'OZ',
  },
};
  const [objDetails, setObjDetails] = useState<any>({
    HWTdetails: [
      {
        title: '编号',
        content: '9527',
      },
    ],
    channelAttribute:[]
  });
  /* 获取产品详情 */
  const [loading,setLoading] = useState<boolean>(true);
  const getChannelProductDetail = async (id: number) => {
    setLoading(true);
    const res = await getChannelProductDetailAPI({
      id,
    });
    if (res.status) {
      const { data } = res;
      setObjDetails({
        HWTdetails: [
          {
            title: '编号',
            content: data.channelId,
          },
        ],
        channelAttribute:[
          {
            title: '国家',
            content: data.country,
          },
          {
            title: '类型',
            content: enumMa.type[data.type],
          },
          {
            title: '区域',
            content: data.region,
          },
          {
            title: 'GFP Freight Class最小值',
            content: data.gfp_freight_class_min,
          },
        ],
        sendingAddress:[
          {
            title: '国家',
            content: data?.sendAddress?.country,
          },
          {
            title: '街道门牌',
            content: data?.sendAddress?.street,
          },
          {
            title: '发件人',
            content: data?.sendAddress?.contactName,
          },
          {
            title: '州/省',
            content: data?.sendAddress?.province,
          },
          {
            title: '邮编',
            content: data?.sendAddress?.zipCode,
          },
          {
            title: '发件电话',
            content: data?.sendAddress?.contactPhone,
          },
          {
            title: '城市',
            content: data?.sendAddress?.city,
          },
        ],
        volumeInformationRestriction:[
          {
            title: '体积最长边大小（cm）',
            content: data.volume_longest_size,
          },
          {
            title: '单个包裹最大重量',
            content: data.single_package_weight_max,
          },
          {
            title: '一票最大总重量',
            content: data.waybill_total_weight_max,
          },
          {
            title: '体积第二长边大小（cm）',
            content: data.volume_2nd_longest_size,
          },
          {
            title: '单个包裹最小重量',
            content: data.single_package_weight_min,
          },
          {
            title: '一票最小总重量',
            content: data.single_package_weight_min,
          },
          {
            title: '体积第三边长大小（cm）',
            content: data.volume_3rd_longest_size,
          },
          {
            title: '包裹最小平均重量',
            content: data.package_average_weight_min,
          },
          {
            title: '重量单位',
            content: enumMa.measure[data.weight_unit],         
          },
          // {
          //   title: '包裹最长边+2* （宽+高）最大值（cm）',
          //   content: data.country,
          // },
        ],
        name: data.name,
      })
      setLoading(false);
    }
  };
  useEffect(() => {
    getChannelProductDetail(state.id);
  }, []);

  return (
    <>
      <DetailsCard title={objDetails?.name} list={objDetails.HWTdetails} loading={loading} />
      <DetailsCard title="渠道属性" list={objDetails.channelAttribute} loading={loading} />
      <DetailsCard title="发件地址" list={objDetails.sendingAddress} loading={loading} />
      <DetailsCard title="材积信息限制" list={objDetails.volumeInformationRestriction} loading={loading} />
    </>
  );
};
export default ChannelDetails;
