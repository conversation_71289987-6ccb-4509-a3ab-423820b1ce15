import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { history } from 'umi';
import service from '@/services/home';
import { Tag } from 'antd';

const { getChannelProductAPI } = service.UserHome;

// 定义表格数据项的类型
export type TableListItem = {
  id: number | string;
  name: string;
  containers: number;
  creator: string;
  status: any;
  createdAt: number;
  memo: string;
  type: number;
  desc: string;
  sendAddress: {
    country: string;
    province: string;
    city: string;
    street: string;
  };
};

// 枚举类型
const typeEnum: any = {
  0: 'GROUND',
  1: 'HWT',
  2: 'GFP',
};

// 定义表格列的配置项
const columns: ProColumns<TableListItem>[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    // 渲染单元格的内容
    // render: (text) => <a>{text}</a>,
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    // 渲染单元格的内容
    render: (text) => <a>{text}</a>,
  },
  {
    title: '类型',
    dataIndex: 'type',
    // 渲染单元格的内容
    render: (text, record) => {
      return <Tag color="volcano">{typeEnum[record.type]}</Tag>;
    },
  },
  {
    title: '产品特性',
    dataIndex: 'desc',
  },
  {
    title: '发件地址',
    dataIndex: 'sendAddress',
    // 渲染单元格的内容
    render: (text, record) => {
      if(!record.sendAddress) return <span>-</span>
      const { country, province, city, street } = record?.sendAddress;
      return <span>{`${country}-${province}-${city}-${street}`}</span>;
    },
  },
  {
    title: '操作',
    width: 180,
    key: 'option',
    valueType: 'option',
    // 渲染单元格的内容
    render: (text, record) => (
      <a
        key="link"
        onClick={() => {
          history.push(
            `/dataManagement/channelProducts/channelDetails`,
            record,
          );
        }}
      >
        详情
      </a>
    ),
  },
];

export default () => {
  return (
    <>
    <ProTable<TableListItem>
      // 设置表格的主键
      rowKey="id"
      // 设置分页器的配置项
      pagination={{
        pageSize: 10,
        showQuickJumper: true,
      }}
      // 设置表格的列配置项
      columns={columns}
      // 不显示表格的搜索框
      search={false}
      // 设置日期格式化方式
      dateFormatter="string"
      // 设置请求数据的方式
      request={async (params) => {
        const { current: start, pageSize: len }:any = params;
        const res = await getChannelProductAPI({ start: (start - 1)*len, len });
        return {
          data: res.data.list || [],
          success: res.status,
          total: res.data.amount,
        };
      }}
      // 设置表格的配置项
      options={{
        fullScreen: true,
      }}
    />
    </>
  );
};
