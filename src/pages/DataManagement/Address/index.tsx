import { ProTable } from '@ant-design/pro-components';
import { Popconfirm, Tag, message } from 'antd';
// import React, { useState } from 'react';
import { formatTime } from '@/utils/format';
import AddAddress from './AddAddress';
import service from '@/services/home';
import { useRef } from 'react';
import { useAccess, Access } from 'umi';
const { getAddressInformationAPI, deleteAddressAPI } = service.UserHome;

/* 枚举指 */
const enumMa: any = {
  state: {
    0: '正常',
    1: '偏远',
    2: '超偏',
    3: '最偏'
  },
};

// const renderBadge = (count: number, active = false) => {
//   return (
//     <Badge
//       count={count}
//       style={{
//         marginBlockStart: -2,
//         marginInlineStart: 4,
//         color: active ? '#1890FF' : '#999',
//         backgroundColor: active ? '#E6F7FF' : '#eee',
//       }}
//     />
//   );
// };

export default () => {
  const access = useAccess();
  // const [activeKey, setActiveKey] = useState<React.Key>('tab1');
  const ref = useRef<any>();
  /* 刷新表格 */
  const refreshTable = () => {
    ref.current.reloadAndRest();
  };

  /* 删除地址 */
  async function deleteAddress(id: number) {
    const res = await deleteAddressAPI({
      id,
    });
    if (res.status) {
      refreshTable();
      message.success('删除成功');
    }
  }
  const columns: any = [
    {
      title: '查询',
      dataIndex: 'keyword',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入要查询的内容',
      },
    },
    {
      title: '助记码',
      dataIndex: 'mnemonicCode',
      width: 120,
      fixed: 'left',
      hideInSearch: true,
      // render: (_) => <Tag>{_}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'remoteLevel',
      hideInSearch: true,
      width: 120,
      render: (_text: any, row: any) => {
        return (
          <div style={{ display: 'flex' }}>
            {row.remoteLevel ? (
              <div>
                <Tag color="geekblue">{enumMa.state[row.remoteLevel]}</Tag>
              </div>
            ) : (
              <Tag color="geekblue" style={{ opacity: 0 }}>
                测试
              </Tag>
              // ''
            )}
            {row.residential === 1 ? <Tag color="blue">私人</Tag> : ''}
          </div>
        );
      },
    },
    {
      title: 'FBA',
      dataIndex: 'isFBA',
      width: 90,
      hideInSearch: true,
      render: (_text: any, row: any) => {
        return <>{row.isFBA ? <Tag color="magenta">FBA</Tag> : '-'}</>;
      },
    },
    {
      title: '国家',
      dataIndex: 'country',
      width: 90,
      hideInSearch: true,
    },
    {
      title: '州/省',
      dataIndex: 'provinceShortName',
      width: 120,
      hideInSearch: true,
      render: (_text: any, row: any) => {
        return `${row.provinceShortName}-${row.province}`;
      },
    },
    {
      title: '城市',
      dataIndex: 'city',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '邮编',
      dataIndex: 'zipCode',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '街道门牌',
      dataIndex: 'street',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
      hideInSearch: true,
      width: 150,
    },
    {
      title: '电话',
      dataIndex: 'contactPhone',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '时间',
      dataIndex: 'updateTime',
      hideInSearch: true,
      width: 160,
      render: (text: any) => {
        return <span>{formatTime(text)}</span>;
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 120,
      fixed: 'right',
      valueType: 'option',
      //隐藏列
      hideInTable: access.AddressLibraryReadOnly(),
      render: (_: any, record: any) => {
        if (record) {
          return [
            <AddAddress
              key="Revised"
              Revised="Revised"
              dataList={record}
              refreshTable={refreshTable}
            />,
            <Popconfirm
              key="del"
              title="确认是否删除"
              description="确认要删除此条数据吗"
              onConfirm={() => deleteAddress(record.id)}
              okText="确认"
              cancelText="取消"
            >
              <a key="del" style={{ color: 'red' }}>
                删除
              </a>
            </Popconfirm>,
          ];
        } else {
          return null;
        }
      },
    },
  ];

  return (
    <>
    <ProTable
      actionRef={ref}
      columns={columns}
      request={async (params: any) => {
        const res = await getAddressInformationAPI({
          service_id: 'Waybill',
          start: (params.current - 1) * params.pageSize,
          len: params.pageSize,
          keyword: params.keyword,
        });
        return {
          data: res.data.list || [],
          success: res.status,
          total: res.data.amount,
        };
      }}
      toolbar={{
        // menu: {
        //   type: 'tab',
        //   activeKey: activeKey,
        //   items: [
        //     {
        //       key: 'tab1',
        //       label: (
        //         <span>发件地址库{renderBadge(99, activeKey === 'tab1')}</span>
        //       ),
        //     },
        //     {
        //       key: 'tab2',
        //       label: (
        //         <span>FBA地址库{renderBadge(30, activeKey === 'tab2')}</span>
        //       ),
        //     },
        //     {
        //       key: 'tab3',
        //       label: (
        //         <span>系统地址库{renderBadge(30, activeKey === 'tab3')}</span>
        //       ),
        //     },
        //     {
        //       key: 'tab4',
        //       label: (
        //         <span>偏远邮编库{renderBadge(30, activeKey === 'tab3')}</span>
        //       ),
        //     },
        //   ],
        //   onChange: (key) => {
        //     setActiveKey(key as string);
        //   },
        // },
        actions: [
          <Access key="export" accessible={!access.AddressLibraryReadOnly()}>
            <AddAddress key="add" refreshTable={refreshTable} />
          </Access>,
        ],
      }}
      rowKey="id"
      pagination={{
        // pageSize: 10,
        defaultPageSize: 10,
        showQuickJumper: true,
      }}
      search={{
        labelWidth: 'auto',
      }}
      dateFormatter="string"
      options={{
        fullScreen: true,
        setting: {
          draggable: true,
          checkable: true,
          checkedReset: false,
          extra: [<a key="confirm">确认</a>],
        },
      }}
      scroll={{ x: 1200 }}
    />
    </>
  );
};
