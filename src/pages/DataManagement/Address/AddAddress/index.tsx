import { PlusOutlined } from '@ant-design/icons';
import {
    ModalForm,
    ProForm,
    ProFormRadio,
    ProFormSelect,
    // ProFormSelect,
    ProFormSwitch,
    ProFormText,
} from '@ant-design/pro-components';
import { Button, Col, Form, message, Modal, Row } from 'antd';
import styles from './index.less';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import service from '@/services/home';
import { useEffect, useState } from 'react';
import {getCountryList} from "@/services/home/<USER>";
const { addAddressAPI, modifyAddressAPI, addressFuzzySearchAPI } =
    service.UserHome;
export default (props: any) => {
    const { right } = props;
    const isRevised = props?.Revised;
    const { dataList, type } = props;
    const [form] = Form.useForm<any>();
    const [valInitail, setValInitail] = useState<any>({});

    // const addressChange = (value: any, selectedOptions: any) => {
    //   // console.log('value: ', value);
    //   // console.log('selectedOptions: ', selectedOptions);
    //   if (selectedOptions?.length === 3) {
    //     form.setFieldsValue({
    //       province: selectedOptions,
    //     });
    //   }
    //   // if(selectedOptions===undefined || selectedOptions === 'undefined'){
    //   //   form.setFieldsValue({
    //   //     country:'',
    //   //     province:'',
    //   //     city:''
    //   //   })
    //   // }
    // };
    const [defaultValue, setDefaultValue] = useState<any>('');
    /*强制提交开关*/
    const [isCancelOpen, setIsCancelOpen] = useState(false);
    const [isValidate, setIsValidate] = useState(false);

    useEffect(() => {
        if (isRevised) {
            // console.log('回显逻辑',dataList);
            /*setDefaultValue(
              `${dataList.country} - ${dataList.provinceShortName} / ${dataList.province} - ${dataList.city}`,
            );*/
            setDefaultValue(`${dataList.province}`);
        }
    }, []);
    /* 提交 */
    const onFinish = async (values: any) => {
        console.log('values: ', values);
        if (values.validate === 0) {
            return true;
        }
        //const province = JSON.parse(values.province.value);
        // message.success('提交成功');
        const { province2, ...other } = values;
        if (isValidate) {
            other.validate = 0;
            setIsValidate(false);
        }
        const { status, errorCode } = await addAddressAPI({
            ...other,
            service_id: 'Waybill',
            // isFBA: values.isFBA ? 1 : 0,
            //country: province.country,
            province: values.province2,
            //city: province.city,
        });
        if (status) {
            message.success('提交成功');
            setIsCancelOpen(false);
            props.refreshTable();
            return true;
        } else {
            if (errorCode === 'UPSValidateFailed') {
                setIsCancelOpen(true);
            }
            // message.error('提交失败');
            return false;
        }
    };
    const onReplyFinish = () => {
        setIsValidate(true);
        form.submit();
    };

    /* 修改只能修改这四个
      contactName
      contactPhone
      companyName
      mnemonicCode
    */
    const onRevised = async (values: any) => {
        // console.log('values: ', values);
        // message.success('提交成功');
        const { status } = await modifyAddressAPI({
            contactName: values.contactName,
            contactPhone: values.contactPhone,
            companyName: values.companyName,
            mnemonicCode: values.mnemonicCode,
            id: dataList.id,
        });
        if (status) {
            message.success('修改成功');
            props.refreshTable();
            return true;
        } else {
            // message.error('提交失败');
            return false;
        }
    };

    return (
        <div>
            <ModalForm
                title={isRevised ? '修改地址' : '添加地址'}
                trigger={
                    isRevised ? (
                        <a
                            onClick={() => {
                                form.setFieldsValue({
                                    ...dataList,
                                });
                                setValInitail({
                                    ...dataList,
                                });
                            }}
                        >
                            修改
                        </a>
                    ) : (
                        <Button
                            type={type === 'link' ? type : 'primary'}
                            style={{ marginRight: right ? right : 0 }}
                        >
              <span>
                <PlusOutlined />
                添加地址
              </span>
                        </Button>
                    )
                }
                //width={800}
                // labelCol={{ span: 3 }}
                // wrapperCol={{ span: 21 }}
                form={form}
                syncToInitialValues={false}
                labelWrap={true}
                autoComplete="off"
                autoFocusFirstInput
                modalProps={{
                    destroyOnClose: true,
                    onCancel: () => console.log('run'),
                    className: styles.wrap,
                }}
                layout="horizontal"
                submitTimeout={2000}
                onFinish={isRevised ? onRevised : onFinish}
                initialValues={
                    isRevised
                        ? {
                            ...valInitail,
                            province2: defaultValue, //单独配置回显逻辑
                        }
                        : {
                            residential: 1,
                            remoteLevel: 0,
                        }
                }
            >
                <ProFormSelect
                    name="province"
                    label="查询"
                    fieldProps={{
                        labelInValue: true,
                        style: {
                            minWidth: 140,
                            width: '90%',
                        },
                        filterOption: false,
                        onChange: (e) => {
                            console.log('JSON.parse(e?.value)', JSON.parse(e?.value));
                            const { zipCode, city, country, province } = JSON.parse(e?.value);
                            form.setFieldValue('zipCode', zipCode);
                            form.setFieldValue('city', city);
                            form.setFieldValue('country', country);
                            form.setFieldValue('province2', province);
                        },
                    }}
                    showSearch
                    disabled={isRevised}
                    placeholder="请选择，支持模糊搜索"
                    //rules={[{ required: true, message: '必填不能为空' }]}
                    debounceTime={300}
                    request={async ({ keyWords }) => {
                        const { status, data } = await addressFuzzySearchAPI({
                            token: keyWords,
                        });
                        if (status) {
                            //country - short_province_name / province - city
                            return data.list.map((item: any) => {
                                return {
                                    label: `${item.country} - ${item.provinceShortName} / ${item.province} - ${item.city} / ${item.zipCode}`,
                                    value: JSON.stringify(item),
                                };
                            });
                        } else {
                            return [];
                        }
                    }}
                />
                {/* <Form.Item
        label="国家"
        name="province"
        rules={[
          {
            required: true,
            message: '请选择完整地址',
          },
        ]}
      >
        <SelectionOfProvincesAndCities
          defaultValue2={defaultValue}
          disabled={isRevised}
          addressChange={addressChange}
        />
      </Form.Item> */}
                <Row>
                    <Col>
                        {' '}
                        <ProFormSelect
                            name="country"
                            style={{ minWidth: '196px' }}
                            label="国家"
                            rules={[{ required: true, message: '必填项不能为空' }]}
                            disabled={isRevised}
                            request={async ({ keyWords }) => {
                                const { status, data } = await getCountryList({
                                    start: 0,
                                    len: 200,
                                    keyword: keyWords,
                                });
                                if (status) {
                                    return data.list.map((item: any) => {
                                        return {
                                            label: `${item.name}-${item.cnName}-${item.code}`,
                                            value: item.code,
                                        };
                                    });
                                }
                                return [];
                            }}
                        />
                    </Col>
                    <Col>
                        {' '}
                        <ProFormText
                            name="province2"
                            label="州/省"
                            style={{ width: '90%' }}
                            labelCol={{ span: 9 }}
                            rules={[{ required: true, message: '必填项不能为空' }]}
                            disabled={isRevised}
                        />
                    </Col>
                </Row>
                <Row>
                    <Col>
                        {' '}
                        <ProFormText
                            name="city"
                            label="城市"
                            style={{ width: '90%' }}
                            rules={[{ required: true, message: '必填项不能为空' }]}
                            disabled={isRevised}
                        />
                    </Col>
                    <Col>
                        {' '}
                        <ProFormText
                            name="zipCode"
                            label="邮编"
                            rules={[{ required: true, message: '必填项不能为空' }]}
                            labelCol={{ span: 9 }}
                            style={{ width: '90%' }}
                            disabled={isRevised}
                        />
                    </Col>
                </Row>
                <ProFormText
                    name="street"
                    fieldProps={{
                        style: { width: '90%' },
                    }}
                    rules={[{ required: true, message: '必填项不能为空' }]}
                    label="街道门牌"
                    disabled={isRevised}
                />

                <ProForm.Group>
                    <ProFormText
                        name="contactName"
                        rules={[{ required: true, message: '必填项不能为空' }]}
                        label="联系人"
                    />
                    <ProFormText
                        name="contactPhone"
                        rules={[{ required: true, message: '必填项不能为空' }]}
                        label="电话"
                    />
                </ProForm.Group>
                <ProFormText
                    name="companyName"
                    label="公司名字"
                    fieldProps={{
                        style: { width: '90%' },
                    }}
                />
                <ProFormText
                    name="email"
                    label="电子邮箱"
                    fieldProps={{
                        style: { width: '90%' },
                    }}
                />
                <ProFormText
                    name="fax"
                    label="传真"
                    fieldProps={{
                        style: { width: '90%' },
                    }}
                />
                <ProFormRadio.Group
                    name="residential"
                    label="性质"
                    disabled={isRevised}
                    options={[
                        {
                            label: '民企',
                            value: 1,
                        },
                        {
                            label: '商业',
                            value: 0,
                        },
                    ]}
                />
                <ProFormText name="mnemonicCode" label="助记码" />
                <Modal
                    title="提示"
                    open={isCancelOpen}
                    okText={'继续添加'}
                    cancelText={'返回修改'}
                    onOk={onReplyFinish}
                    onCancel={() => setIsCancelOpen(false)}
                >
                    <p>来自UPS官方地址校验未通过，请核对地址准确性</p>
                </Modal>
            </ModalForm>
        </div>
    );
};
