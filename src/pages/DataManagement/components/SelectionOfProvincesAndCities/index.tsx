import React, { useEffect, useState } from 'react';
import { Cascader } from 'antd';

const SelectionOfProvincesAndCities = (props: any) => {
  const [options, setOptions] = useState<any>([]);

  const getUS = async () => {
    const response = await fetch(
      'https://web-common.kmfba.com/location/country.json',
    );
    const json = await response.json();
    const newJson = json.countries.map((item: any) => ({
      code: item,
      name: item,
      isLeaf: false,
      loading: false,
    }));
    setOptions(newJson);
  };

  const getProvinces: any = () => {
    return new Promise((resolve, reject) => {
      fetch('https://web-common.kmfba.com/location/us/provinces.json')
        .then((response) => {
          return response.json();
        })
        .then((json) => {
          resolve(json);
        })
        .catch((err) => {
          reject(err);
        });
    });
  };

  const getCities: any = (key: any) => {
    return new Promise((resolve, reject) => {
      fetch(`https://web-common.kmfba.com/location/us/cities/${key}.json`)
        .then((response) => {
          return response.json();
        })
        .then((json) => {
          resolve(json);
        })
        .catch((err) => {
          reject(err);
        });
    });
  };

  useEffect(() => {
    getUS();
  }, []);

  const onChange = (value: (string | number)[], selectedOptions: any) => {
    props?.addressChange?.(value, selectedOptions);
  };

  const loadData = (selectedOptions: any) => {
    const targetOption: any = selectedOptions[selectedOptions.length - 1];
    if (selectedOptions.length === 1) {
      targetOption.loading = true;
      getProvinces().then((res: any) => {
        res?.forEach((item: any) => {
          item.isLeaf = false;
        });
        targetOption.loading = false;
        targetOption.children = res;
        const newOptions = options.map((option: any) => {
          if (option.code === targetOption.code) {
            return targetOption;
          }
          return option;
        });
        setOptions(newOptions);
      });
    }
    if (selectedOptions.length === 2) {
      targetOption.loading = true;
      getCities(targetOption.code).then((res: any) => {
        const newList = res?.map((item: any) => {
          return {
            ...item,
            name: item.city + '/' + item.county,
            code: `${item.city}-${item.latitude}-${item.longitude}`,
          };
        });
        targetOption.loading = false;
        targetOption.children = newList;
        const newOptions = options.map((option: any) => {
          if (option.code === targetOption.code) {
            return targetOption;
          }
          return option;
        });
        setOptions(newOptions);
      });
    }
  };
  const filter = (inputValue: any, path: any) =>
  path.some((option:any) => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
  return (
    <Cascader
      options={options}
      loadData={loadData}
      onChange={onChange}
      showSearch={{
        filter,
      }}
      fieldNames={{ label: 'name', value: 'code' }}
      changeOnSelect
      placeholder={props.disabled ? props.defaultValue2 : '请选择地址'}
      disabled={props.disabled}
    />
  );
};

export default SelectionOfProvincesAndCities;
