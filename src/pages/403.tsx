import { Button, Result } from 'antd';
import React, { useEffect } from 'react';
// import { history as history2 } from 'umi';
const { REACT_APP_ENV } = process.env;
const NoFoundPage: React.FC = () => {
  /***
   * 这个方法后续抽出来 现在不确定域名
   * 需求是 三个独立域名项目 一个登录 一个前台一个后台
   * 登录成功跳转到对应的 前台或者后台  没有权限 返回登录  问题是都不同的项目
   */
  function urlPush() {
    //获取当前域名
    const host = window.location.href;
    let url: any;
    if (host.includes('localhost') || host.includes('3001')) {
      //如果是本地环境  跳转本地
      url = 'http://localhost:3000';
    }
    if (host.includes('dev-web-waybill')) {
      //如果是测试环境 跳转测试
      url = 'https://dev-web-home.kmfba.com/';
    }
    if (REACT_APP_ENV === 'pre') {
      //如果是生产环境 跳转生产
      url = 'https://staging-web-home.kmfba.com';
    }
    if (REACT_APP_ENV === 'production') {
      // const currentDomain = window.location.hostname;
      // if (currentDomain.includes('cloud')) {
      //   url = 'https://home.mingrui.cloud';
      // } else {
      //   url = 'https://home.kmfba.com/';
      // }
      url = 'https://home.kmfba.com/';
    }
    return url;
  }

  useEffect(() => {
    let time = 2;
    let timer = setInterval(() => {
      time--;
      if (time === 0) {
        clearInterval(timer);
        location.replace(urlPush());
      }
    }, 1000);
  }, []);
  return (
    <Result
      status="403"
      title="403"
      subTitle="没有权限请 先登陆."
      extra={
        <Button
          type="primary"
          onClick={() => {
            // history.pushState(null, '', '/');
            // history2.push('/')
            location.replace(urlPush());
          }}
        >
          返回登录页
        </Button>
      }
    />
  );
};

export default NoFoundPage;
