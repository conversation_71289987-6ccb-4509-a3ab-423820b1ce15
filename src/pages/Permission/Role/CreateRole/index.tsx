import { Button, Form, Input, Modal, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import service from '@/services/home';
import RolePermissionsTree from '../../components/RolePermissionsTree';
import React from 'react';
const {
  getPermissionCatalogAPI,
  createRoleAPI,
  getEditRolePermissionAPI,
  editRolePermissionAPI,
} = service.UserHome;
interface Props {
  disabled?: boolean | any; //后端接口返回的是字符串0或1...
  btnText: string; //按钮文本
  btnType: 'link' | 'primary'; //按钮类型
  isEdit?: boolean; //是否是编辑 true:编辑 false:创建
  refreshTable?: any; //刷新表格
  record?: any; //编辑时传入的数据
}
const layout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};
const CreateRole = ({
  disabled,
  btnText,
  btnType,
  refreshTable,
  isEdit,
  record,
}: Props) => {
  const [form] = Form.useForm();
  const formRef = useRef<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  /* 已选权限 */
  const [selectedAuth, setSelectedAuth] = useState<any>([]);
  /* 创建角色 */
  const createRole = async (value: any) => {
    try {
      const { status } = await createRoleAPI({
        ...value,
        authorities: selectedAuth?.join(',') || '',
      });
      if (status) {
        message.success('创建角色成功');
        setIsModalOpen(false);
        form.resetFields();
        setSelectedAuth([]);
        refreshTable();
      }
    } catch (err) {
      console.log('创建角色出错', err);
    }
  };
  /* 修改角色 */
  const editRole = async (value: any) => {
    try {
      const { status } = await editRolePermissionAPI({
        ...value,
        authorities: selectedAuth?.join(',') || '',
        roleId: record.id,
      });
      if (status) {
        message.success('修改角色成功');
        setIsModalOpen(false);
        form.resetFields();
        setSelectedAuth([]);
        refreshTable();
      }
    } catch (err) {
      console.log('修改角色出错', err);
    }
  };

  /* 提交 */
  const onFinish = (value: any) => {
    if (isEdit) {
      editRole(value);
    } else {
      createRole(value);
    }
  };
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    formRef.current.submit();
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  /* 获取角色权限目录 */
  const [roleAuthLst, setRoleAuthLst] = useState<any>([]);
  const getRoleAuthLst = async () => {
    try {
      const { status, data }: any = await getPermissionCatalogAPI({});
      if (status) {
        setRoleAuthLst([...data]);
      }
    } catch (err) {
      console.log('获取角色权限出错', err);
    }
  };
  /* 编辑时获取角色目录 */
  const getEditRoleAuthLst = async () => {
    try {
      const { status, data }: any = await getEditRolePermissionAPI({
        roleId: record.id,
      });
      if (status) {
        setRoleAuthLst([...data]);
      }
    } catch (err) {
      console.log('获取角色权限出错', err);
    }
  };

  //设置表单值
  useEffect(() => {
    form.setFieldsValue({
      authorities: selectedAuth,
    });
  }, [selectedAuth]);

  /* 是否是编辑 */
  useEffect(() => {
    if (!isModalOpen) return;
    if (isEdit) {
      getEditRoleAuthLst();
      form.setFieldsValue({
        name: record.name,
      });
    } else {
      getRoleAuthLst();
    }
  }, [isModalOpen]);

  return (
    <>
      <Button type={btnType} onClick={showModal} disabled={disabled}>
        {btnText}
      </Button>
      <Modal
        title="角色信息"
        width={800}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        forceRender
      >
        <>
          <Form
            {...layout}
            ref={formRef}
            form={form}
            onFinish={onFinish}
            name="control-ref"
          >
            <Form.Item
              name="name"
              label="角色名称"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input style={{ width: '50%' }} placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="authorities"
              label="分配权限"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <RolePermissionsTree
                treeData={roleAuthLst}
                setSelectedAuth={setSelectedAuth}
              />
            </Form.Item>
          </Form>
        </>
      </Modal>
    </>
  );
};

export default React.memo(CreateRole);
