// import { DownOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import service from '@/services/home';
import { Button, Modal, Switch, message } from 'antd';
import { useRef, useState } from 'react';
import CreateRole from './CreateRole';
import { useAccess, Access } from 'umi';
import { ExclamationCircleFilled } from '@ant-design/icons';
const { getRoleListAPI, enableRoleAPI, deleteRoleAPI } = service.UserHome;

const { confirm } = Modal;

export default () => {
  const access = useAccess();
  const actionRef = useRef<any>();
  /* 是否启用角色 */
  const enableRole = async (roleId: string, enabled: string | number) => {
    try {
      const { status } = await enableRoleAPI({
        roleId,
        enabled,
      });
      if (status) {
        message.success('角色状态修改成功');
      }
    } catch (err) {
      console.log('修改角色状态出错', err);
    }
  };
  /* 删除角色 */
  const deleteRole = async (roleId: string) => {
    try {
      const { status } = await deleteRoleAPI({
        roleId,
      });
      if (status) {
        message.success('角色删除成功');
        actionRef.current.reload();
      }
    } catch (err) {
      console.log('删除角色出错', err);
    }
  };
  /* 刷新表格 */
  const refreshTable = () => {
    actionRef.current.reload();
  };
  const [columns, setColumns] = useState<any>([
    {
      title: '角色名称',
      width: 200,
      dataIndex: 'name',
    },
    {
      title: '权限',
      dataIndex: 'authDesc',
      ellipsis: true,
    },
    {
      title: '角色状态',
      dataIndex: 'enabled',
      render: (text: any, record: any) => {
        return (
          <Switch
            disabled={record.editable !== 1 || access.RAMReadOnly()}
            checked={record.enabled}
            onChange={(e: any) => {
              const flage = e ? 1 : 0;
              record.enabled = flage;
              enableRole(record.id, flage);
              setColumns([...columns]);
            }}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 120,
      fixed: 'right',
      valueType: 'option',
      hideInTable: access.RAMReadOnly(),
      render: (text: any, record: any) => [
        <CreateRole
          btnText="编辑"
          btnType="link"
          key="edit"
          refreshTable={refreshTable}
          isEdit={true}
          record={record}
          disabled={!record.enabled || record.editable !== 1}
        />,
        // <Button key="journal" type="link">
        //   日志
        // </Button>,
        <Button
          key="del"
          type="link"
          danger
          disabled={!record.enabled || record.editable !== 1}
          onClick={() => {
            confirm({
              title: '确认删除此角色吗',
              icon: <ExclamationCircleFilled />,
              content: `确认删除角色【${record.name}】吗？`,
              onOk() {
                deleteRole(record.id);
              },
              onCancel() {
                message.info('已取消删除');
              },
            });
          }}
        >
          删除
        </Button>,
      ],
    },
  ]);

  return (
    <>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        request={async (params: any) => {
          const msg = await getRoleListAPI({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
          });
          return {
            data: msg?.data?.list || [],
            success: msg.status,
            total: msg.data.amount || 0,
          };
        }}
        rowKey="id"
        pagination={{
          showQuickJumper: true,
          pageSize: 10,
        }}
        options={{
          fullScreen: true,
        }}
        search={false}
        dateFormatter="string"
        headerTitle="角色管理"
        toolBarRender={() => [
          <Access accessible={!access.RAMReadOnly()} key={1} fallback={null}>
            <CreateRole
              btnText="添加角色"
              btnType="primary"
              key="add"
              refreshTable={refreshTable}
              isEdit={false}
            />
          </Access>,
        ]}
      />
    </>
  );
};
