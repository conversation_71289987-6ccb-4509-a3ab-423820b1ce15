import { Tree } from 'antd';
import React, { useEffect } from 'react';
import { useState } from 'react';
interface Props {
  treeData: any; //角色权限根
  setSelectedAuth?: any; //设置已选权限
}
const RolePermissionsTree = ({ treeData, setSelectedAuth }: Props) => {
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  //展开/收起节点时触发
  const onExpand = (expandedKeysValue: any) => {
    // console.log('onExpand', expandedKeysValue);
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  const onCheck = (checkedKeysValue: any) => {
    /* 过滤节点 */
    const filteredArr = checkedKeysValue.filter((item: string) => {
      const parts = item.split(':');
      return parts.length !== 2;
    });
    setCheckedKeys(checkedKeysValue);
    setSelectedAuth(filteredArr);
  };
  const onSelect = (selectedKeysValue: any) => {
    // console.log('selectedKeysValue: ', selectedKeysValue);
    // console.log('selectedKeysValue: ', selectedKeysValue);
    // console.log('onSelect', info);
    setSelectedKeys(selectedKeysValue);
  };
  /* 根据treeData 设置选中的节点 回显逻辑 */
  /* 递归 */
  const getCheckedKeys = (treeData: any) => {
    let checkedKeysArr: any = [];
    const loop = (data: any) => {
      data.forEach((item: any) => {
        if (item.list && item.list.length) {
          loop(item.list);
        } else {
          if (item.assigned === 1) {
            checkedKeysArr.push(item.path);
          }
        }
      });
    };
    loop(treeData);
    return checkedKeysArr;
  };
  useEffect(() => {
    const newTreeData = getCheckedKeys(treeData);
    setCheckedKeys(newTreeData);
    setSelectedAuth(newTreeData);
  }, [treeData]);

  return (
    <Tree
      fieldNames={{ title: 'desc', key: 'path', children: 'list' }}
      checkable
      onExpand={onExpand} //展开/收起节点时触发
      expandedKeys={expandedKeys} //（受控）展开指定的树节点
      autoExpandParent={autoExpandParent} //是否自动展开父节点
      onCheck={onCheck} //点击复选框触发
      checkedKeys={checkedKeys} //（受控）选中复选框的树节点
      onSelect={onSelect} //点击树节点触发
      selectedKeys={selectedKeys} //受控）设置选中的树节点
      treeData={treeData}
    />
  );
};
export default React.memo(RolePermissionsTree);
