// import { DownOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import service from '@/services/home';
import { Button, Modal, message } from 'antd';
import NewUser from './NewUser';
import { useRef } from 'react';
import { useAccess, Access } from 'umi';
import { ExclamationCircleFilled } from '@ant-design/icons';
const { getUserListAPI, deleteUserAPI } = service.UserHome;
const { confirm } = Modal;

export default () => {
  const access = useAccess();
  const actionRef = useRef<any>();
  /* 刷新表格 */
  const refreshTable = () => {
    actionRef.current.reload();
  };
  /* 删除用户 */
  const deleteUser = async (uid: string | number) => {
    try {
      const { status } = await deleteUserAPI({
        uid,
      });
      if (status) {
        message.success('用户删除成功');
        actionRef.current.reload();
      }
    } catch (err) {
      console.log('删除用户出错', err);
    }
  };

  const columns: any = [
    {
      title: '邮箱地址',
      width: 200,
      dataIndex: 'email',
      search: false,
    },
    {
      title: '名称',
      dataIndex: 'user.name',
      render: (text: any, record: any) => {
        return record.user.name;
      },
    },
    {
      title: '角色',
      dataIndex: 'roleNames',
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'option',
      width: 120,
      fixed: 'right',
      valueType: 'option',
      hideInTable: access.RAMReadOnly(),
      render: (text: any, record: any) => [
        <NewUser
          key="edit"
          btnText="编辑"
          isEdit={true}
          btnType="link"
          refreshTable={refreshTable}
          record={record}
        />,
        <Button
          key="del"
          type="link"
          danger
          onClick={() => {
            confirm({
              title: '确认删除此用户吗?',
              icon: <ExclamationCircleFilled />,
              content: `用户：【${record.user.name}】`,
              onOk() {
                deleteUser(record.id);
              },
              onCancel() {
                message.info('已取消删除');
              },
            });
          }}
        >
          删除
        </Button>,
      ],
    },
  ];
  return (
    <>
    <ProTable
      actionRef={actionRef}
      columns={columns}
      request={async (params: any) => {
        const msg = await getUserListAPI({
          start: (params.current - 1) * params.pageSize,
          len: params.pageSize,
        });
        return {
          data: msg?.data?.list || [],
          success: msg.status,
          total: msg.data.amount || 0,
        };
      }}
      rowKey="id"
      pagination={{
        showQuickJumper: true,
        pageSize: 10,
      }}
      options={{
        fullScreen: true,
      }}
      search={false}
      dateFormatter="string"
      headerTitle="用户管理"
      toolBarRender={() => [
        <Access accessible={!access.RAMReadOnly()} key={1}>
          <NewUser
            key="key"
            btnText="添加用户"
            btnType="primary"
            refreshTable={refreshTable}
          />
        </Access>
      ]}
    />
    </>
  );
};
