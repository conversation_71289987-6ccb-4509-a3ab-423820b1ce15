import { Button, Form, Input, Modal, Select, message } from 'antd';
import { useEffect, useState } from 'react';
import service from '@/services/home';
const { getRoleListAPI, addUserAPI, editUserAPI } = service.UserHome;
interface Props {
  disabled?: boolean | any; //后端接口返回的是字符串0或1...
  btnText: string; //按钮文本
  btnType: 'link' | 'primary'; //按钮类型
  isEdit?: boolean; //是否是编辑 true:编辑 false:创建
  refreshTable?: any; //刷新表格
  record?: any; //编辑时传入的数据
}
const layout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};
const NewUser = ({
  btnText,
  btnType,
  disabled,
  refreshTable,
  record,
  isEdit,
}: Props) => {
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [options, setOptions] = useState<any>([]);

  /* 获取角色 */
  const getRolePermission = async () => {
    try {
      const { status, data } = await getRoleListAPI({});
      if (status) {
        setOptions([...data.list]);
      }
    } catch (err) {
      console.log('获取角色出错', err);
    }
  };

  /* 添加用户 */
  const addUser = async (values: any) => {
    try {
      const { status } = await addUserAPI({
        ...values,
        roleIds: values.roleIds?.join(',') || '',
      });
      if (status) {
        message.success('添加用户成功');
        setIsModalOpen(false);
        form.resetFields();
        refreshTable();
      }
    } catch (err) {
      console.log('添加用户出错', err);
    }
  };
  /* 编辑用户 */
  const editUser = async (values: any) => {
    try {
      const { status } = await editUserAPI({
        ...values,
        roleIds: values.roleIds?.join(',') || '',
        uid: record.id,
      });
      if (status) {
        message.success('编辑用户成功');
        setIsModalOpen(false);
        form.resetFields();
        refreshTable();
      }
    } catch (err) {
      console.log('编辑用户出错', err);
    }
  };
  // const handleChange = (value: any) => {
  //   console.log(`selected ${value}`);
  // };
  const onFinish = (values: any) => {
    if (isEdit) {
      editUser(values);
    } else {
      addUser(values);
    }
  };
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    if (record && isEdit && isModalOpen) {
      getRolePermission();
      form.setFieldsValue({
        name: record.user.name || '111',
        email: record.email,
        roleIds: record.roleIds?.split(',') || [],
      });
    }
  }, [record, isModalOpen]);

  useEffect(() => {
    if (isModalOpen) {
      getRolePermission();
    }
  }, [isModalOpen]);

  return (
    <>
      <Button type={btnType} onClick={showModal} disabled={disabled}>
        {btnText}
      </Button>
      <Modal
        title="用户信息"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={700}
        forceRender
      >
        <Form {...layout} form={form} onFinish={onFinish} name="control-ref">
          <Form.Item
            name="name"
            label="名称"
            rules={[
              {
                required: true,
                message: '请输入名称',
              },
            ]}
          >
            <Input
              style={{ width: '90%' }}
              placeholder="请输入"
              disabled={isEdit}
            />
          </Form.Item>
          {!isEdit && (
            <Form.Item
              name="email"
              label="邮箱地址"
              rules={[
                {
                  required: true,
                  message: '请输入邮箱地址',
                },
                {
                  pattern:
                    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                  message: '请输入正确的邮箱地址',
                },
              ]}
            >
              <Input
                style={{ width: '90%' }}
                placeholder="请输入"
                disabled={isEdit}
              />
            </Form.Item>
          )}
          <Form.Item
            name="roleIds"
            label="角色"
            rules={[
              {
                required: true,
                message: '请选择角色',
              },
            ]}
          >
            <Select
              mode="multiple"
              allowClear
              style={{
                width: '90%',
              }}
              placeholder="请选择"
              // onChange={handleChange}
              options={options}
              fieldNames={{ label: 'name', value: 'id' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
export default NewUser;
