import { ProTable } from '@ant-design/pro-components';
import { useRef } from 'react';
import { DatePicker } from 'antd';
import service from '@/services/home'
import { formatTime } from '@/utils/format'
const { getOperationLog } = service.UserHome;
const { RangePicker } = DatePicker;
const TableList = () => {
  const columns: any = [
    {
      title: '查询',
      dataIndex: 'keyword',
      hideInTable: true,
      fieldProps: {
        placeholder:'请输入要查询的内容'
      },
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      // copyable: true,
      // ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '描述',
      dataIndex: 'content',
      hideInSearch: true,
    },
    {
      title: '模块',
      dataIndex: 'serviceModuleDesc',
      hideInSearch: true,
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      hideInSearch: true,
    },
    {
      title: '时间',
      dataIndex: 'time',
      // valueType: 'date',
      sorter: true,
      hideInSearch: true,
      renderFormItem:()=><RangePicker showTime={{
        format: 'HH:mm',
      }} format="YYYY-MM-DD HH:mm" />,
      render:(text:any)=>{
        return <span>{formatTime(text)}</span>
      }
    },//
  ];
  const actionRef = useRef<any>();
  return (
    <>
    <ProTable<any>
      columns={columns}
      actionRef={actionRef}
      // cardBordered
      request={async (params:any = {}) => {
        const msg = await getOperationLog({
          start: (params.current - 1)*params.pageSize,
          len: params.pageSize,
          service_id:'Waybill',
          keyword: params.keyword,
        });
        // if(msg.status){

        // }
        return {
          data: msg?.data?.list || [],
          // success 请返回 true，
          // 不然 table 会停止解析数据，即使有数据
          success: msg.status,
          // 不传会使用 data 的长度，如果是分页一定要传
          total: msg.data.amount,
        };
      }}
      rowKey="id"
      search={{
        labelWidth: 'auto',
      }}
      options={{
        fullScreen: true,
      }}
      // form={{
      //   // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
      //   syncToUrl: (values, type) => {
      //     if (type === 'get') {
      //       return {
      //         ...values,
      //         created_at: [values.startTime, values.endTime],
      //       };
      //     }
      //     return values;
      //   },
      // }}
      
      pagination={{
        pageSize: 10,
        onChange: (page) => console.log(page),
      }}
      
    />
    </>
  );
}
export default TableList;