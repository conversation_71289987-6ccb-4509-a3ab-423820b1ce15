import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, Space, Tag, Switch } from 'antd';
import { useRef } from 'react';
import { request } from 'umi';

type GithubIssueItem = {
  url: string;
  id: number;
  number: number;
  title: string;
  labels: {
    name: string;
    color: string;
  }[];
  state: string;
  comments: number;
  created_at: string;
  updated_at: string;
  closed_at?: string;
};

const columns: ProColumns<GithubIssueItem>[] = [
  {
    title: '客户ID',
    dataIndex: 'state',
    width: 120,
  },
  {
    title: '名称',
    dataIndex: 'state',
  },
  // {
  //   title: '余额',
  //   dataIndex: 'state',
  // },
  // {
  //   title: '信用额',
  //   dataIndex: 'state',
  // },
  // {
  //   title: '押金',
  //   dataIndex: 'state',
  // },
  // {
  //   title: '预扣款',
  //   dataIndex: 'state',
  // },
  // {
  //   title: '汇率损补',
  //   dataIndex: 'state',
  // },
  // {
  //   title: '联系电话',
  //   dataIndex: 'state',
  // },
  {
    title: '操作',
    valueType: 'option',
    key: 'option',
    align: 'left',
    width: 250,
    render: (text, record, _, action) => [
      <a key="editable"> 财务管理 </a>,
      <a key="editable1"> 渠道管理 </a>,
      <a key="editable2"> 功能配置 </a>,
      <a key="editable3"> 编辑 </a>,
    ],
  },
];

export default () => {
  const actionRef = useRef<ActionType>();
  return (
    <ProTable<GithubIssueItem>
      rowKey="id"
      columns={columns}
      actionRef={actionRef}
      // headerTitle="角色管理"
      request={async (params = {}, sort, filter) => {
        console.log(sort, filter);
        const data = await request<{
          data: GithubIssueItem[];
        }>('https://proapi.azurewebsites.net/github/issues', {
          params,
        });
        console.log('data: ', data)
        return data
      }}
      columnsState={{
        persistenceKey: 'table-permission-customer',
        persistenceType: 'localStorage',
      }}
      options={{
        setting: {
          listsHeight: 400,
        },
      }}
      scroll={{ y: 'calc(100vh - 350px)' }}
      pagination={false}
      toolBarRender={() => [
        <Button key="button" icon={<PlusOutlined />}>
          添加角色
        </Button>,
      ]}
    />
  );
};
