import { Tabs } from 'antd';
import PriceRecord from './PriceRecord';
import PriceTrial from './PriceTrial';

const Calculate = () => {
  const onChange = (key: any) => {
    console.log(key);
  };
  const items = [
    {
      key: '价格试算',
      label: '价格试算',
      children: <PriceTrial/>,
    },
    {
      key: '报价记录',
      label: '报价记录',
      children: <PriceRecord/>,
    },
    
  ];
  return (
    <>
      <Tabs defaultActiveKey="价格试算" items={items} onChange={onChange} />
    </>
  );
};
export default Calculate;
