import MySearch from "@/components/MySearch"
import { getPriceList } from "@/services/home/<USER>"
import { formatTime } from "@/utils/format"
import { ProTable } from "@ant-design/pro-components"
import { history } from "@umijs/max"
import { Tag } from "antd"
import React, { useRef, useState } from "react"
const PriceRecord = React.memo(() => {
    const formRef = useRef<any>(null)
    const [keyword,setKeyword] = useState('')
    const columns: any = [
        {
            hideInTable: true,
            fieldProps: {
                placeholder: '请输入企业名称、ID搜索',
            },
            renderFormItem: () => {
                return <MySearch
                    placeholder="请输入运单号、子运单号、订单号查询"
                    allowClear
                    enterButton="搜索"
                    size="large"
                    style={{ width: '400px', background: '#FBFBFB' }}
                    onSearch={(e: any) => {
                          setKeyword(e)
                          formRef?.current?.submit()
                        //   refreshTabel()
                    }}
                />
            }
        },
        {
            title: '日期',
            dataIndex: 'createTime',
            align: 'center',
            width: 200,
            hideInSearch: true,
            render:(_:any,recode:any) => {
                return recode?.createTime ? formatTime(recode.createTime) : ''
            }
        },
        {
            title: '状态',
            dataIndex: 'success',
            align: 'center',
            width: 200,
            hideInSearch: true,
            render:(_:any,recode:any) => {
                return  <Tag color={successState[_]?.color}>{successState[_]?.text}</Tag>
            }
        },
        {
            title: '客户',
            dataIndex: 'clientName',
            align: 'center',
            width: 200,
            hideInSearch: true,
        },
        {
            title: '业务员',
            dataIndex: 'salesman',
            align: 'center',
            width: 200,
            hideInSearch: true,
        },
        {
            title: '总量',
            dataIndex: 'totalChargeAmount',
            align: 'center',
            width: 200,
            hideInSearch: true,
        },
        {
            title: '方数',
            dataIndex: 'totalVolume',
            align: 'center',
            width: 200,
            hideInSearch: true,
        },
        {
            title: '收件地址',
            dataIndex: 'address',
            align: 'center',
            width: 200,
            hideInSearch: true,
            render:(_:any,recode:any) => {
                return `${recode?.recipientAddress?.country} / ${recode?.recipientAddress?.city} / ${recode?.recipientAddress?.provinceShortName}`
            }
        },
        {
            title: '总价',
            dataIndex: 'totalChargeAmount',
            align: 'center',
            width: 200,
            hideInSearch: true,
        },
        {
            title: '试算员',
            dataIndex: 'user',
            key: 'user',
            width: 150,
            hideInSearch: true,
            align: 'center',
            render: (_: any, text: any) => {
              return (
                text?.user ?
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-evenly',
                  }}
                >
                  <img
                    style={{ width: 30, height: 30, borderRadius: '50%' }}
                    src={`https://static.kmfba.com/${_?.avatar}`}
                  />
                  <span>{_?.name}</span>
                </div>
               : '')
            },
          },
        {
            title: '操作',
            valueType: 'option',
            align: 'center',
            fixed: 'right',
            width: 200,
           render:(_:any,recode:any) => {
            return <a key='detail' onClick={() => {
                history.push('/waybillManagement/calculate/priceRecord/priceDetails',{
                    id:recode?.id
                })
            }}>详情</a>
           }
        },
    ]
    const successState:any = {
        0:{
            text:'已预报',
            color:'magenta'
        },
        1:{
            text:'已成交',
            color:'volcano'
        }
    }
    return  <ProTable<any>
            columns={columns}
            rowKey='id'
            formRef={formRef}
            request={async (params: any) => {
                const { current: start, pageSize: len } = params;
                const res = await getPriceList({
                    start: (start - 1) * len,
                    len,
                    keyword,
                })
                return {
                    data: res?.data?.list || [],
                    success:res?.data?.status,
                    total: res?.data?.total,
                };
            }}
            editable={{
                type: 'multiple',

            }}
            search={{
                labelWidth: 'auto',
                collapsed: false,
                optionRender: false
            }}
            options={false}
            dateFormatter="string"
            style={{ marginBottom: 20 }}
           />
    
})
export default PriceRecord
