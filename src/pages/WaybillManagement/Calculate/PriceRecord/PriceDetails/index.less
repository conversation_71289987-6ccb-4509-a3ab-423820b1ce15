.price_details {
    :global {
        .ant-card .ant-card-head {
            border-bottom: none !important;
        }
        .ant-card {
            width: 100%;
            margin-bottom: 10px;
        }
    }
    .price_top {
        display: flex;
        justify-content: space-between;
        width: 60%;
        margin-bottom: 24px;
        span {
            color: #AEAEAE;
        }
    }
    .price_state {
        display: flex;
        width: 100%;
        height: 140px;

        :first-child {
            margin-right: 24px;
        }

        >div {
            display: flex;
            flex-direction: column;
            width: 50%;
            height: 140px;
            background-color: #F4F4F4;
            box-sizing: border-box;
            padding: 8px;
            .address {
                width: 100%;
                height: 40px;
                // line-height: 40px;
                color: #707070;
            }

            .content {
                width: 100%;
                flex: 1;
                background-color: #fff;
                >div {
                    width: 100%;
                    height: 50%;
                    // line-height: 42px;
                    padding: 8px;
                    box-sizing: border-box;
                }
            }
        }
    }
}