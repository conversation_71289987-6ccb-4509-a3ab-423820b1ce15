import { Card, Switch, Table, Tag, message } from "antd"
import React, { useEffect, useState } from "react"
import styles from './index.less'
import { useLocation } from "@umijs/max"
import { getPriceDetail, getRecordSucceeded } from "@/services/home/<USER>"
import { formatTime } from "@/utils/format"
import { CheckOutlined, CloseOutlined } from "@ant-design/icons"
const PriceDetails = React.memo(() => {
  const {state}:any = useLocation()
  const [detail,setDetail] = useState<any>({})
  // const [checked,setChecked] = useState<boolean>(true)
    const columnsGoods: any = [
        {
          title: '件数',
          dataIndex: 'quantity',
          key: 'quantity',
          align:'center'
        },
        {
          title: '包装类型',
          dataIndex: 'packagingType',
          key: 'packagingType',
          align:'center'
        },
        {
          title: '密度',
          dataIndex: 'freightClass',
          align:'center',
          key: 'freightClass',
        },
        {
          title: '重量/kg',
          // title: (text:any) =>  {
          //   // console.log(text,'text',d);
            
          //   return '重量/kg'
          // },

          key: 'tags',
          align:'center',
          dataIndex: 'weight',
   
        },
        {
          title: '长/cm',
          key: 'action',
          align:'center',
          dataIndex: 'length',
  
        },
        {
          title: '高/cm',
          key: 'action',
          align:'center',
          dataIndex: 'height',
  
        },
        {
          title: '可堆叠',
          key: 'action',
          align:'center',
          dataIndex: 'stackable',
          render:(_:any,recode:any) => {
            return recode?.stackable ? <CheckOutlined style={{color:'#00CE3B'}}/> : <CloseOutlined style={{color:'#EC841A'}}/>
          }
          
  
        },
        {
          title: '可放倒',
          key: 'action',
          align:'center',
          dataIndex: 'turnable',
          render:(_:any,recode:any) => {
            return recode?.turnable ? <CheckOutlined style={{color:'#00CE3B'}}/> : <CloseOutlined style={{color:'#EC841A'}}/>
          }
          
  
        },
        {
          title: '货物说明',
          key: 'action',
          align:'center',
          dataIndex: 'contentsDescription',

          
  
        },
      ];
      const columnsGoodsDetail: any = [
        {
          title: '渠道',
          dataIndex: 'serviceLevel',
          key: 'serviceLevel',
          align:'center'

        },
        {
          title: '取货时间',
          dataIndex: 'deliveryDateStart',
          key: 'deliveryDateStart',
          align:'center',
          render:(_:any,recode:any) => {
            return `${recode?.pickupDateStart}-${recode?.pickupDateEnd}`
          }
        },
        {
          title: '送货时间',
          dataIndex: 'address',
          key: 'address',
          align:'center',
          render:(_:any,recode:any) => {
            return `${recode?.deliveryDateStart}-${recode?.deliveryDateEnd}`
          }

        },
        {
          title: '价格/美元',
          key: 'chargeAmount',
          dataIndex: 'chargeAmount',
          align:'center'

   
        },
        {
          title: '单价/人民币',
          key: 'price',
          dataIndex: 'price',
          align:'center'
  
        },
      ];
      useEffect(() => {
        if(state?.id) {
          getDetail(state?.id)
        }
      },[state?.id])
      const getDetail = async (id:string) => {
        try {
           const {status,data} = await getPriceDetail({
            id
           })
           if(status) {
            setDetail(data)
            // setChecked(detail?.success == 1)
           }
        } catch (error) {
          
        }
      }
      const rowSelection = {
        selectedRowKeys:detail?.priceItemSelected?.split(','),
        getCheckboxProps: (record: any) => ({
          disabled: true, // Column configuration not to be checked
          name: record.name,
        }),
      };
      // 修改记录的成功状态
      const getSucceeded = async(e:boolean) => {
        try {
          const {status} = await getRecordSucceeded({
            success:e ? 1 : 0,
            recordId:detail?.id
          })
          if(status) {
            message.success('操作成功')
            getDetail(state?.id)
          }
        } catch (error) {
          
        }
      }
    return <div className={styles.price_details}>
        <Card title="报价记录详情" style={{ width: '100%' }} >
            <div className={styles.price_top}>
                <div><span>客户：</span>{detail?.clientName}</div>
                <div><span>业务员：</span>{detail?.salesman}</div>
                <div><span>提货日期：</span>{detail?.createTime ? formatTime(detail.createTime) : ''}</div>
                <div><span>总报价：</span>{detail?.totalChargeAmount} 元</div>
                <div><span>单价：</span>{detail?.price} 元/kg</div>
                <div><span>{detail?.success == 1 ?  '已成交' : '已预报'}：</span><Switch checked={detail?.success == 1} onChange={(e) => {
                  getSucceeded(e)
                  // setChecked(e)
                }}/></div>
            </div>
            <div className={styles.price_state}>
                <div>
                    <div className={styles.address}>发件地址</div>
                    <div className={styles.content}>
                        {/* <div>US - CA / California - LOS ANGELES / 90001</div> */}
                        <div>{`${detail?.request?.destState} / ${detail?.request?.destCity} / ${detail?.request?.destZipCode}`}</div>
                        <div>
                        {detail?.request?.pickupLiftgate && <Tag color="magenta">叉车</Tag>}
                       {detail?.request?.pickupInside && <Tag color="red">仓库内搬货</Tag>}
                        {detail?.request?.pickupBlindShipment && <Tag color="volcano">盲送</Tag>}
                        {detail?.request?.pickupLimitAccess && <Tag color="orange">非商业公共场所</Tag>}
                        {detail?.request?.pickupAppt && <Tag color="gold">预约</Tag>}
                        </div>
                    </div>
                </div>
                <div>
                    <div className={styles.address}>收件地址</div>
                    <div className={styles.content}>
                        <div>{`${detail?.request?.originState} / ${detail?.request?.originCity} / ${detail?.request?.originZipCode}`}</div>
                        <div>
                        {detail?.request?.deliveryAppt && <Tag color="lime">预约</Tag>}
                       {detail?.request?.deliveryLiftgate &&  <Tag color="green">叉车</Tag>}
                       {detail?.request?.deliveryInside &&  <Tag color="cyan">仓库内搬货</Tag>}
                       {detail?.request?.deliveryLimitAccess &&  <Tag color="blue">非商业公共场所</Tag>}
                        {detail?.request?.deliveryNotify && <Tag color="geekblue">交货通知</Tag>}
                        {detail?.request?.deliverySortSegregate && <Tag color="purple">分拣分类</Tag>}
                        </div>
                    </div>
                </div>
            </div>
        </Card>
        <Card title="货物信息" style={{ width: '100%' }} >
        <Table columns={columnsGoods} dataSource={detail?.request?.itemList || []} pagination={false} rowKey='id' />
        </Card>
        <Card title="试算结果" style={{ width: '100%' }} >
        <Table columns={columnsGoodsDetail} dataSource={detail?.priceItemList || []} pagination={false}  rowKey='id'  rowSelection={{
          // type: 'checkbox',
          ...rowSelection,
        }}/>
        </Card>
    </div>
})
export default PriceDetails