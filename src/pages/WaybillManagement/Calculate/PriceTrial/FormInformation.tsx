import { addressFuzzySearchAPI, getChannelAPI } from '@/services/home/<USER>';
import { ProFormSelect } from '@ant-design/pro-components';
import { Checkbox, Col, DatePicker, Divider, Form, Input, Row, Select } from 'antd';
import React, { useEffect } from 'react';

interface Props {
  onChage?: (value: any) => void;
}

const layout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

const FormInformation = ({ onChage }: Props) => {
  const [form] = Form.useForm();
  const [channel, setChannel] = React.useState<any[]>([]); //渠道
  /* 获取渠道 */
  const getChannel = async () => {
    try {
      const { status, data } = await getChannelAPI({});
      if (status) {
        const channelList = data?.list?.map((item: any, index: any) => {
          return {
            label: item.name,
            value: item.carrierName,
            ids: index,
          };
        });
        setChannel(channelList || []);
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    getChannel();
  }, []);

  return (
    <>
      <Form
        form={form}
        onValuesChange={(changedValues, allValues) => {
          if (onChage) {
            onChage(allValues);
          }
        }}
        {...layout}
      >
        <Row>
          <Col span={12}>
            <Form.Item label="渠道" name="carrierName">
              <Select
                style={{ width: '60%' }}
                options={channel}
                placeholder="请选择渠道"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="提货时间" name="pickDate">
              <DatePicker style={{ width: '50%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Divider style={{ marginTop: 0 }} />
        <Row>
          <Col span={12}>
            <ProFormSelect
              name="originCityId"
              label="发件地址"
              fieldProps={{
                labelInValue: true,
                style: {
                  minWidth: 140,
                  width: '60%',
                },
                filterOption: false,
              }}
              showSearch
              placeholder="请选择，支持模糊搜索"
              // rules={[{ required: true, message: '必填不能为空' }]}
              debounceTime={300}
              request={async ({ keyWords }) => {
                const { status, data } = await addressFuzzySearchAPI({
                  token: keyWords,
                  country: 'us',
                });
                if (status) {
                  //country - short_province_name / province - city
                  return data.list.map((item: any) => {
                    return {
                      label: `${item.country} - ${item.provinceShortName} / ${item.province} - ${item.city} / ${item.zipCode}`,
                      // value: JSON.stringify(item),
                      value: item.id,
                    };
                  });
                } else {
                  return [];
                }
              }}
            />
          </Col>
        
          <Col span={12}>
            <Form.Item label="街区邮编" name="originZipCode">
              <Input
                style={{ width: '50%' }}
                placeholder="请输入发件地址邮编"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="类型" name="originZipType">
              <Select style={{ width: '60%' }} placeholder="请选择类型">
                <Select.Option value={0}>Business with Dock</Select.Option>
                <Select.Option value={1}>Business without Dock</Select.Option>
                <Select.Option value={2}>Residential</Select.Option>
                <Select.Option value={3}>Construction Site</Select.Option>
                <Select.Option value={4}>Trade Show</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row style={{paddingLeft:'20px'}}>
        <Col span={5}>
            <Form.Item label="" name="pickupLiftgate" valuePropName="checked">
              <Checkbox>叉车</Checkbox>
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item label="" name="ppickupInside" valuePropName="checked">
              <Checkbox>仓库内提货</Checkbox>
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item
              label=""
              name="pickupLimitAccess"
              valuePropName="checked"
            >
              <Checkbox>非商业公共场所</Checkbox>
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item
              label=""
              name="pickupBlindShipment"
              valuePropName="checked"
            >
              <Checkbox>盲送</Checkbox>
            </Form.Item>
          </Col>
        </Row>
        <Divider style={{ marginTop: 0 }} />
        <Row>
          <Col span={12}>
            <ProFormSelect
              name="destCityId"
              label="收件地址"
              fieldProps={{
                labelInValue: true,
                style: {
                  minWidth: 140,
                  width: '60%',
                },
                filterOption: false,
              }}
              showSearch
              placeholder="请选择，支持模糊搜索"
              // rules={[{ required: true, message: '必填不能为空' }]}
              debounceTime={300}
              request={async ({ keyWords }) => {
                const { status, data } = await addressFuzzySearchAPI({
                  token: keyWords,
                  country: 'us',
                });
                if (status) {
                  //country - short_province_name / province - city
                  return data.list.map((item: any) => {
                    return {
                      label: `${item.country} - ${item.provinceShortName} / ${item.province} - ${item.city} / ${item.zipCode}`,
                      // value: JSON.stringify(item),
                      value: item.id,
                    };
                  });
                } else {
                  return [];
                }
              }}
            />
          </Col>
       
          <Col span={12}>
            <Form.Item label="街区邮编" name="destZipCode">
              <Input
                style={{ width: '50%' }}
                placeholder="请输入收件地址邮编"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="类型" name="destZipType">
              <Select style={{ width: '60%' }} placeholder="请选择类型">
                <Select.Option value={0}>Business with Dock</Select.Option>
                <Select.Option value={1}>Business without Dock</Select.Option>
                <Select.Option value={2}>Residential</Select.Option>
                <Select.Option value={3}>Construction Site</Select.Option>
                <Select.Option value={4}>Trade Show</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row style={{paddingLeft:'20px'}}>
        <Col span={4}>
            <Form.Item label="" name="deliveryLiftgate" valuePropName="checked">
              <Checkbox>叉车</Checkbox>
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label="" name="deliveryInside" valuePropName="checked">
              <Checkbox>仓库内提货</Checkbox>
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label="" name="deliveryAppt" valuePropName="checked">
              <Checkbox>预约</Checkbox>
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item
              label=""
              name="deliverySortSegregate"
              valuePropName="checked"
            >
              <Checkbox>分拣/分类</Checkbox>
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label="" name="deliveryNotify" valuePropName="checked">
              <Checkbox>交货通知</Checkbox>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default React.memo(FormInformation);
