import { ProTable } from '@ant-design/pro-components';
import { Space, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import QuotationModal from './QuotationModal';
interface Props {
  trialResult?: any;
  trialObj?: any;
}
const TrialCalculationResults = ({ trialResult, trialObj }: Props) => {
  const [tableListDataSource, setTableListDataSource] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [maxPrice, setMaxPrice] = useState<any>({});
  const columns: any = [
    {
      title: '渠道',
      dataIndex: 'serviceLevel',
    },
    {
      title: '取货时间',
      dataIndex: 'pickupDateStart',
      render: (text: any, record: any) => {
        return record?.pickupDateStart + ' - ' + record?.pickupDateEnd;
      },
    },
    {
      title: '送货时间',
      dataIndex: 'pickupDateEnd',
      render: (text: any, record: any) => {
        return (
          <Space>
            <div>
              {record?.deliveryDateStart} - {record?.deliveryDateEnd}
            </div>
            <Tag color="orange">预估</Tag>
          </Space>
        );
      },
    },
    {
      title: '价格/美元',
      dataIndex: 'chargeAmount',
      defaultSortOrder: 'descend',
      sorter: (a: any, b: any) => a.chargeAmount - b.chargeAmount,
    },
    {
      title: '单价/(元/kg)',
      dataIndex: 'price',
    },
  ];

  useEffect(() => {
    if(trialResult.length){
      const list = trialResult
      //找出最大价格
      const maxPrice1 = list.reduce((prev:any, next:any) => {
        return (Number(prev.chargeAmount) > Number(next.chargeAmount)) ? prev : next;
      }, {});
      setMaxPrice(maxPrice1);
      setSelectedRowKeys([maxPrice1])
    }
    setTableListDataSource([...trialResult]);
  }, [trialResult]);
  return (
    <>
      <ProTable
        dataSource={tableListDataSource}
        rowKey="id"
        pagination={{
          showQuickJumper: true,
        }}
        rowSelection={{
          type: 'radio',
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRows);
            setMaxPrice(selectedRows[0]);
          },
          selectedRowKeys: [maxPrice?.id],
        }}
        columns={columns}
        search={false}
        dateFormatter="string"
        headerTitle="试算结果"
        options={false}
        toolBarRender={() => [
          <QuotationModal
            btnText="保存为报价单"
            key={1}
            selectedRowKeys={selectedRowKeys}
            btnType="primary"
            trialObj={trialObj}
          />,
        ]}
      />
    </>
  );
};

export default React.memo(TrialCalculationResults);
