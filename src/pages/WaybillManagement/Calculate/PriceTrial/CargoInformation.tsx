import {
  ProTable,
  // ProCard,
  // ProFormField,
} from '@ant-design/pro-components';
import { Button, Input, Select, Space, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import s from './index.less';
import classNames from 'classnames';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
interface Props {
  onChange?: (value: any) => void;
  setMeasures?: any;
}

type UnitType = 'kg' | 'lb' | 'cm' | 'in';

/* 单位转换 */
function convertUnit(value: any, from: UnitType, to: UnitType) {
  if (!value) return '';
  const conversionTable: any = {
    kg: {
      lb: 2.20462,
    },
    lb: {
      kg: 0.453592,
    },
    cm: {
      in: 0.393701,
    },
    in: {
      cm: 2.54,
    },
  };
  if (!conversionTable[from] || !conversionTable[from][to]) {
    throw new Error(`转换单位出错 ${from} to ${to}`);
  }
  if (value === '') return '';
  const result = value * conversionTable[from][to];
  return Number(result.toFixed(2));
}

export default ({ onChange, setMeasures }: Props) => {
  const [activeKey, setActiveKey] = useState('KG');
  const defaultData: any = [
    {
      id: 1,
    },
  ];
  // const [defaultData, setDefaultData] = useState<any>([
  //   {
  //     id: 1,
  //     density1: '',
  //   },
  // ]);
  const [dataSource, setDataSource] = useState(() => defaultData);
  function mapDensity(density: any) {
    if (density > 50) {
      return 50;
    } else if (density > 35) {
      return 55;
    } else if (density > 30) {
      return 60;
    } else if (density > 22.5) {
      return 65;
    } else if (density > 15) {
      return 70;
    } else if (density > 13.5) {
      return 77.5;
    } else if (density > 12) {
      return 85;
    } else if (density > 10.5) {
      return 92.5;
    } else if (density > 9) {
      return 100;
    } else if (density > 8) {
      return 110;
    } else if (density > 7) {
      return 125;
    } else if (density > 6) {
      return 150;
    } else if (density > 5) {
      return 175;
    } else if (density > 4) {
      return 200;
    } else if (density > 3) {
      return 250;
    } else if (density > 2) {
      return 300;
    } else if (density > 1) {
      return 400;
    } else {
      return 500;
    }
  }
  /* 根据重量 官渡高度长度 计算密度算法 */
  function calculateDensity(
    quantity: any,
    weightKg: any,
    lengthCm: any,
    widthCm: any,
    heightCm: any,
  ) {
    //如果入惨不存在
    if (!quantity || !weightKg || !lengthCm || !widthCm || !heightCm) {
      return;
    }

    let volumeM3: any;
    let weightLbs: any;

    if (activeKey === 'KG') {
      volumeM3 = (lengthCm * widthCm * heightCm) / 1e6; // 转换 cm³ 到 m³
      //单重改为总重
      weightLbs = (weightKg / quantity) * 2.2045; // 转换 kg 到 lbs
    } else {
      volumeM3 = lengthCm * widthCm * heightCm;
      //单重改为总重
      weightLbs = weightKg / quantity;
    }

    const D = weightLbs / (volumeM3 * 35);
    console.log('D: ', D);

    return mapDensity(D.toFixed(2));
  }
  /**
   * 密度映射
   * 超过50 50
   * 35-50 > 55
   * 30-35 > 60
   * 22.5-30 > 65
   * 15-22.5 > 70
   * 13.5-15 > 77.5
   * 12-13.5 > 85
   * 10.5-12 > 92.5
   * 9-10.5 > 100
   * 8-9 > 110
   * 7-8 > 125
   * 6-7 > 150
   * 5-6 > 175
   * 4-5 > 200
   * 3-4 > 250
   * 2-3 > 300
   * 1-2 > 400
   * 小于等于1 > 500
   */

  //更新
  // const update = () => {
  //   dataSource.forEach((item: any) => {
  //     item.density1 = calculateDensity(
  //       item?.weight,
  //       item?.length,
  //       item?.width,
  //       item?.height,
  //     );
  //   }
  //   );
  //   setDataSource([...dataSource]);
  // };

  const columns: any = [
    {
      title: '数量',
      dataIndex: 'quantity',
      width: 120,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
          {
            message: '只能是数字',
            pattern:
              /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/,
          },
        ],
      },
      render: (text: any, record: any) => {
        return (
          <Input
            allowClear
            value={record?.quantity}
            onChange={(e) => {
              record.quantity = e.target.value;
              record.freightClass = calculateDensity(
                e.target.value,
                record?.weight,
                record?.length,
                record?.width,
                record?.height,
              );
              setDataSource([...dataSource]);
            }}
            // status="error"
            // suffix={
            //   <Tooltip title="只能是数字" defaultOpen color="#fff">
            //     <CloseCircleOutlined style={{color:'red'}} />
            //   </Tooltip>
            // }
          />
        );
      },
    },
    {
      title: '包裹类型',
      key: 'packagingType',
      dataIndex: 'packagingType',
      valueType: 'select',
      width: 180,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
      render: (text: any, record: any) => {
        return (
          <Select
            allowClear
            style={{ width: '90%' }}
            value={record?.packagingType}
            options={[
              { label: 'Pallets (48x40)', value: 'PALLET_48x40' },
              { label: 'Pallets (48x48)', value: 'PALLET_48x48' },
              { label: 'Pallets (60x48)', value: 'PALLET_60x48' },
              { label: 'Pallets (Custom)', value: 'PALLET_OTHER' },
              { label: 'Bags', value: 'BAG' },
              { label: 'Bales', value: 'BALE' },
              { label: 'Boxes', value: 'BOX' },
              { label: 'Bundles', value: 'BUNDLE' },
              { label: 'Coils', value: 'COIL' },
              { label: 'Crates', value: 'CRATE' },
              { label: 'Cylinders', value: 'CYLINDER' },
              { label: 'Drums', value: 'DRUM' },
              { label: 'Pails', value: 'PAIL' },
              { label: 'Reels', value: 'REEL' },
              { label: 'Rolls', value: 'ROLL' },
              { label: 'Slipsheets', value: 'SLIP_SHEET' },
              { label: 'Tubes/Pipes', value: 'TUBE_OR_PIPE' },
              { label: 'Units', value: 'UNIT' },
            ]}
            onChange={(e) => {
              record.packagingType = e;
              setDataSource([...dataSource]);
            }}
          />
        );
      },
    },

    {
      title: <>总重/{activeKey}</>,
      dataIndex: 'weight',
      width: 120,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
          {
            message: '只能是数字',
            pattern:
              /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/,
          },
        ],
      },
      render: (text: any, record: any) => {
        return (
          <Input
            allowClear
            value={record?.weight}
            onChange={(e) => {
              record.weight = e.target.value;
              record.freightClass = calculateDensity(
                record?.quantity,
                e.target.value,
                record?.length,
                record?.width,
                record?.height,
              );
              setDataSource([...dataSource]);
            }}
          />
        );
      },
    },
    {
      title: <>长度/{activeKey === 'KG' ? 'CM' : 'IN'}</>,
      dataIndex: 'length',
      width: 120,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
          {
            message: '只能是数字',
            pattern:
              /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/,
          },
        ],
      },
      render: (text: any, record: any) => {
        return (
          <Input
            allowClear
            value={record?.length}
            onChange={(e) => {
              record.length = e.target.value;
              record.freightClass = calculateDensity(
                record?.quantity,
                record?.weight,
                e.target.value,
                record?.width,
                record?.height,
              );

              setDataSource([...dataSource]);
            }}
          />
        );
      },
    },
    {
      title: <>宽度/{activeKey === 'KG' ? 'CM' : 'IN'}</>,
      dataIndex: 'width',
      width: 120,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
          {
            message: '只能是数字',
            pattern:
              /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/,
          },
        ],
      },
      render: (text: any, record: any) => {
        return (
          <Input
            allowClear
            value={record?.width}
            onChange={(e) => {
              record.width = e.target.value;
              record.freightClass = calculateDensity(
                record?.quantity,
                record?.weight,
                record?.length,
                e.target.value,
                record?.height,
              );
              setDataSource([...dataSource]);
            }}
          />
        );
      },
    },
    {
      title: <>高度/{activeKey === 'KG' ? 'CM' : 'IN'}</>,
      dataIndex: 'height',
      width: 120,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
          {
            message: '只能是数字',
            pattern:
              /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/,
          },
        ],
      },
      render: (text: any, record: any) => {
        return (
          <Input
            allowClear
            value={record?.height}
            onChange={(e) => {
              record.height = e.target.value;
              record.freightClass = calculateDensity(
                record?.quantity,
                record?.weight,
                record?.length,
                record?.width,
                e.target.value,
              );
              setDataSource([...dataSource]);
            }}
          />
        );
      },
    },
   
    // {
    //   title: '计算密度',
    //   dataIndex: 'density1',
    //   width: 120,
    //   // renderFormItem: (e: any, { record }: any) => {
    //   //   return <>{calculateDensity(
    //   //     record?.weight,
    //   //     record?.length,
    //   //     record?.width,
    //   //     record?.height,
    //   //   )}</>
    //   // },
    // },
    {
      title: '密度',
      dataIndex: 'freightClass',
      width: 120,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
      valueType: 'select',
      render: (text: any, record: any) => {
        return (
          <Select
            allowClear
            style={{ width: '90%' }}
            value={record?.freightClass}
            options={[
              { label: '50', value: '50' },
              { label: '55', value: '55' },
              { label: '60', value: '60' },
              { label: '65', value: '65' },
              { label: '70', value: '70' },
              { label: '77.5', value: '77.5' },
              { label: '85', value: '85' },
              { label: '92.5', value: '92.5' },
              { label: '100', value: '100' },
              { label: '110', value: '110' },
              { label: '125', value: '125' },
              { label: '150', value: '150' },
              { label: '175', value: '175' },
              { label: '200', value: '200' },
              { label: '250', value: '250' },
              { label: '300', value: '300' },
              { label: '400', value: '400' },
              { label: '500', value: '500' },
            ]}
            onChange={(e) => {
              record.freightClass = e;
              setDataSource([...dataSource]);
            }}
          />
        );
      },
    },
    {
      title: '说明',
      dataIndex: 'contentsDescription',
      width: 160,
      render: (text: any, record: any) => {
        return (
          <Input
            allowClear
            value={record?.contentsDescription}
            onChange={(e) => {
              record.contentsDescription = e.target.value;
              setDataSource([...dataSource]);
            }}
            status={record?.contentsDescription ? 'success' : ('error' as any)}
            suffix={
              <Tooltip title="必填项">
                {record?.contentsDescription ? (
                  <CheckCircleOutlined style={{ color: 'green' }} />
                ) : (
                  <CloseCircleOutlined style={{ color: 'red' }} />
                )}
              </Tooltip>
            }
          />
        );
      },
    },
    {
      title: '可堆叠',
      dataIndex: 'isStackable',
      valueType: 'select',
      width: 120,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
      render: (text: any, record: any) => {
        return (
          <Select
            allowClear
            style={{ width: '90%' }}
            value={record?.isStackable}
            options={[
              { label: '是', value: true },
              { label: '否', value: false },
            ]}
            onChange={(e) => {
              record.isStackable = e;
              setDataSource([...dataSource]);
            }}
          />
        );
      },
    },
    {
      title: '可放倒',
      dataIndex: 'isTurnable',
      valueType: 'select',
      width: 120,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
      render: (text: any, record: any) => {
        return (
          <Select
            allowClear
            style={{ width: '90%' }}
            value={record?.isTurnable}
            options={[
              { label: '是', value: true },
              { label: '否', value: false },
            ]}
            onChange={(e) => {
              record.isTurnable = e;
              setDataSource([...dataSource]);
            }}
          />
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: () => {
        return (
          <Button type="link" danger>
            删除
          </Button>
        );
      },
    },
  ];

  useEffect(() => {
    if (onChange) {
      onChange(dataSource);
    }
  }, [dataSource]);

  useEffect(() => {
    if (!dataSource.length) return;
    dataSource.forEach((item: any) => {
      if (activeKey === 'KG') {
        item.weight = convertUnit(item.weight, 'lb', 'kg');
        item.width = convertUnit(item.width, 'in', 'cm');
        item.height = convertUnit(item.height, 'in', 'cm');
        item.length = convertUnit(item.length, 'in', 'cm');
      } else {
        item.weight = convertUnit(item.weight, 'kg', 'lb');
        item.width = convertUnit(item.width, 'cm', 'in');
        item.height = convertUnit(item.height, 'cm', 'in');
        item.length = convertUnit(item.length, 'cm', 'in');
      }
    });
    setDataSource(dataSource);
  }, [activeKey]);

  return (
    <>
      <ProTable
        headerTitle={[
          <Space key={1}>
            <div style={{ fontSize: 16 }}>货物信息</div>
            <div style={{ color: '#707070', marginLeft: '18px' }}>单位切换</div>
            <div className={classNames(s.warp)}>
              <div
                className={classNames(s.box1, activeKey === 'KG' && s.choice)}
                onClick={() => {
                  setActiveKey('KG');
                  setMeasures(0);
                }}
              >
                KG/CM
              </div>
              <div
                className={classNames(s.box2, activeKey === 'LB' && s.choice)}
                onClick={() => {
                  setActiveKey('LB');
                  setMeasures(1);
                }}
              >
                LB/IN
              </div>
            </div>
          </Space>,
        ]}
        columns={columns}
        rowKey="id"
        scroll={{
          x: 1200,
        }}
        search={false}
        dataSource={dataSource}
        pagination={false}
      />
    </>
  );
};
