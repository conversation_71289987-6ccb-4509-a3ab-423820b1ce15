import React, { useState } from 'react';
import { Button, Col, Form, Input, Modal, Row, Space, message } from 'antd';
import { saveQuoteRecordAPI } from '@/services/home/<USER>';

interface Props {
  btnText: string;
  btnType:
    | 'link'
    | 'text'
    | 'ghost'
    | 'default'
    | 'primary'
    | 'dashed'
    | undefined;
  selectedRowKeys?: any;
  trialObj?: any;
}
const QuotationModal = ({
  btnText,
  btnType,
  selectedRowKeys,
  trialObj,
}: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [form] = Form.useForm();
  /* 保存报价单 */
  const saveQuotation = async (values: any) => {
    try {
      const { status } = await saveQuoteRecordAPI({
        ...values,
        recordId: trialObj?.id,
        priceItemId: selectedRowKeys[0]?.id,
      });
      if (status) {
        setIsModalOpen(false);
        message.success('保存成功').then(() => {
          window.location.reload();
        });
        
      }
    } catch (err) {}
  };

  const showModal = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择一条数据');
      return false;
    }
    setIsModalOpen(true);
  };
  const handleOk = () => {
    form.validateFields().then((values) => {
      saveQuotation(values);
    });
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title="报价单"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={700}
      >
        <div>
          <Space>
            <div>
              <span style={{ color: '#747d8c' }}>卡派价格：</span>
              <span style={{ color: 'blue' }}>
                ${selectedRowKeys[0]?.chargeAmount || '-'}
              </span>
            </div>
          </Space>
          <div>
            <Space size={80} style={{ marginTop: 18 }}>
              <div>
                <span style={{ color: '#747d8c' }}>单价：</span>
                <span>{selectedRowKeys[0]?.price}元/kg</span>
              </div>
              <div>
                <span style={{ color: '#747d8c' }}>汇率：</span>
                <span>{trialObj?.rate}</span>
              </div>
              <div>
                <span style={{ color: '#747d8c' }}>渠道：</span>
                <span>{selectedRowKeys[0]?.serviceLevel}</span>
              </div>
            </Space>
          </div>
        </div>
        <div style={{ marginTop: 30 }}>
          <Form form={form}>
            <Row>
              <Col span={12}>
                <Form.Item label="客户" name="clientName">
                  <Input
                    placeholder="请输入客户代码或简称"
                    style={{ width: '90%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="业务员" name="salesman">
                  <Input
                    placeholder="请输入业务员姓名"
                    style={{ width: '90%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="报价" name="totalChargeAmount">
                  <Input placeholder="请输入总价" style={{ width: '90%' }} suffix="美元" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="单价" name="price">
                  <Input placeholder="请输入单价" style={{ width: '90%' }} suffix="元/kg" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </Modal>
    </>
  );
};
export default React.memo(QuotationModal);
