import { ProCard } from '@ant-design/pro-components';
import React, { useState } from 'react';
import FormInformation from './FormInformation';
import CargoInformation from './CargoInformation';
import { Button, Spin, message } from 'antd';
import { getCardEstimateAPI } from '@/services/home/<USER>';
import TrialCalculationResults from './TrialCalculationResults';

const PriceTrial = () => {
  //基本信息
  const [formInformation, setFormInformation] = useState<any>({});
  //货物信息
  const [cargoInformation, setCargoInformation] = useState<any>([]);
  //试算结果
  const [trialResult, setTrialResult] = useState<any>([]);
  //试算对象
  const [trialObj, setTrialObj] = useState<any>({}); 
  const [loading,setLoading] = useState(false)
  const [measures,setMeasures] = useState<any>(0)
  /* 卡派试算 */
  const getCardEstimate = async () => {
    setLoading(true)
    try {
      const { status, data } = await getCardEstimateAPI({
        ...formInformation,
        destCityId: formInformation.destCityId?.value,
        originCityId: formInformation.originCityId?.value,
        pickDate: formInformation.pickDate?.format('YYYY-MM-DD'),
        itemList: cargoInformation,
        measure:measures //1 磅/英寸,0 kg/cm
      });
      if (status) {
        setLoading(false)
        setTrialResult(data?.priceItemList || []);
        setTrialObj(data);
        message.success('试算成功');
        
      }
    } catch (e) {
      setLoading(false)
      console.log(e);
    }finally{
      setLoading(false)
    }

  }; 
  return (
    <Spin spinning={loading} tip="正在试算，请稍后">
      <ProCard title="基本信息" style={{ marginBottom: '20px' }}>
        <FormInformation
          onChage={(e) => {
            // console.log('基本信息', e);
            setFormInformation(e);
          }}
        />
      </ProCard>
      <CargoInformation
        onChange={(e) => {
          // console.log('货物信息', e);
          setCargoInformation(e);
        }}
        setMeasures={setMeasures}
      />
      <div style={{display:"flex" ,justifyContent:'center',padding:20}}>
      <Button onClick={getCardEstimate}>立即试算</Button>
      </div>
      {trialResult?.length >= 1 && (
        <>
          <TrialCalculationResults trialResult={trialResult} trialObj={trialObj} />
        </>
      )}
    </Spin>
  );
};

export default React.memo(PriceTrial);
