import {
  EditableProTable,
  // ProCard,
  // ProFormField,
} from '@ant-design/pro-components';
import { Button, Form, Space } from 'antd';
import s from './index.less';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import ExcelReader from './ExcelReader';
type UnitType = 'kg' | 'lb' | 'cm' | 'in';
export default (props: any) => {
  const { packageChange ,setUnit} = props;
  const [form] = Form.useForm();
  const defaultData = [
    {
      id: 1,
      number: '',
      feature1: '',
      feature2: '',
      weight: '',
      width: '',
      height: '',
      length: '',
      // volume: '',
      // remark: '',
    },
  ];
  /* tabs */
  const [activeKey, setActiveKey] = useState('KG');
  const [editableKeys, setEditableRowKeys] = useState<any>(() =>
    defaultData.map((item) => item.id),
  );
  const [dataSource, setDataSource] = useState<any>(() => defaultData);
  /* 单位转换 */
  function convertUnit(value: any, from: UnitType, to: UnitType) {
    const conversionTable: any = {
      kg: {
        lb: 2.20462,
      },
      lb: {
        kg: 0.453592,
      },
      cm: {
        in: 0.393701,
      },
      in: {
        cm: 2.54,
      },
    };
    if (!conversionTable[from] || !conversionTable[from][to]) {
      throw new Error(`转换单位出错 ${from} to ${to}`);
    }
    if (value === '') return '';
    const result = value * conversionTable[from][to];
    return Number(result.toFixed(2));
  }

  const columns: any = [
    {
      title: '数量',
      dataIndex: 'number',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
          {
            message: '只能为数字',
            pattern: /^[0-9]*$/,
          },
        ],
      },
    },
    {
      title: '参考号-1',
      dataIndex: 'feature1',
    },
    {
      title: '参考号-2',
      dataIndex: 'feature2',
    },
    {
      title: `重量/${activeKey === 'KG' ? 'KG' : 'LB'}`,
      dataIndex: 'weight',
    },
    {
      title: `宽度/${activeKey === 'KG' ? 'CM' : 'IN'}`,
      dataIndex: 'width',
    },
    {
      title: `高度/${activeKey === 'KG' ? 'CM' : 'IN'}`,
      dataIndex: 'height',
    },
    {
      title: `长度/${activeKey === 'KG' ? 'CM' : 'IN'}`,
      dataIndex: 'length',
    },
    // {
    //   title: '时间',
    //   dataIndex: 'time',
    // },

    {
      title: '操作',
      valueType: 'option',
      width: 100,
      render: () => {
        return null;
      },
    },
  ];

  /* 导入的模版数据 */
  function onExcelData(data: any) {
    if (data.length) {
      const newData = data.map((item: any, index: any) => {
        return {
          id: Date.now() + index,
          number: item['数量'],
          feature1: item['参考号-1'],
          feature2: item['参考号-2'],
          weight: item['重量'],
          width: item['宽度'],
          height: item['高度'],
          length: item['长度'],
        };
      });
      packageChange(newData);
      setDataSource(newData);
      setEditableRowKeys(newData.map((item: any) => item.id));
    }
  }

  useEffect(() => {
    if (!dataSource.length) return;
    dataSource.forEach((item: any) => {
      if (activeKey === 'KG') {
        item.weight = convertUnit(item.weight, 'lb', 'kg');
        item.width = convertUnit(item.width, 'in', 'cm');
        item.height = convertUnit(item.height, 'in', 'cm');
        item.length = convertUnit(item.length, 'in', 'cm');
      } else {
        item.weight = convertUnit(item.weight, 'kg', 'lb');
        item.width = convertUnit(item.width, 'cm', 'in');
        item.height = convertUnit(item.height, 'cm', 'in');
        item.length = convertUnit(item.length, 'cm', 'in');
      }
      form.setFieldsValue({
        [item.id]: item,
      });
    });
    setDataSource(dataSource);
  }, [activeKey]);

  return (
    <>
      <EditableProTable
        headerTitle={[
          <Space key={1}>
            <div style={{fontSize:16}}>4.包裹信息</div>
            <div style={{ color: '#707070', marginLeft: '18px' }}>单位切换</div>
            <div className={classNames(s.warp)}>
              <div
                className={classNames(s.box1, activeKey === 'KG' && s.choice)}
                onClick={() => {
                  setActiveKey('KG');
                  setUnit('KG');
                }}
              >
                KG/CM
              </div>
              <div
                className={classNames(s.box2, activeKey === 'LB' && s.choice)}
                onClick={() => {
                  setActiveKey('LB');
                  setUnit('LB');
                }}
              >
                LB/IN
              </div>
            </div>
          </Space>,
        ]}
        columns={columns}
        rowKey="id"
        scroll={{
          x: 960,
        }}
        style={{ marginBottom: '22px' }}
        value={dataSource}
        onChange={setDataSource}
        recordCreatorProps={{
          newRecordType: 'dataSource',
          record: () => ({
            id: Date.now(),
            number: '',
            feature1: '',
            feature2: '',
            weight: '',
            width: '',
            height: '',
            length: '',
              
          }),
        }}
        toolBarRender={() => {
          return [
            <Space key={1}>
              <ExcelReader onExcelData={onExcelData} />
              <Button
                type="link"
                onClick={() => {
                  window.open(
                    'https://web-common.kmfba.com/static/Package.xlsx',
                  );
                }}
              >
                查看模版
              </Button>
            </Space>,
          ];
        }}
        editable={{
          deleteText: [
            <Button key={1} type="link" danger>
              删除
            </Button>,
          ],
          type: 'multiple',
          editableKeys,
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.delete];
          },
          form: form,
          onValuesChange: (record, recordList) => {
            // console.log('recordList: ', recordList);
            packageChange(recordList);
            setDataSource(recordList);
          },
          onChange: setEditableRowKeys,
        }}
      />
      {/* <ProCard title="表格数据" headerBordered collapsible defaultCollapsed>
        <ProFormField
          ignoreFormItem
          fieldProps={{
            style: {
              width: '100%',
            },
          }}
          mode="read"
          valueType="jsonCode"
          text={JSON.stringify(dataSource)}
        />
      </ProCard> */}
    </>
  );
};
