import { ProCard, ProFormSelect } from '@ant-design/pro-components';
import { Cascader, Form, Space, Tooltip, Typography } from 'antd';
import service from '@/services/home';
import { useEffect, useState } from 'react';
import AddressLibrary from '@/components/AddressLibrary';
import { SearchOutlined } from '@ant-design/icons';
const { addressFuzzySearchAPI, getAreaAddressAPI } = service.UserHome;
const SenderAddress = ({ setSelectedArea, setChannelIds }: any) => {
  const [form] = Form.useForm();
  /* 发件地址已选数据 */
  const [selectedAreaObj, setSelectedAreaObj] = useState<any>(null);

  /* 获取客户渠道条数 */
  // const getChannelCount = async () => {
  //   try {
  //     const { status, data } = await getChannelCountAPI({
  //       Address: selectedAreaObj,
  //     });
  //     if (status) {
  //       setChannelCount(data.count);
  //     }
  //   } catch (e) {
  //     console.error('获取客户渠道条数接口出错',e);
  //   }
  // };
  // useEffect(() => {
  //   getChannelCount();
  // }, [selectedAreaObj]);
  /* 获取区域地址 */
  const [areaAddress, setAreaAddress] = useState<any>(null);
  const getAreaAddress = async () => {
    try {
      const { status, data } = await getAreaAddressAPI({});
      if (status) {
        setAreaAddress(data);
      }
    } catch (e) {
      console.error('获取区域地址接口出错', e);
    }
  };
  const findChannelById = (data:any, id:any) => {
    const list:any = []
    data.forEach((item:any) => {
      if(item?.channelList.length){
        list.push(...item.channelList)
      }
    })
    const result = list.find((item:any) => item?.sendAddress?.id === id)
    return result;
  }
  /* 从地址库选择的回调 */
  const changeAddressLibrary = (value: any) => {
    setSelectedAreaObj(value);
    setSelectedArea({
      key: JSON.stringify(value),
      value: JSON.stringify(value),
      label: JSON.stringify(value),
    });
    const {id} = value
    const {list} = areaAddress
    const newList = findChannelById(list,id)
    form.setFieldsValue({
      province: {
        key: JSON.stringify(value),
        label: `${newList?.region}-${newList?.name}`,
        value: JSON.stringify(value),
      },
    });
  };
  useEffect(() => {
    getAreaAddress();
  }, []);
  return (
    <>
      <ProCard
        title="2.发件地址"
        style={{ marginBottom: '22px' }}
        extra={[
          <a key={1}>
            <SearchOutlined />{' '}
            <AddressLibrary
              onChangeAddressLibrary={changeAddressLibrary}
              isShippingAddress={true}
            />
          </a>,
        ]}
      >
        <Form
          name="basic"
          autoComplete="off"
          form={form}
          labelCol={{
            span: 2,
          }}
        >
          {!selectedAreaObj ? (
            <Form.Item
              label="产品"
              name="channelGroupName"
              rules={[
                {
                  required: true,
                  message: '请选择区域',
                },
              ]}
            >
              <Space>
                <Cascader
                  options={areaAddress?.list || []}
                  style={{ width: 500 }}
                  placeholder="请选择"
                  multiple
                  showCheckedStrategy={Cascader.SHOW_CHILD}
                  fieldNames={{
                    label: 'name',
                    value: 'name',
                    children: 'channelList',
                  }}
                  onChange={(value, selectedOptions: any) => {
                    let channelIds: string[] = [];
                    selectedOptions.forEach((item: any) => {
                      item.forEach((item2: any) => {
                        if (item2?.id) {
                          channelIds.push(item2.id);
                        }
                      });
                    });
                    // console.log('channelIds',channelIds);
                    setChannelIds(channelIds.join(','));
                  }}
                />
                <Tooltip title={`${areaAddress?.totalChannelCount} 个可用渠道`}>
                  <Typography.Link>
                    {areaAddress?.totalChannelCount || 0} 个可用渠道
                  </Typography.Link>
                </Tooltip>
              </Space>
            </Form.Item>
          ) : (
            <ProFormSelect
              name="province"
              label="地址"
              width={500}
              disabled={true}
              fieldProps={{
                labelInValue: true,
                style: {
                  minWidth: 140,
                },
                filterOption: false,
                onChange: (e) => {
                  setSelectedAreaObj(JSON.parse(e.value));
                  setSelectedArea(e);
                  form.setFieldsValue({
                    province: e,
                  });
                },
              }}
              showSearch
              placeholder="请选择，支持模糊搜索"
              rules={[{ required: true, message: '必填不能为空' }]}
              debounceTime={300}
              request={async ({ keyWords }) => {
                const { status, data } = await addressFuzzySearchAPI({
                  token: keyWords,
                });
                if (status) {
                  return data.list.map((item: any) => {
                    return {
                      label: `${item.country} - ${item.provinceShortName} / ${item.province} - ${item.city} / ${item.zipCode}`,
                      value: JSON.stringify(item),
                    };
                  });
                }
              }}
              addonAfter={[
                <Space key={1}>
                  <a
                    onClick={() => {
                      setSelectedAreaObj(null);
                      setSelectedArea(null);
                      form.setFieldsValue({
                        province: null,
                      });
                    }}
                  >
                    清除
                  </a>
                  <span>{`${selectedAreaObj.country} - ${selectedAreaObj.provinceShortName} / ${selectedAreaObj.province} - ${selectedAreaObj.city} / ${selectedAreaObj.zipCode}`}</span>
                </Space>,
              ]}
            />
          )}
        </Form>
      </ProCard>
    </>
  );
};
export default SenderAddress;
