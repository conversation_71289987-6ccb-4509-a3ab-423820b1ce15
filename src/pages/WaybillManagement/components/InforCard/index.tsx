import { ProCard } from '@ant-design/pro-components';
import { Col, Row, Form, Input, Select } from 'antd';
import { forwardRef, useImperativeHandle } from 'react';

// 从antd库中导入Input组件中的TextArea子组件
const { TextArea } = Input;

// 使用forwardRef创建一个名为InforCard的函数组件，其返回一个表单
const InforCard = forwardRef((props: any, ref: any) => {
  // 使用antd库中的Form组件的useForm钩子来创建一个form对象
  const [form] = Form.useForm();

  // useImperativeHandle能够使子组件把自己的方法暴露给父组件
  // 使用ref作为第二个参数，可以让父组件获得这个方法
  useImperativeHandle(ref, () => ({
    // 把表单验证方法暴露给父组件
    formValidation: () => form.validateFields(),
  }));
  /* 密度值 50,55,60,65,70,77.5,85,92.5,100,110,125,150,175,200,250,300,400,500 改成value label格式 */
  const myArray = [
    { value: 50, label: '50' },
    { value: 55, label: '55' },
    { value: 60, label: '60' },
    { value: 65, label: '65' },
    { value: 70, label: '70' },
    { value: 77.5, label: '77.5' },
    { value: 85, label: '85' },
    { value: 92.5, label: '92.5' },
    { value: 100, label: '100' },
    { value: 110, label: '110' },
    { value: 125, label: '125' },
    { value: 150, label: '150' },
    { value: 175, label: '175' },
    { value: 200, label: '200' },
    { value: 250, label: '250' },
    { value: 300, label: '300' },
    { value: 400, label: '400' },
    { value: 500, label: '500' }
  ];
 


  return (
    <>
      <ProCard title="3.基本信息" style={{ marginBottom: '22px' }}>
        <Form
          name="basic"
          autoComplete="off"
          form={form}
          labelCol={{
            span: 6,
          }}
        >
          <Row>
            <Col span={8}>
              <Form.Item
                label="密度"
                name="density"
                rules={[
                  {
                    required: false,
                    message: '必填项不能为空',
                  },
                ]}
              >
                {/* <Input style={{ width: '90%' }} placeholder="请选择" /> */}
                <Select
                  allowClear
                  placeholder="请选择" 
                  style={{ width: '90%' }}
                  options={myArray}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="关联订单号"
                name="outerOrderId"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                  // {
                  //   pattern: /^[0-9]*$/,
                  //   message: '订单号只能为正整数',
                  // }
                ]}
              >
                <Input
                  maxLength={30}
                  showCount
                  style={{ width: '90%' }}
                  placeholder="请输入订单号"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <Form.Item
                label="签名服务"
                name="signature"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Select
                  placeholder="请选择" 
                  style={{ width: '90%' }}
                  options={[
                    {
                      value: '0',
                      label: '无需签名',
                    },
                    {
                      value: '1',
                      label: '签名',
                    },
                    {
                      value: '2',
                      label: '成人签名',
                    },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="备注"
                name="remark"
                rules={[
                  {
                    required: false,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <TextArea
                  showCount
                  style={{ width: '90%' }}
                  placeholder="请输入备注"
                  maxLength={50}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </ProCard>
    </>
  );
});

// 导出组件
export default InforCard;
