import { ProCard } from '@ant-design/pro-components';
import { Table } from 'antd';
import { useEffect, useState } from 'react';
const columns = [
  {
    title: '编号',
    dataIndex: 'channelId',
  },
  {
    title: '渠道产品',
    dataIndex: 'channelName',
  },
  {
    title: '计费重',
    dataIndex: 'chargeWeight',
    render: (text: any,record:any) => {
      if(record?.errorMessage){
        return <span style={{color:'#ff4757'}}>{record?.errorMessage}</span>
      }
      return text + ' kg';
    },
  },
  // {
  //   title: '上游试算价格',
  //   dataIndex: 'totalCharge',
  // },
  {
    title: '密度',
    dataIndex: 'limitDensity',
    render: (text: any) => {
      return text ? text  : '-';
    }

  },
  {
    title: '总费用',
    dataIndex: 'withholdFee',
    render: (text: any) => {
      return text + ' $';
    },
    // sorter: (a: any, b: any) => {
    //   return Number(a.withholdFee) - Number(b.withholdFee);
    // }
  },
  {
    title: '发件地址',
    dataIndex: 'senderAddress',
    render: (text: any) => {
      return `${text?.country} - ${text?.provinceShortName} / ${text?.province} - ${text?.city} / ${text?.zipCode}`;
    },
  },
];
const TrialinfoCard = (props: any) => {
  const { trialResult, setReportInfo } = props;
  const [dataList, setDataList] = useState<any>([]);
  //已选区域数据
  const [selectedArea, setSelectedArea] = useState<any>([]);

  useEffect(() => {
    if (trialResult.length) {
      const data = JSON.parse(JSON.stringify(trialResult[0]?.waybillTestPriceResults || []));
      /* 过滤channelName不为空的数据 */
      const newData = data?.filter((item: any) => item.channelName!=='');
      //把newData中errorMessage 不为空的值，放到最后

      /* 对数据 withholdFee 由小到大排序 */
      const newList = newData?.sort((a: any, b: any) => {
        return Number(a.withholdFee)- Number(b.withholdFee);
      });
      //把newList中errorMessage 不为空的值，放到最后
      const list1 = newList?.filter((item: any) => item.errorMessage!=='');
      const list2 = newList?.filter((item: any) => item.errorMessage==='');
      const newList1 = [...list2,...list1]
      
      setDataList([...newList1]);
      setReportInfo([newList1[0]]);
      setSelectedArea([newList1[0]?.channelId])
    }
  }, [trialResult]);

  return (
    <>
      <ProCard title="试算信息" style={{ marginBottom: '22px' }}>
        <Table
          pagination={false}
          rowKey="channelId"
          dataSource={dataList}
          columns={columns}
          rowSelection={{
            type: 'radio',
            onChange: (selectedRowKeys, selectedRows) => {
              // console.log('selectedRows: ', selectedRows);
              setSelectedArea(selectedRowKeys)
              setReportInfo(selectedRows);
            },
            selectedRowKeys: selectedArea,
          }}
        />
      </ProCard>
    </>
  );
};
export default TrialinfoCard;
