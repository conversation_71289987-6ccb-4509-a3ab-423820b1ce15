import React, { useEffect, useState } from 'react';
import { ProCard, CheckCard } from '@ant-design/pro-components';
import { Button, Space } from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import styles from '../index.less';
import AddressLibrary from '@/components/AddressLibrary'
import AddAddress from '@/pages/DataManagement/Address/AddAddress'
import service from '@/services/home';
import classNames from 'classnames';
const { getAddressInformationAPI } = service.UserHome;

// 地址卡片组件
const AddressCard= (props:any) => {
  const {AddressCardChange} = props
  // 地址选中状态
  const [selectedAddress, setSelectedAddress] = useState<any>(null);

  // 地址列表
  const [addressList, setAddressList] = useState<any>([]);

  const [loading, setLoading] = useState(true);
  /* 获取常用地址库地址 */
  const getAddressInformation = async () => {
    setLoading(true);
    try {
      const { status, data } = await getAddressInformationAPI({
        start: 0,
        len: 10,
      });
      if (status) {
        setAddressList([...data.list]);
        setLoading(false);
      } 
    } catch (err) {
      console.log('地址库选择抛出异常1: ', err);
      setLoading(false);
    }
  };
  /* 数组对象去重 */
  const unique = (arr: any) => {
    const res = new Map();
    return arr.filter((a: any) => !res.has(a.id) && res.set(a.id, 1));
  };

  /* 从地址库选择的地址信息 */
  const changeAddressLibrary = (value: any) => {
    console.log('value: ', value);
    setSelectedAddress(value.id);
    AddressCardChange(value)
    const newAddressList = unique([value, ...addressList]);
    setAddressList([...newAddressList]);
  };
  useEffect(() => {
    getAddressInformation();
  }, []);
  return (
    // 地址卡片容器
    <ProCard
      loading={loading}
      style={{ marginBottom: '22px' }}
      title="1.请选择收货地址"
      bordered
      // 右上角操作区域
      extra={[
        <Space key={1} >
          <a>
            <SearchOutlined /> <AddressLibrary onChangeAddressLibrary={changeAddressLibrary} />
          </a>
          <Button type='link' icon={<ReloadOutlined />} onClick={getAddressInformation}>刷新</Button>
          <AddAddress key="add" refreshTable={getAddressInformation} type='link' />
        </Space>,
      ]}
    >
      {/* 地址选项列表 */}
      <CheckCard.Group
        value={selectedAddress} 
        // 选项变化回调
        onChange={(value) => {
          AddressCardChange(value)
          setSelectedAddress(value)
        }}
        className={styles.isNoCheckCard}
      >
        <div style={{display:'flex',overflow:'auto' }}>
          {/* 渲染地址选项 */}
          {addressList.map((item: any) => (
            <CheckCard
              style={{flexShrink:0}}
              key={item.id}
              value={JSON.stringify(item)}
              // 地址卡片标题
              title={<div className={classNames(selectedAddress===item.id?styles.choice:null)}>
                <div>
                  <span>{item?.isFBA===1?item?.name:item.contactName}</span>
                  {
                    item.isFBA===1?
                    <span className={styles.isFBA}>FBA</span>
                    :null
                  }
                </div>
                <div style={{fontSize:12}}>{item.contactPhone}</div>
                <div style={{fontSize:12}}>{item.companyName}</div>
              </div>}
              // 地址卡片描述
              description={
                <div style={{ color: '#707070' }}>{item.street},{item.city},{item.provinceShortName},{item.country},{item.zipCode}</div>
              }
              // 地址卡片右侧操作
              // extra={
              //   <Space key="operation">
              //     <EditOutlined style={{ color: '#979797' }} />
              //     <DeleteOutlined style={{ color: '#979797' }} />
              //   </Space>
              // }
            />
          ))}
        </div>
      </CheckCard.Group>
    </ProCard>
  );
};

export default AddressCard;
