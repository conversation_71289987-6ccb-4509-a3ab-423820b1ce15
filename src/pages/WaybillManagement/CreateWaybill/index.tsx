import React, { useState, useMemo } from 'react';
import { Tabs } from 'antd';
import WaybillContent from './WaybillContent';
import styles from './index.less';
import MultiVoteCreation from '../MultiVoteCreation';

const items = [
  {
    label: '单票创建',
    key: '1',
    children: <WaybillContent />,
  },
  {
    label: '多票创建',
    key: '2',
    children: <MultiVoteCreation />,
  },
];

const CreateWaybill = () => {
  const [activeKey, setActiveKey] = useState('1');

  const onChange = (key:any) => {
    setActiveKey(key);
  };

  const tabsProps = useMemo(() => ({ className: styles.tabBox, activeKey, items, onChange }), [activeKey]);

  return (
    <div>
      <Tabs {...tabsProps} />
    </div>
  );
};

export default CreateWaybill;
