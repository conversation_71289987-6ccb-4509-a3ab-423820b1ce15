import React, { useRef, useState } from 'react';
import AddressCard from '../components/AddressCard';
import InforCard from '../components/InforCard';
import PackageCard from '../components/PackageCard';
import TrialinfoCard from '../components/TrialinfoCard';
import SenderAddress from '../components/SenderAddress';
import { history, useAccess, Access } from 'umi';
import { Space, Button, message, Spin } from 'antd';
import service from '@/services/home';
import Declare from '../components/Declare';
const { getWaybillEstimateAPI, getWaybillReportAPI } = service.UserHome;
const WaybillContent = () => {
  const access = useAccess();
  /* 已选区域数据  发件地址 */
  const [selectedArea, setSelectedArea] = useState<any>(null);
  const [channelIds, setChannelIds] = useState<string>('');
  /* 试算按钮loading */
  const [trialLoading, setTrialLoading] = useState<boolean>(false);
  /* 立即预报loading */
  const [reportLoading, setReportLoading] = useState<boolean>(false);
  /* 立即试算结果 */
  const [trialResult, setTrialResult] = useState<any>([]);
  const [trialObj, setTrialObj] = useState<any>({
    address: null,
    basicInfo: null,
    packageList: null,
    declares: []
  });
  /* 单位 */
  const [unit, setUnit] = useState<string>('KG');
  /* 选择收获地址的change */
  const AddressCardChange = (value: any) => {
    if (value) {
      setTrialObj({
        ...trialObj,
        address: typeof value === 'object' ? value : JSON.parse(value),
      });
    }
  };

  /* 基本信息 */
  /* 包裹信息change */
  const packageChange = (value: any) => {
    setTrialObj({
      ...trialObj,
      packageList: value,
    });
  };
  // 申报信息回调
  const getDeclares = (value: any) => {
    setTrialObj({
      ...trialObj,
      declares: value,
    });
  }
  /* 检测是否空 */
  /* 立即试算接口 */
  const getWaybillEstimate = async (resInfo: any) => {
    // console.log('trialObj',trialObj);
    //数组序列化
    setTrialLoading(true);
    try {
      const { status, data } = await getWaybillEstimateAPI({
        ...resInfo,
        recipientAddressId: trialObj.address?.id,
        // recipientAddressId:'addr1867c2d9541001',
        pieces: trialObj.packageList,
        isKgAndCm: unit === 'KG' ? 1 : 0,
        senderAddress: selectedArea?.value && JSON.parse(selectedArea.value),
        channelIds: channelIds
      });
      if (status) {
        setTrialResult([{ ...data }]);
        setTrialLoading(false);
        message.success('试算成功');
        setTimeout(() => {
          window.scrollTo(0, document.body.scrollHeight);
        }, 0);
      } else {
        setTrialLoading(false);
      }
    } catch (err) {
      setTrialLoading(false);
      console.log('立即试算出错: ', err);
    }
  };
  /* 检测空数组 */
  function validateData(data: any) {
    for (let item of data) {
      if (Object.keys(item).length <= 7) {
        return false;
      }
      for (let key in item) {
        if (item[key] === '' || item[key] === null || item[key] === undefined) {
          return false;
        }
      }
    }
    return true;
  }

  /* 立即试算信息汇总 */
  const refAddress = useRef<any>(null);
  const TrialinfoCardChange = async () => {
    let resInfo: any = null;
    if (!trialObj.address) return message.error('请选择收货地址');
    console.log(channelIds);
    if (!selectedArea && !channelIds.length)
      return message.error('请选择发件地址');
    try {
      resInfo = await refAddress.current.formValidation();
    } catch (err) {
      console.log('err: ', err);
      message.error('请填写基本信息');
    }
    if (!resInfo) return;
    setTrialObj({
      ...trialObj,
      basicInfo: resInfo,
    });
    if (!trialObj.packageList)
      return message.error('至少保证有一条包裹信息才能试算');
    if (!validateData(trialObj.packageList))
      return message.error('请检查包裹信息是否填写完整,或删除行');
    // if (validateData(trialObj.declares))
    // return message.error('请填写申报信息');
    // console.log('立即试算: ', trialObj);
    getWaybillEstimate(resInfo);
  };

  /* 立即预报所选的试算信息 */
  const [reportInfo, setReportInfo] = useState<any>([]);
  /* 立即预报 */
  const immediateForecast = async () => {
    // console.log('立即预报: ', trialObj);
    // if (!reportInfo?.length) {
    //   message.error('请选择一条试算信息后再预报');
    //   return false
    // }
    setReportLoading(true);
    try {
      const { status } = await getWaybillReportAPI({
        recipientAddressId: trialObj.address?.id,
        ...trialObj.basicInfo,
        pieces: trialObj.packageList,
        // ...trialResult[0],
        ...reportInfo[0],//2023年8月30日 17:00:00 的需求变更
        isKgAndCm: unit === 'KG' ? 1 : 0,
        senderAddress: selectedArea?.value && JSON.parse(selectedArea.value),
        channelIds: channelIds,
        declares: trialObj.declares
      });
      if (status) {
        setReportLoading(false);
        message.success('立即预报成功');
        /* 跳转到运单查询 */
        history.push('/waybillManagement/waybill');
      } else {
        setReportLoading(false);
      }
    } catch (err) {
      setReportLoading(false);
      console.log('立即预报出错: ', err);
    }
  };
  return (
    <Spin spinning={trialLoading}>
      {/* 选择收货地址 */}
      <AddressCard AddressCardChange={AddressCardChange} />
      {/* 发件地址 */}
      <SenderAddress
        setSelectedArea={setSelectedArea}
        setChannelIds={setChannelIds}
      />
      {/* 基本信息 */}
      <InforCard ref={refAddress} />
      {/* 包裹信息 */}
      <PackageCard packageChange={packageChange} setUnit={setUnit} />
      {/* 申报信息 */}
      {!['US','us',undefined].includes(trialObj?.address?.country) && <Declare packageChange={getDeclares}/>}
      {/* <Declare packageChange={getDeclares} /> */}
      {/* 试算信息 */}
      {trialResult.length >= 1 ? (
        <TrialinfoCard trialResult={trialResult} setReportInfo={setReportInfo} />
      ) : (
        ''
      )}

      <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '40px' }}>
        <Access
          accessible={!access.WaybillReadOnly()}
          fallback={<div>无提交权限.</div>}
        >
          <Space size={42} wrap>
            <Button
              size="large"
              onClick={immediateForecast}
              disabled={!trialResult.length}
              loading={reportLoading}
            >
              立即预报
            </Button>
            <Button
              type="primary"
              size="large"
              onClick={TrialinfoCardChange}
              loading={trialLoading}
            >
              立即试算
            </Button>
          </Space>
        </Access>
      </div>
    </Spin>
  );
};

export default WaybillContent;
