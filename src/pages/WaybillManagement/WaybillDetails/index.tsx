import { useLocation } from 'umi';
import DetailsCard from '@/pages/DataManagement/components/DetailsCard';
import service from '@/services/home';
import { useEffect, useState } from 'react';
import { formatTime } from '@/utils/format';
import { Tag, Typography } from 'antd';
import { ProTable } from '@ant-design/pro-components';
const { waybillListDetailsAPI, getPackageInformationAPI } = service.UserHome;

// @ts-ignore
const instance=JSON.parse(localStorage.getItem('instance'));
const statusEnum: any = {
  0: '已预报',
  1: '预报中',
  2: '预报失败',
  3: '部分出账',
  4: '全部出账',
  5: '已取消',
};
const { Link, Paragraph } = Typography;
const signatureEnum: any = {
  '0': '无需签名',
  '1': '签名',
  '2': '成人签名'
}


const declareColumns: any = [
  {
      title: '产品描述',
      width:'250px',
      dataIndex: 'productDesc',
    },
{
  title: '产品数量',
  dataIndex: 'number',
  formItemProps: {
    rules: [
      {
        required: true,
        whitespace: true,
        message: '此项是必填项',
      },
      {
        message: '只能为数字',
        pattern: /^[0-9]*$/,
      },
    ],
  },
},

{
  title: `重量/KG`,
  dataIndex: 'weight',
},
{
  title: '申报价值',
  dataIndex: 'price',
},
{
  title: '海关编码',
  dataIndex: 'hsfCode',
},
{
  title: '原产国家/2位编码',
  dataIndex: 'country',
},
// {
//   title: '时间',
//   dataIndex: 'time',
// },

];
const WaybillDetails = () => {
  const location = useLocation();
  const [wabiList, setWabiList] = useState<any>({});
  const { state: newData }: any = location;
  // console.log('state: ', newData);
  /* loading */
  const [loading, setLoading] = useState<boolean>(false);
  /* 详情数据 */
  const [detailedData, setDetailedData] = useState<any>({
    essentialInformation: [],
  });
  /* 获取运单详情 */
  const getWaybillDetails = async () => {
    setLoading(true);
    try {
      const { status, data } = await waybillListDetailsAPI({
        id: newData.id,
        flg: newData.flg,
      });
      if (status) {
        setWabiList(data);
        let datas= [
              {
                title: '订单号',
                content: data.id,
              },
              {
                title: '产品名',
                content: data.channelName,
              },
              {
                title: '运单状态',
                content: statusEnum[data.status],
              },
              {
                title: '关联订单号',
                content: data.outerOrderId,
              },
              {
                title: '总件数',
                content: data.piecesNumber + ' 件',
              },
              {
                title: '预报失败原因',
                content: data.failedReason,
              },
              {
                title: '创建时间',
                content: formatTime(data.insertTime),
              },
              {
                title: '运单号',
                content: data.expressCode || '-',
              },
              {
                title: '重量',
                content: data.totalWeight + ' KG',
              },
              {
                title: '打单次数',
                content: data.printTimes || '-',
              },
              {
                title: '预报金额',
                content: data?.withholdFee + ' 美元' || '-',
              },
              {
                title: '签名服务',
                content: signatureEnum[data?.signature] || '-'
              },
            ];
        if(instance.id === 'ins185b32f4c47163' && data?.referenceTotalAmount){
          datas.push( {
            title: '价格表金额',
            content: data?.referenceTotalAmount + ' 美元',
          })
        }
        setDetailedData({
          /* 基本信息 */
          essentialInformation: datas,
          /* 发件信息 */
          senderAddress: [
            {
              title: '姓名',
              content: data.senderAddress?.contactName,
            },
            {
              title: '国家',
              content: data.senderAddress?.country,
            },
            {
              title: '邮编',
              content: data.senderAddress?.zipCode,
            },
            {
              title: '公司',
              content: data.senderAddress?.companyName,
            },
            {
              title: '州/省',
              content: data.senderAddress?.provinceShortName,
            },
            {
              title: '地址',
              content: data.senderAddress?.street,
            },
            {
              title: '电话',
              content: data.senderAddress?.contactPhone,
            },
            {
              title: '城市',
              content: data.senderAddress?.city,
            },
          ],
          /* 收件信息 */
          receivingInformation: [
            {
              title: '姓名',
              content: data.recipientAddress?.contactName,
            },
            {
              title: '国家',
              content: data.recipientAddress?.country,
            },
            {
              title: '邮编',
              content: data.recipientAddress?.zipCode,
            },
            {
              title: '公司',
              content: data.recipientAddress?.companyName,
            },
            {
              title: '州/省',
              content: data.recipientAddress?.provinceShortName,
            },
            {
              title: '地址',
              content: data.recipientAddress?.street,
            },
            {
              title: '电话',
              content: data.recipientAddress?.contactPhone,
            },
            {
              title: '城市',
              content: data.recipientAddress?.city,
            },
          
          ],
          // 申报信息
          declaresResponses:data?.declaresResponses,
          /* 备注以及日志 */
          remarksLogs: [
            {
              title: '备注',
              content: data.remark,
            },
          ],
        });
        setLoading(false);
      }
    } catch (err) {
      setLoading(false);
      console.log('获取运单详情出错：', err);
    }
  };
  useEffect(() => {
    if (!detailedData?.essentialInformation.length) return;
    detailedData?.essentialInformation?.forEach((item: any, index: any) => {
      if (
        item.title === '预报失败原因' &&
        (item.content === '-' || item.content === '')
      ) {
        // console.log('item: ', item);
        detailedData.essentialInformation.splice(index, 1);
      }
    });
    setDetailedData({ ...detailedData });
  }, [detailedData?.essentialInformation]);
  /* 获取包裹信息 */
  // const [packageInfoList, setPackageInfoList] = useState<any>([]);
  // const getPackageInformation = async () => {
  //   try {
  //     const { status, data } = await getPackageInformationAPI({
  //       id: newData.id,
  //     });
  //     if (status) {
  //       setPackageInfoList([...data.list]);
  //     }
  //   } catch (err) {
  //     console.log('包裹详情: ', err);
  //   }
  // };
  useEffect(() => {
    getWaybillDetails();
    // getPackageInformation();
  }, []);

  const columns = [
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: any) => {
        return statusEnum[text];
      },
      width: 110,
    },
    {
      title: '子订单号',
      dataIndex: 'id',
      copyable: true,
      width: 200,
    },
    {
      title: '子单跟踪号',
      dataIndex: 'subExpressCode',
      width:240,
      render: (text: any, record: any) => {
        return (
          <>
            {record?.subExpressCode ? (
              <Paragraph
                copyable={{
                  tooltips: true,
                  text: record?.subExpressCode,
                }}
                style={{ marginBottom: 0 }}
              >
                {
                    wabiList?.mainExpressCodes && wabiList?.mainExpressCodes?.split(',')?.includes(record?.subExpressCode) && <Tag color="orange">主单</Tag>
                  }
                <Link
                  href={`https://www.ups.com/track?loc=en_US&tracknum=${record?.subExpressCode}&requester=WT/trackdetails`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {record?.subExpressCode}
                </Link>
              </Paragraph>
            ) : (
              '-'
            )}
          </>
        );
      },
    },
    {
      title: '参考号1',
      dataIndex: 'feature1',
      width: 100,
      ellipsis: true,
    },
    {
      title: '参考号2',
      dataIndex: 'feature2',
      width: 100,
      ellipsis: true,
    },
    {
      title: '重量/KG',
      dataIndex: 'weight',
      width: 80,
      ellipsis: true,
    },
    {
      title: '宽度/CM',
      dataIndex: 'width',
      width: 80,
      ellipsis: true,
    },
    {
      title: '高度/CM',
      dataIndex: 'height',
      width: 80,
      ellipsis: true,
    },
    {
      title: '长度/CM',
      dataIndex: 'length',
      width: 80,
      ellipsis: true,
    },
    {
      title: '时间',
      dataIndex: 'insertTime',
      valueType: 'dateTime',
      width: 110,
      ellipsis: true,
    },
    {
      title: '扣费金额 ($)',
      dataIndex: 'payment',
      width: 100,
      ellipsis: true,
      // render: (text: any) => {
      //   return text;
      // },
    },
  ];
  return (
    <>
      <DetailsCard
        title="基本信息"
        list={detailedData.essentialInformation}
        loading={loading}
      />
      {/* <DetailsCard title='物流信息' list={[]} /> */}
      <DetailsCard title="包裹信息" list={[]}>
        <>
          {/* <Table rowKey="id" dataSource={packageInfoList} columns={columns} /> */}
          <ProTable
            ghost
            // 设置表格的主键
            rowKey="id"
            // 设置分页器的配置项
            pagination={{
              pageSize: 10,
              showQuickJumper: true,
            }}
            // 设置表格的列配置项
            columns={columns}
            // 不显示表格的搜索框
            search={false}
            scroll={{ x: 1200 }}
            // 设置日期格式化方式
            dateFormatter="string"
            // 设置请求数据的方式
            request={async (params: any) => {
              const { current: start, pageSize: len }: any = params;
              const res = await getPackageInformationAPI({
                start: (start - 1) * len,
                len,
                id: newData.id,
                flg: newData.flg,
              });
              return {
                data: res.data.list || [],
                success: res.status,
                total: res.data.amount,
              };
            }}
            // 设置表格的配置项
            options={false}
          />
        </>
      </DetailsCard>
      <DetailsCard title="申报信息" list={[]}>
        <>
          {/* <Table rowKey="id" dataSource={packageInfoList} columns={columns} /> */}
          <ProTable
            // 设置表格的主键
            rowKey="id"
            // 设置分页器的配置项
            pagination={{
              pageSize: 10,
              showQuickJumper: true,
            }}
            // 设置表格的列配置项
            columns={declareColumns}
            // 不显示表格的搜索框
            search={false}
            // 设置日期格式化方式
            dateFormatter="string"
            // 设置请求数据的方式
            // request={async (params: any) => {
            //   const { current: start, pageSize: len }: any = params;
            //   const res = await getPackageInformationAPI({
            //     start: (start - 1) * len,
            //     len,
            //     id: newData.id,
            //     flg: newData.flg,
            //   });
            //   return {
            //     data: res.data.list || [],
            //     success: res.status,
            //     total: res.data.amount,
            //   };
            // }}
            dataSource={detailedData?.declaresResponses}
            // 设置表格的配置项
            options={false}
          />
        </>
      </DetailsCard>
      <DetailsCard
        title="发件信息"
        list={detailedData.senderAddress}
        loading={loading}
      />
      <DetailsCard
        title="收件信息"
        list={detailedData.receivingInformation}
        loading={loading}
      />
      <DetailsCard title="备注以及日志" list={detailedData.remarksLogs} />
    </>
  );
};
export default WaybillDetails;
