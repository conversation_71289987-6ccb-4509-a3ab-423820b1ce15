.waybillSearch :global .ant-form {
  display: none !important;
}
.waybillSearch :global .ant-pro-card-border {
  border: none !important;
}
.listWrap {
  margin-bottom: 20px;
}
.listWrap .kou {
  background: #ffeae9;
}
.listWrap .kou :global( .ant-table-cell-row-hover) {
  background: #FFE1E0 !important;
}
.listWrap .kou :global( .ant-table-cell-fix-right) {
  background: #ffeae9;
}
.listWrap :global( .ant-pagination) {
  position: fixed;
  bottom: 2px ;
  right: 30px;
  z-index: 999;
}
/*自定义搜索表单*/
.searchWrap {
  width: 100%;
  background: white;
  display: flex;
  justify-content: space-between;
  padding: 20px 12px 0px 12px;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-areas: "header header2 header3" "header main sidebar" "menu footer2 footer3";
}
.searchWrap .item1 {
  width: 40%;
  grid-area: header;
}
.searchWrap .item1 .search_wrap {
  border: 1px solid #e8e8e8;
  height: 80px;
  display: flex;
  width: 442px;
  border-radius: 8px;
  margin-bottom: 20px;
}
.searchWrap .item1 .search_wrap :global(.ant-select-selector) {
  height: 100%;
}
.searchWrap .item1 .search_wrap :global(.ant-select-selector) :global(.ant-select-selection-item) {
  display: flex;
  align-items: center;
}
.searchWrap .searchRight {
  width: 60%;
  display: flex;
  flex-direction: column;
}
.searchWrap .searchRight .rightTime {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}
.searchWrap .searchRight .forecastTime {
  display: flex;
  align-items: center;
  margin-left: 20px;
}
/*这个可以通用*/
.search_back {
  background-color: #fcfcfc;
}
.search_back :global(.ant-select-selector) {
  background-color: #fcfcfc !important;
}
.search_back :global(.ant-picker) {
  background-color: #fcfcfc !important;
}
