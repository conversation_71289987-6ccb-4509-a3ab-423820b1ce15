// import { DownOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import { Button, Popconfirm, message, Tag, Typography, Modal, DatePicker, Input, Select, Space } from 'antd';
const { RangePicker } = DatePicker;
import { useRef, useState } from 'react';
import { history, useAccess, Access } from 'umi';
import { saveAs } from 'file-saver';
import service from '@/services/home';
import { formatTime } from "@/utils/format";
import { batchCancelAPI, downloadInvoiceAPI, downloadInvoicesAPI, getDownloadLabelsSheet } from '@/services/home/<USER>';
import dayjs from 'dayjs';
const { TextArea } = Input
import styles from './index.less'
import _ from 'lodash'
const {
  getWaybillListAPI,
  cancelWaybillAPI,
  downloadWaybillAPI,
  waybillExportAPI,
} = service.UserHome;
/* 枚举 
  0已预报/1预报中/2预报失败/3部分出账/4全部出账/
*/
// 签收证明枚举
const signatureEnum: any = {
  '0': '无需签名',
  '1': '签名',
  '2': '成人签名'
}
const statusEnum: any = {
  0: {
    text: '已预报',
    color: 'green',
  },
  1: {
    text: '预报中',
    color: 'orange',
  },
  2: {
    text: '预报失败',
    color: 'red',
  },
  3: {
    text: '部分出账',
    color: 'blue',
  },
  4: {
    text: '全部出账',
    color: 'purple',
  },
  5: {
    text: '已取消',
    color: 'Grey',
  },
  6: {
    text: '部分签收',
    color: 'blue',
  },
  7: {
    text: '签收',
    color: 'purple',
  },
};
const { Link, Paragraph } = Typography;

export default () => {
  const access = useAccess();
  const ref = useRef<any>();
  const objRef = useRef<any>();
  const [activeKey, setActiveKey] = useState<any>('1');
  /*强制取消开关*/
  const [isCancelOpen, setIsCancelOpen] = useState(false);
  /*强制取消id*/
  const [cancelId, setCancelId] = useState();
  /* 运单导出 */
  /* 导出 loding */
  const [exportLoading, setExportLoading] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [keyword, setKeyword] = useState('');
  const [params, setParams] = useState<any>({
    termQuery: false,
    // forecastStartTime: formatTime(moment().startOf('day')),
    // forecastEndTime:formatTime( moment().endOf('day'))   
  });
  /*强制取消关闭*/
  const handleCancel = () => {
    setIsCancelOpen(false)
  }
  /*确认强制取消*/
  const handleCancelOk = async () => {
    try {
      const { status } = await cancelWaybillAPI({ id: cancelId, force: true });
      if (status) {
        message.success('取消成功');
        await ref.current.reloadAndRest();
      }
      setIsCancelOpen(false);
    } catch (err) {
      console.log('取消运单: ', err);
    }
  }
  /*批量强制取消*/
  const handleBatchCancelOk = async () => {
    if (!selectedRowKeys?.length) return message.warning('请选择数据')
    try {
      const { status } = await batchCancelAPI({ ids: selectedRowKeys?.join(','), force: true });
      if (status) {
        message.success('取消成功');
        await ref.current.reloadAndRest();
      }
      setIsCancelOpen(false);
    } catch (err) {
      console.log('取消运单: ', err);
    }
  }
  const exportWaybill = async () => {
    setExportLoading(true);
    // const params = objRef?.current?.getFieldsValue();
    try {
      const response: any = await waybillExportAPI({
        // keyword: params?.keyword,
        // forecastStartTime: params?.time && formatTime(params?.time[0]),
        // forecastEndTime: params?.time && formatTime(params?.time[1]),
        ...params,
        flg: activeKey,
        ids: selectedRowKeys
      });
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      setExportLoading(false);
      saveAs(blob, newFileName);
    } catch (err) {
      setExportLoading(false);
      console.log('导出运单: ', err);
    }
  };
  /* 取消运单 */
  const cancelWaybill = async (id: any) => {
    try {
      const { status } = await cancelWaybillAPI({ id: id });

      if (status) {
        message.success('取消成功');
        ref.current.reloadAndRest();
      } else {
        /*渠道不支持添加强制取消*/
        //if(errorCode ==='UnsupportedOperation'){
        setCancelId(id);
        setIsCancelOpen(true);
        //}
      }
    } catch (err) {
      console.log('取消运单: ', err);
    }
  };
  const confirm = (e: any) => {
    // console.log(e);
    cancelWaybill(e);
  };
  const cancel = () => {
    message.info('已取消操作');
  };
  /* 打印面单 */
  const printWaybill = async (id: any) => {
    try {
      const { status, data }: any = await downloadWaybillAPI({ id });
      if (status) {
        window.open(data.ossUrl);
      }
    } catch (err) {
      console.log('打印面单: ', err);
    }
  };
  /* 打印发票  单个*/
  const getInvoice = async (id: any) => {
    try {
      const { status, data }: any = await downloadInvoiceAPI({ id });
      if (status) {
        window.open(data.ossUrl);
      }
    } catch (err) {
      console.log('打印面单: ', err);
    }
  };
  const columns: any = [
    {
      title: '查询',
      dataIndex: 'keyword',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入要查询的内容',
      },
    },
    {
      title: '时间',
      dataIndex: 'time',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <RangePicker showTime />)
      },
    },
    {
      title: '状态',
      width: 80,
      dataIndex: 'status',
      hideInSearch: true,
      render: (text: any) => {
        return (
          <Tag color={statusEnum[text].color}>{statusEnum[text].text}</Tag>
        );
      },
    },
    {
      title: '订单号',
      dataIndex: 'id',
      width: 220,
      fixed: 'left',
      hideInSearch: true,
      copyable: true,
      ellipsis: true,
    },
    {
      title: '跟踪号',
      dataIndex: 'expressCode',
      width: 200,
      hideInSearch: true,
      // copyable: true,
      // ellipsis: true,
      render: (text: any, record: any) => {
        return (
          <>
            {record?.expressCode ? <Paragraph
              copyable={{
                tooltips: true,
                text: record?.expressCode,
              }}
              style={{ marginBottom: 0 }}
            >
              <Link
                href={`https://www.ups.com/track?loc=en_US&tracknum=${record?.expressCode}&requester=WT/trackdetails`}
                target="_blank"
                rel="noopener noreferrer"
              >
                {record?.expressCode}
              </Link>
            </Paragraph> : '-'}
          </>
        );
      },
    },
    {
      title: '关联订单号',
      dataIndex: 'outerOrderId',
      width: 200,
      hideInSearch: true,
      copyable: true,
      ellipsis: true,
    },
    // {
    //   title: '客户',
    //   dataIndex: 'tenantName',
    //   width: 120,
    // },
    // {
    //   title: '客户ID',
    //   dataIndex: 'tenantId',
    //   width: 120,
    // },
    {
      title: '产品/渠道',
      dataIndex: 'channelName',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '件数',
      dataIndex: 'piecesNumber',
      width: 120,
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <span>
            {record.completedPieces}/{record.piecesNumber}
          </span>
        );
      },
    },
    // {
    //   title: '面单地址',
    //   dataIndex: 'piecesNumber',
    //   width: 120,
    // },
    {
      title: '预报重量 (KG)',
      dataIndex: 'totalWeight',
      width: 120,
      hideInSearch: true,
      render: (text: any) => {
        return text;
      },
    },
    {
      title: '预扣费金额 ($)',
      dataIndex: 'withholdFee',
      width: 120,
      hideInSearch: true,
      render: (text: any) => {
        return text;
      },
    },
    {
      title: '打单次数',
      dataIndex: 'printTimes',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '预报时间',
      dataIndex: 'insertTime',
      valueType: 'dateTime',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '签名服务',
      dataIndex: 'signature',
      valueType: 'signature',
      width: 200,
      hideInSearch: true,
      render: (_: any, recode: any) => {
        return signatureEnum[recode?.signature]
      }
    },
    {
      title: '取消时间',
      dataIndex: 'cancelTime',
      width: 200,
      hideInSearch: true,
      hideInTable: activeKey == '1',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      // align: 'center',
      width: 260,
      fixed: 'right',
      // hideInTable
      render: (text: any, record: any) => {
        return activeKey === '1' ? (
          <Space>
            <Access accessible={!access.WaybillReadOnly()}>
              <Button
                key="dy"
                type="link"
                disabled={!record.labelUrl}
                onClick={() => {
                  // window.open(record.labelUrl);
                  printWaybill(record.id);
                }}
              >
                面单
              </Button>
            </Access>
            {record?.channelName === 'UPS-test-CA' && <Button
              key="invoice"
              type="link"
              disabled={!record.labelUrl}
              onClick={() => {
                // window.open(record.labelUrl);
                getInvoice(record?.id)
              }}
            >
              发票
            </Button>}
            {/* {<a  onClick={() => getInvoice(record?.id)}>发票</a>} */}
            <Button
              type="link"
              key="editable"
              onClick={() => {
                history.push(`/waybillManagement/waybill/details`, {
                  ...record,
                  flg: activeKey,
                });
              }}
            >
              详情
            </Button>

            <Access accessible={!access.WaybillReadOnly()}>
              <Popconfirm
                key="delete"
                title="取消运单"
                description="想好了吗，确认取消运单?"
                onConfirm={() => confirm(record.id)}
                onCancel={cancel}
                okText="确认"
                cancelText="再想想"
              >
                <a style={{ color: 'red' }}>撤销</a>
              </Popconfirm>
            </Access>
          </Space>
        ) : (
          <a
            key="editable"
            onClick={() => {
              history.push(`/waybillManagement/waybill/details`, {
                ...record,
                flg: activeKey,
              });
            }}
          >
            详情
          </a>
        );
      },
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      setSelectedRowKeys(selectedRowKeys)
    },
  };
  // 下载面单
  const getDownload = async () => {
    if (selectedRowKeys.length <= 0) return message.warning('请选择运单')
    try {
      const response: any = await getDownloadLabelsSheet({ ids: selectedRowKeys });
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename*=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
      message.success('下载成功')
      setSelectedRowKeys([])
      refresh
    } catch (err) {
    }
  }
  // 下载发票
  const getInvoiceBatch = async () => {
    if (selectedRowKeys.length <= 0) return message.warning('请选择运单')
    try {
      const response: any = await downloadInvoicesAPI({ ids: selectedRowKeys });
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename*=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
      message.success('下载成功')
      setSelectedRowKeys([])
      refresh
    } catch (err) {
    }
  }
  const getSearchShake = _.debounce((e: any, type: string) => {
    if (type === 'tail_after') {
      setParams({ ...params, expressCode: e?.target?.value })

    } else {
      setParams({ ...params, outerId: e?.target?.value })
    }
    refresh()
  }, 500)
  const refresh = () => ref?.current?.reloadAndRest() 

  return (
    <>
      <div className={styles.searchWrap}>
        <div className={styles.item1}>
          <div className={styles.search_wrap}>
            <Select
              defaultValue="批量"
              bordered={false}
              style={{
                height: '100%',
                border: 'none',
                backgroundColor: '#f7f7f7',
              }}
              onChange={(value: any) => {
                setParams({ ...params, termQuery: value });
                refresh()
              }}
              options={[
                { value: 'true', label: '精确' },
                { value: 'false', label: '批量' },

              ]}>

            </Select>

            {/* <Input
              name="keyword"
              style={{
                flex: 1,
                resize: 'none',
                padding: '10px',
              }}
              placeholder="请输入ID,运单号，客户代码，客户名称，业务员搜索"
              bordered={false}
              className={styles.search_back}
              // onPressEnter={() => {
              //   setParams({ ...params, keyword: keyword })
              //   refresh()
              // }}
              // value={keyword}
              onChange={(e: any) => {
                // // console.log(e,'eeee');
                // var inputString = "2323232323,322,3,23,23,23,23,232323232332323232323332323323333232323323323332323232332333223";
                // var splitArray = inputString.split(",");
                // var resultString = splitArray.join("\n");
                // console.log(resultString, 'resultStringresultString');

                setKeyword(e?.target?.value)
              }}
            /> */}
            <TextArea style={{ border: 'none', resize: 'none', height: '77px', borderRadius: '0px', paddingTop: '27px' }} onChange={(e) => {
              setKeyword(e?.target?.value)
            }} placeholder="请输入关键词搜索"
              onPressEnter={() => {
                setParams({ ...params, keyword: keyword })
                refresh()
              }} />
            <Button
              style={{ height: '100%', color: '#537ffd', border: 'none' }}
              className={styles.search_back}
              onClick={() => {
                setParams({ ...params, keyword: keyword })
                refresh()
              }}
            >
              搜索
            </Button>
          </div>
        </div>
        <div className={styles.searchRight}>
          <div className={styles.rightTime}>
            <div className={styles.forecastTime}>
              <span> 预报时间：</span>
              <RangePicker format="YYYY-MM-DD HH:mm:ss" style={{ width: '220px' }} showTime={{ defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')] }}
                onChange={(e: any) => {
                  if (e?.length > 0) {
                    setParams({ ...params, forecastStartTime: formatTime(e[0]), forecastEndTime: formatTime(e[1]) })
                  } else {
                    setParams({ termQuery: params?.termQuery, keyword: params?.keyword })
                  }
                  refresh()
                }}
              />
            </div>
            <div className={styles.forecastTime}>
              <span> 签收证明：</span>
              <Select
                placeholder='请选择'
                allowClear
                // defaultValue="批量"
                // bordered={false}
                style={{
                  width: '220px',
                  // border: 'none',
                  // marginTop: '20px',
                  backgroundColor: '#f7f7f7',
                }}
                onChange={(value: any) => {
                  setParams({ ...params, signature: value });
                  refresh()
                }}
                options={[
                  { value: '0', label: '不需要' },
                  { value: '1', label: '需要' },

                ]} >
                {/* <Option value="0">不需要</Option>
            <Option value="1">需要</Option> */}
              </Select>
              {/* <RangePicker format="YYYY-MM-DD HH:mm:ss" showTime={{ defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')] }}
            onChange={(e: any) => {
              if (e?.length > 0) {
                setParams({ ...params, forecastStartTime: formatTime(e[0]), forecastEndTime: formatTime(e[1]) })
              } else {
                setParams({ termQuery: params?.termQuery, keyword: params?.keyword })
              }
              refresh()
            }}
          /> */}
            </div>
          </div>
          <div className={styles.rightTime}>
            <div className={styles.forecastTime}>
              <span> 跟踪号：</span>
              <Input
                placeholder='请输入跟踪号'
                allowClear
                // defaultValue="批量"
                // bordered={false}
                style={{
                  width: '220px',

                }}
                onChange={(e) => getSearchShake(e, 'tail_after')} />
            </div>
            <div className={styles.forecastTime}>
              <span> 关联订单号：</span>
              <Input
                placeholder='请输入关联订单号'
                allowClear
                // defaultValue="批量"
                // bordered={false}
                style={{
                  width: '220px',

                }}
                onChange={(e) => getSearchShake(e, 'relevance')} />
              {/* <Option value="0">不需要</Option>
            <Option value="1">需要</Option> */}

              {/* <RangePicker format="YYYY-MM-DD HH:mm:ss" showTime={{ defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')] }}
            onChange={(e: any) => {
              if (e?.length > 0) {
                setParams({ ...params, forecastStartTime: formatTime(e[0]), forecastEndTime: formatTime(e[1]) })
              } else {
                setParams({ termQuery: params?.termQuery, keyword: params?.keyword })
              }
              refresh()
            }}
          /> */}
            </div>
          </div>
        </div>
      </div>

      <Modal title="提示" open={isCancelOpen} onOk={handleCancelOk} onCancel={handleCancel}>
        <p>上游渠道取消异常，是否强制完成取消?</p>
      </Modal>
      <ProTable
        columns={columns}
        actionRef={ref}
        formRef={objRef}
        // cardBordered
        request={async (paramsData: any) => {
          const pageSize = localStorage.getItem('waybilSize')
          const msg = await getWaybillListAPI({
            start: (paramsData.current - 1) * paramsData.pageSize,
            len: pageSize || 10,
            flg: activeKey,
            // keyword: params.keyword,
            // forecastStartTime: params.time && formatTime(params.time[0]),
            // forecastEndTime: params.time && formatTime(params.time[1]),
            ...params
          });

          return {
            data: msg?.data?.list || [],
            success: msg.status,
            total: msg.data.amount || 0,
          };
        }}

        columnsState={{
          persistenceKey: 'waybill',
          persistenceType: 'localStorage'
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          collapsed: false,
          optionRender: false
        }}
        className={styles.waybillSearch}
        options={{
          fullScreen: true,
        }}
        pagination={{
          defaultPageSize: localStorage.getItem('waybilSize') as any || 10,
          // onChange: (page) => console.log(page),
          showSizeChanger: true,
        }}
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        onChange={({ pageSize }: any) => {
          localStorage.setItem('waybilSize', pageSize)
        }}
        scroll={{ x: 1200 }}
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: [
              {
                key: '1',
                label: <span>运单列表</span>,
              },
              {
                key: '0',
                label: <span>回收站</span>,
              },
            ],
            onChange: (key) => {
              setActiveKey(key as string);
              ref.current.reloadAndRest();
            },
          },
        }}
        toolBarRender={() => [
          // <Button key="show" type="primary">查看日志</Button>,
          <Access accessible={!access.WaybillReadOnly()} key="out">
            <Button
              key="revocation"
              type="primary"

              onClick={handleBatchCancelOk}>
              批量撤销
            </Button>
            <Button
              key="face"
              type="primary"

              onClick={getDownload}>
              下载面单
            </Button>
            <Button
              key="face"
              type="primary"

              onClick={getInvoiceBatch}
            >
              下载发票
            </Button>
            <Button
              key='exprot_data'
              disabled={activeKey === '0'}
              type="primary"
              onClick={exportWaybill}
              loading={exportLoading}
            >
              导出数据
            </Button>
          </Access>,
        ]}
      />
    </>
  );
};
