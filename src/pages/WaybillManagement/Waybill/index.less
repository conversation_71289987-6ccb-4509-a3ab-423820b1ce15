.waybillSearch {
    :global {
        .ant-form  {
            display: none !important;
        }
        .ant-pro-card-border {
            border: none !important;
        }
    }
}
.listWrap{
    .kou{
     background: #ffeae9;
     :global( .ant-table-cell-row-hover)
     {
      background: #FFE1E0 !important;
     }
     :global( .ant-table-cell-fix-right)
     {
      background: #ffeae9 ;
     }
    }
   
    :global( .ant-pagination){
     position: fixed;
     bottom: 2px ;
     right: 30px;
     z-index: 999;
    }
    margin-bottom: 20px;
   }
   /*自定义搜索表单*/
   .searchWrap{
    width: 100%;
    background: white;

    // margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    // flex-wrap: wrap;
    padding: 20px 12px 0px 12px;
    grid-template-columns: 1fr 1fr 1fr;
    .item1 {
        width: 40%;
     grid-area: header;
     .search_wrap{
      border:1px solid #e8e8e8;
      height: 80px;
      display: flex;
      width:442px;
      border-radius:8px;
      margin-bottom: 20px;
      :global(.ant-select-selector){
       height: 100%;
       :global(.ant-select-selection-item){
       display: flex;
       align-items: center;
      }
     }
     }
    }
    .searchRight {
        width: 60%;

        display: flex;
        flex-direction: column;
        // justify-content: space-evenly;
        // align-items: center;
        // flex-wrap: wrap;
        .rightTime {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        .forecastTime {
            display: flex;
            align-items: center;
            // width: 600px;
            // margin-top: 24px;
            margin-left: 20px;
        }
    }
 
    grid-template-areas:
       "header header2 header3"
       "header main sidebar"
       "menu footer2 footer3";
   }
   :global(#contentBodyBox) {
     //overflow: hidden !important;
   }
   /*这个可以通用*/
   .search_back{
     background-color: #fcfcfc;
    :global(.ant-select-selector){
     background-color: #fcfcfc !important;
    }
    :global(.ant-picker){
     background-color: #fcfcfc !important;
    }
   }