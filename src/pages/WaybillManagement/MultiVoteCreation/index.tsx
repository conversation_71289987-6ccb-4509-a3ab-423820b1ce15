import { ProCard } from '@ant-design/pro-components';
// import SendAddress from '@/components/SendAddress';
import { Button, Divider, Space, Spin, Tag, message } from 'antd';
import useDownloadFile from '@/hooks/useDownloadFile';
import ExcelReader from '@/components/ExcelReader';
import { useState } from 'react';
import service from '@/services/home';
import { history, useAccess, Access } from 'umi';
const { getWaybillBatchCreateAPI } = service.UserHome;
const MultiVoteCreation = () => {
  const access = useAccess();
  const [handleDownload, downloading]: any = useDownloadFile(
    'https://web-common.kmfba.com/static/%E8%AE%A2%E5%8D%95%E9%A2%84%E6%8A%A5%E6%A8%A1%E6%9D%BF.xlsx',
  );
  const [dataObj, setDataObj] = useState<any>({
    waybillFile: null,
    // address: null,
    name: null,
  });
  // const sendAddOnChange = (value: any) => {
  //   setDataObj({
  //     ...dataObj,
  //     address: value,
  //   });
  // };
  const onExcelData = (data: any, file: any) => {
    setDataObj({
      ...dataObj,
      waybillFile: file,
      name: file.name,
    });
  };
  const handleDownloadTemplate = () => {
    handleDownload('订单预报模版.xlsx');
  };
  /* 多票创建 */
  const [loading, setLoading] = useState<boolean>(false);
  const handleMultiVoteCreation = async () => {
    setLoading(true);
    try {
      const { status } = await getWaybillBatchCreateAPI({
        waybillFile: dataObj.waybillFile,
      });
      if (status) {
        message.success('多票创建成功');
        history.push('/waybillManagement/waybill');
      }
      setLoading(false);
    } catch (e) {
      console.error('多票创建接口出错', e);
      setLoading(false);
    }
  };
  return (
    <Spin spinning={loading}>
      <ProCard style={{ height: '400px' }}>
        {/* <SendAddress onChange={sendAddOnChange} style={{marginTop:12}} /> */}

        <div style={{ marginTop: 12 }}>
          <Space>
            <ExcelReader onChange={onExcelData}>
              <Button type="primary" ghost>
                选择文件
              </Button>
            </ExcelReader>
            <Button
              type="primary"
              ghost
              onClick={handleDownloadTemplate}
              loading={downloading}
            >
              下载模版
            </Button>
          </Space>
        </div>
        <div style={{ marginTop: 12 }}>
          {!dataObj?.name ? null : (
            <Tag
              color="green"
              closable
              onClose={() => {
                setDataObj({
                  ...dataObj,
                  waybillFile: null,
                  name: null,
                });
              }}
            >
              文件上传成功：{dataObj?.name}
            </Tag>
          )}
        </div>
        <Divider />

        <Access
          accessible={!access.WaybillReadOnly()}
          fallback={<div>无操作权限.</div>}
        >
          <Button
            type="primary"
            disabled={!dataObj?.waybillFile}
            onClick={handleMultiVoteCreation}
          >
            立即预报
          </Button>
        </Access>
      </ProCard>
    </Spin>
  );
};
export default MultiVoteCreation;
