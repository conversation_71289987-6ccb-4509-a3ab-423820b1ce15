import { ProList } from '@ant-design/pro-components';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Empty, Tag, Switch } from 'antd';
import React, { useState } from 'react';

const dataSource = [
  {
    name: 'US-美东',
  },
  {
    name: 'US-美东',
  },
  {
    name: 'US-美东',
  },
];

const renderBadge = (count: number, active = false) => {
  return (
    <Badge
      count={count}
      style={{
        marginBlockStart: -2,
        marginInlineStart: 4,
        color: active ? '#1890FF' : '#999',
        backgroundColor: active ? '#E6F7FF' : '#eee',
      }}
    />
  );
};

export default () => {
  const [activeKey, setActiveKey] = useState<React.Key | undefined>('tab1');
  return (
    <>
      <ProList<any>
        rowKey="name"
        dataSource={dataSource}
        metas={{
          title: {
            dataIndex: 'name',
          },
          description: {
            // dataIndex: 'desc',
          },
          content: {
            dataIndex: 'content',
            render: (text) => (
              <div
                key="label"
                style={{ display: 'flex', justifyContent: 'space-around' }}
              >
                <div style={{ width: 100 }}>
                  <Tag color="red">GFP</Tag>
                </div>
                <div style={{ width: 200 }}>提点：7%</div>
                <div style={{ width: 200 }}>发件地址：California US</div>
                <div style={{ width: 100 }}>
                  {' '}
                  <Switch defaultChecked />
                </div>
              </div>
            ),
          },
          actions: {
            render: (text, row) => [
              <a
                href={row.html_url}
                target="_blank"
                rel="noopener noreferrer"
                key="link"
              >
                详情
              </a>,
              <a
                href={row.html_url}
                target="_blank"
                rel="noopener noreferrer"
                key="warning"
              >
                设置
              </a>,
            ],
          },
        }}
        headerTitle="1. 渠道 http://hyeus.net/"
        toolbar={{
          search: false,
          actions: [<Button type="primary">添加渠道</Button>],
        }}
      />
      <br></br>
      <br></br>
      <br></br>
      <Card title="2. 渠道 https://www.speedtrans.vip/e" bordered={false}>
        <Empty
          image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
          imageStyle={{
            height: 60,
          }}
          description={
            <span>
              还没有任务渠道 <a href="#API">立即添加</a>
            </span>
          }
        ></Empty>
      </Card>
    </>
  );
};
