import { useLocation } from 'umi';
import DetailsCard from '@/components/DetailsCard';
import service from '@/services/home'
import {formatTime} from '@/utils/format'
import { useEffect, useRef, useState } from 'react';
import { ProTable } from '@ant-design/pro-components';
const {getReconciliationWaybillDetailAPI,getWaybillDetailAPI}  = service.UserHome
const Details = ()=>{
  const actionRef = useRef<any>();
  const location = useLocation();
  const {state:newData}:any = location
  /* 获取对账详情 */
  const [list,setList] = useState<any>({})
  const getReconciliationWaybillDetail = async()=>{
    try{
      const {status,data} = await getReconciliationWaybillDetailAPI({id:newData.id})
      if(status){
        setList({
          ...data,
          reconciliation:[
            {
              title:'客户',
              content:data?.tenantName
            },
            {
              title:'运单号',
              content:data?.expressCode
            },
            {
              title:'实销金额',
              content:data?.payment+ ' $'
            },
            
            {
              title:'客户id',
              content:data?.tenantId
            },
            {
              title:'包裹数',
              content:data?.piecesNumber
            },
            // {
            //   title:'成本',
            //   content:data?.cost + ' $'
            // },
            {
              title:'渠道',
              content:data?.channelName
            },
            {
              title:'客户订单',
              content:data?.outerOrderId
            },
            // {
            //   title:'利润',
            //   content:data?.profit+ ' $'
            // },
            {
              title:'最新对账时间',
              content:formatTime(data?.latestBillTime) || ''
            },
          ]
        })
      }
    }catch(err){
      console.log('获取对账详情: ', err);
    }
  }
  const columns:any = [
    {
      title: '子单号',
      dataIndex: 'extraId_2',
      width: 200,
      fixed: 'left',
    },
    {
      title: '事项',
      dataIndex: 'reason',
    },
    {
      title: '时间',
      dataIndex: 'time',
      render: (text: any) => {
        return <span>{formatTime(text)}</span>;
      },
    },
    {
      title: '金额（$）',
      dataIndex: 'amount',
    },
    // {
    //   title: '成本（$）',
    //   dataIndex: 'cost',
    // },
    // {
    //   title: '利润（$）',
    //   dataIndex: 'profit',
    // },
  ]
  useEffect(() => {
    getReconciliationWaybillDetail()
  }, [])
  return <>
    <DetailsCard title='基本信息' list={list.reconciliation || []} />
    <DetailsCard title='包裹信息' list={[]}>
    <ProTable<any>
      columns={columns}
      actionRef={actionRef}
      request={async (params: any) => {
        const msg = await getWaybillDetailAPI({
          id: newData.id,
          start: (params.current - 1) * params.pageSize,
          len: params.pageSize,
        });
        return {
          data: msg?.data?.list || [],
          success: msg.status,
          total: msg.data.amount,
        };
      }}
      rowKey="id"
      search={false}
      options={false}
      scroll={{ x: 1200 }}
      pagination={{
        pageSize: 10,
      }}
    />
    </DetailsCard>
  </>
}

export default Details