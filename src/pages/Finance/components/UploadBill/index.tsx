import { Button, Modal, message } from 'antd';
import { useEffect, useState } from 'react';
import service from '@/services/home';
import ConfigList from './ConfigList';
import styles from '../index.less';
const { getReconciliationConfigListAPI ,addReconciliationConfigAPI,deleteReconciliationConfigAPI,updateReconciliationConfigAPI} = service.UserHome;
const UploadBill = ({refresh}:any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  /* 获取配置列表 */
  const [configList, setConfigList] = useState<any>([]);
  const getReconciliationConfigList = async (params: any) => {
    try {
      const { status, data } = await getReconciliationConfigListAPI(params);
      if (status) {
        setConfigList([...data.list])
      }
    } catch (error) {
      console.log('获取配置列表失败', error);
    }
  };
  /* 新建配置 */
  /* 新增loading */
  const [addLoading, setAddLoading] = useState(false);
  const addReconciliationConfig = async () => {
    setAddLoading(true);
    try {
      const { status } = await addReconciliationConfigAPI({
        name: '新增配置',//配置名称
        issueNo: 'A',//期号
        subExpressCode:'B',//ups
        outerOrderId:'C',//关联订单号
        amount:'D',//金额
        reason:'E',//原因
      });
      if (status) {
        message.success('新建配置成功');
        getReconciliationConfigList({
          start: 0,
          len: 10,
        });
      }
      setAddLoading(false);
    } catch (error) {
      console.log('新建配置失败', error);
      setAddLoading(false);
    }
  };
  /* 删除接口 */
  const deleteReconciliationConfig = async (id: any) => {
    try {
      const { status } = await deleteReconciliationConfigAPI({
        id,
      });
      if (status) {
        message.success('删除成功');
        getReconciliationConfigList({
          start: 0,
          len: 10,
        });
      }
    } catch (error) {
      console.log('删除失败', error);
    }
  };
  /* 修改配置 */
  const updateReconciliationConfig = async (params:any) => {
    try {
      const { status } = await updateReconciliationConfigAPI({
        ...params
      });
      if (status) {
        message.success('修改成功');
        getReconciliationConfigList({
          start: 0,
          len: 10,
        });
      }
    } catch (error) {
      console.log('修改失败', error);
    }
  };

  useEffect(() => {
    getReconciliationConfigList({
      start: 0,
      len: 10,
    });
  }, []);

  return (
    <>
      <Button type="primary" onClick={showModal}>
        上传账单
      </Button>
      <Modal
        className={styles.modalWarp}
        title={[
          <span key="1">上传账单</span>,
          <span key="2">
            <Button type="link" onClick={addReconciliationConfig} loading={addLoading}>新建配置</Button>
          </span>,
          <div
            key="3"
            style={{ fontSize: '14px', fontWeight: 400, color: '#AEAEAE' }}
          >
            上传excel对账单，进行批量对账，请先配置excel中对应字段所处列的位置（字母）
          </div>,
        ]}
        width={850}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        {
          configList.map((item: any) => {
            return <ConfigList refresh={refresh} key={item.id} configList={item} updateReconciliationConfig={updateReconciliationConfig} deleteReconciliationConfig={deleteReconciliationConfig} />
          })
        }
      </Modal>
    </>
  );
};

export default UploadBill;
