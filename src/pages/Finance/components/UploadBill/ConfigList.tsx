import { Button, Col, Input, Modal, Row, Select, Spin, Upload, message } from 'antd';
import styles from '../index.less';
import {
  CheckCircleFilled,
  CloseCircleFilled,
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleFilled,
  UploadOutlined,
} from '@ant-design/icons';
import { useState } from 'react';
import { REQUESTADDRESS_UPLOAD } from '@/globalData';
const { confirm } = Modal;
const ConfigList = ({
  configList,
  deleteReconciliationConfig,
  updateReconciliationConfig,
  refresh
}: any) => {
  /**
   * @configList
   */
  /* 26个大写字母 */
  const options = [
    { label: 'A', value: 'A' },
    { label: 'B', value: 'B' },
    { label: 'C', value: 'C' },
    { label: 'D', value: 'D' },
    { label: 'E', value: 'E' },
    { label: 'F', value: 'F' },
    { label: 'G', value: 'G' },
    { label: 'H', value: 'H' },
    { label: 'I', value: 'I' },
    { label: 'J', value: 'J' },
    { label: 'K', value: 'K' },
    { label: 'L', value: 'L' },
    { label: 'M', value: 'M' },
    { label: 'N', value: 'N' },
    { label: 'O', value: 'O' },
    { label: 'P', value: 'P' },
    { label: 'Q', value: 'Q' },
    { label: 'R', value: 'R' },
    { label: 'S', value: 'S' },
    { label: 'T', value: 'T' },
    { label: 'U', value: 'U' },
    { label: 'V', value: 'V' },
    { label: 'W', value: 'W' },
    { label: 'X', value: 'X' },
    { label: 'Y', value: 'Y' },
    { label: 'Z', value: 'Z' },
  ];

  const [list, setList] = useState<any>([
    {
      label: '期号',
      value: configList.issueNo || '',
      id: 1,
    },
    {
      label: 'UPS单号',
      value: configList.outerOrderId || '',
      id: 2,
    },
    {
      label: '关联订单号',
      value: configList.subExpressCode || '',
      id: 3,
    },
    {
      label: '金额',
      value: configList.amount || '',
      id: 4,
    },
    {
      label: '原因',
      value: configList.reason || '',
      id: 5,
    },
  ]);

  /* 标题 */
  const [isTitle, setIsTitle] = useState<any>(configList.name || '');

  /* 编辑 */
  const [editNot, setEditNot] = useState<boolean>(false);
  const edit = () => {
    setEditNot(true);
  };
  /* 重置 */
  // const reset = () => {
  //   setList([
  //     {
  //       label: '期号',
  //       value: 'A',
  //       id: 1,
  //     },
  //     {
  //       label: 'UPS单号',
  //       value: 'B',
  //       id: 2,
  //     },
  //     {
  //       label: '关联订单号',
  //       value: 'C',
  //       id: 3,
  //     },
  //     {
  //       label: '金额',
  //       value: 'D',
  //       id: 4,
  //     },
  //     {
  //       label: '原因',
  //       value: 'E',
  //       id: 5,
  //     },
  //   ]);
  //   updateReconciliationConfig({
  //     id: configList.id,
  //     name: '默认配置',
  //     issueNo: 'A',
  //     outerOrderId: 'C',
  //     subExpressCode: 'B',
  //     amount: 'D',
  //     reason: 'E',
  //   }).then(() => {
  //     setEditNot(false);
  //   });
  // };
  /* 保存配置 */
  const saveConfig = () => {
    updateReconciliationConfig({
      id: configList.id,
      name: isTitle,
      issueNo: list[0].value,
      outerOrderId: list[1].value,
      subExpressCode: list[2].value,
      amount: list[3].value,
      reason: list[4].value,
    }).then(() => {
      setEditNot(false);
    });
  };
  /* 删除配置 */
  /* 删除loading */
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);

  const deleteConfig = () => {
    confirm({
      title: '确定删除吗？',
      icon: <ExclamationCircleFilled />,
      content: '确定删除此项配置吗？',
      onOk() {
        setDeleteLoading(true);
        deleteReconciliationConfig(configList.id).then(() => {
          setDeleteLoading(false);
        });
      },
      onCancel() {
        message.info('取消删除');
      },
    });
  };
  /* uploading */
  const [uploading, setUploading] = useState<boolean>(false);
  /* 上传Excel */
  const [updataList, setUpdataList] = useState<any>(null);
  /* 是否上传 */
  const [isUpload, setIsUpload] = useState<boolean>(false);
  const props: any = {
    name: 'multiFile',
    action: `${REQUESTADDRESS_UPLOAD}?service_id=WaybillManagement`,
    accept: '.xls,.xlsx',
    headers: {
      // authorization: 'authorization-text',
      // "Content-Type": "multipart/form-data",
      token: localStorage.getItem('token'),
    },

    withCredentials: true,
    maxCount: 1,
    data: () => {
      return {
        configId: configList.id,
        // multiFile:e
      };
    },
    method: 'POST',
    beforeUpload(file: any) {
      const isXls = file.type === 'application/vnd.ms-excel';
      const isXlsx =
        file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      if (!isXls && !isXlsx) {
        message.error('只能上传xls或xlsx格式的文件');
      }
      return isXls || isXlsx;
    },

    onChange(info: any) {
      if (info.fileList.length >= 1) {
        setIsUpload(true);
      } else {
        setIsUpload(false);
      }
      if (info.file.status === 'uploading') {
        setUploading(true);
      }else{
        setUploading(false);
      }
      if (info.file.status === 'done') {
        // message.success(`${info.file.name} 上传文件成功`);
        if (info.file.response.status) {
          refresh();
          setUpdataList(info.file.response);
        } else {
          setUpdataList(info.file.response);
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传文件失败`);
      }
    },
  };

  /* 状态 */
  const ListBoxStatus = ()=>{
    if(updataList === null){
      return <></>
    }
    if(updataList.status===true && isUpload){
      return <div className={styles.successBox}><CheckCircleFilled /> 处理成功，完成对账</div>
    }
    if(updataList.status===false && isUpload){
      return <div className={styles.errorBox}><CloseCircleFilled /> {updataList.errorDesc}</div>
    }
    if(uploading){
      return <div className={styles.loadingBox}><Spin /> 处理中</div>
    }
    return <></>
  }

  return (
    <div className={styles.configBox}>
      <div className={styles.confTop}>
        <div className={styles.title}>
          {editNot ? (
            <Input
              value={isTitle}
              onChange={(e: any) => {
                setIsTitle(e.target.value);
              }}
            />
          ) : (
            <span>{isTitle}</span>
          )}
        </div>
        <div>
          {editNot ? (
            <>
              <Button type="link" onClick={saveConfig}>
                保存配置
              </Button>
              <Button type="link" onClick={() => setEditNot(false)}>
                取消
              </Button>
            </>
          ) : (
            <>
              <Button type="link" icon={<EditOutlined />} onClick={edit} disabled={isUpload}>
                编辑
              </Button>
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={deleteConfig}
                loading={deleteLoading}
                disabled={isUpload}
              >
                删除
              </Button>
            </>
          )}
        </div>
      </div>
      <div>
        <Row>
          {list.map((item: any) => {
            return (
              <Col flex={1} className={styles.colBox} key={item.id}>
                <div>
                  <span>{item.label}：</span>
                  <span>
                    {editNot ? (
                      <Select
                        showSearch
                        placeholder="请选择或输入"
                        options={options}
                        value={item.value}
                        style={{ width: '80px' }}
                        onChange={(value: any) => {
                          item.value = value;
                          setList([...list]);
                        }}
                      />
                    ) : (
                      item.value
                    )}
                  </span>
                </div>
              </Col>
            );
          })}
        </Row>
      </div>
      <div className={styles.confFooter}>
        <div className={styles.footerBox}>
          <ListBoxStatus />
          <Upload {...props} >
            {isUpload ? null : (
              <Button type="link" icon={<UploadOutlined />} disabled={editNot}>
                上传Excel
              </Button>
            )}
          </Upload>
        </div>
      </div>
    </div>
  );
};
export default ConfigList;
