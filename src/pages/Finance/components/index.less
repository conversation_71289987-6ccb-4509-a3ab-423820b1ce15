.modalWarp{
  :global(.ant-modal-content){
    background:#F4F4F4 ;
  }

  :global(.ant-modal-header){
    background:#F4F4F4 ;
  }
}

.configBox{
  width: 100%;
  background: #fff;
  border-radius: 2px;
  padding:10px 15px;
  margin-bottom: 20px;

  .confTop{
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 24px;
    margin-bottom: 10px;

    .title{
      font-size: 16px;
      font-weight: 600;
      color: #333;
      line-height: 24px;
    }
  }

  
  
  .colBox{
    color: #707070;
    font-size: 15px;
    outline: 1px solid #f4f4f4;
    outline-offset: -1px;
    line-height: 50px;
    padding-left: 12px;
  }

  .confFooter{
    display: flex;
    justify-content: center;
    margin-top: 10px;

    .footerBox{
      display: flex;
      align-items: flex-end;
    }

    .errorBox{
      font-size: 14px;
      font-weight: 500;
      color: #EC841A;
      line-height: 24px;
      margin-right: 15px;
    }

    .successBox{
      font-size: 14px;
      font-weight: 500;
      color: #12B400;
      line-height: 24px;
      margin-right: 15px;
    }
    
    .loadingBox{
      width: 48px;
      font-weight: 500;
      color: #4071FF;
      line-height: 24px;
    }
  }
}