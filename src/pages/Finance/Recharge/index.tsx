import  { useState } from 'react';
import { Tabs } from 'antd';
import styles from './index.less';
import RechargeEntrance from './RechargeEntrance';
import RechargeRecord from './RechargeRecord';
const Recharge = () => {
  const [activeKey, setActiveKey] = useState<string>('1');
  return (
    <>
      <Tabs
        className={styles.tabBox}
        activeKey={activeKey}
        items={[
          {
            label: '充值',
            key: '1',
            children: <RechargeEntrance setActiveKey={setActiveKey} />,
          },
          {
            label: '充值记录',
            key: '2',
            children: <RechargeRecord />,
          },
        ]}
        onChange={(key) => {
          setActiveKey(key);
        }}
      />
    </>
  );
};

export default Recharge;
