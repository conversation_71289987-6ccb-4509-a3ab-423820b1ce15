import { ProCard } from '@ant-design/pro-components';
import { Radio, InputNumber, Form, Button, message, Spin, Modal } from 'antd';
import service from '@/services/home';
import { useState } from 'react';
import { CheckCircleOutlined } from '@ant-design/icons';
const {payMoneyAPI} = service.UserHome;
const { confirm } = Modal;
export default ({setActiveKey}:any) => {
  /* loding */
  const [loading, setLoading] = useState<boolean>(false);
  
  /* 支付 */
  const payMoney = async (values: any) => {
    setLoading(true);
    try{
      const { status,data } = await payMoneyAPI({
        chargeAmount:values.chargeAmount,
      });
      if (status) {
        message.success('提交成功,正在跳转');
        setTimeout(() => {
          //window 上挂载一个方法，供支付宝回调使用
          window.open(data, '_blank');
          window.alipayCallback = function () {
            confirm({
              title: '充值成功！',
              icon: <CheckCircleOutlined />,
              content: '充值成功，是否跳转到充值记录？',
              onOk() {
                setActiveKey('2')
                message.success('跳转成功')
              },
              onCancel() {
                message.info('取消跳转')
              },
            });
          }
        }, 1000);
      } 
      setLoading(false)
    }catch(err){
      setLoading(false)
      console.log('充值接口出错',err);
    }
  };
  const onFinish = (values: any) => {
    payMoney(values)
  };
  
  return (
    <Spin spinning={loading} tip="提交成功，请等待跳转">
      <ProCard bordered style={{ height: '500px' }}>
        <Form
          name="basic"
          style={{
            maxWidth: 600,
          }}
          initialValues={{
            paymentMethod:'支付宝',
          }}
          autoComplete="off"
          onFinish={onFinish}
        >
          <Form.Item
            label="支付方式"
            name="paymentMethod"
            rules={[
              {
                required: true,
                message: 'Please input your paymentMethod!',
              },
            ]}
          >
            <Radio.Group>
              <Radio value='支付宝'>支付宝</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="支付金额"
            name="chargeAmount"
            rules={[
              {
                required: true,
                message: '支付金额不能为空',
              },
            ]}
          >
            <InputNumber style={{ width: '60%' }} placeholder="请输入金额" />
          </Form.Item>
          <Form.Item
            wrapperCol={{
              offset: 3,
            }}
          >
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </ProCard>
    </Spin>
  );
};
