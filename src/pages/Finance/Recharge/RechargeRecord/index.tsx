// import { DownOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import service from '@/services/home';
const {getRechargeDetailAPI } = service.UserHome;
const valueEnum: any = {
  0: 'close',
  1: 'running',
  2: 'online',
  3: 'error',
};

export type TableListItem = {
  key: number;
  name: string;
  containers: number;
  creator: string;
  status: string;
  createdAt: number;
  memo: string;
};
const tableListDataSource: TableListItem[] = [];

const creators = ['付小小', '曲丽丽', '林东东', '陈帅帅', '兼某某'];

for (let i = 0; i < 5; i += 1) {
  tableListDataSource.push({
    key: i,
    name: 'AppName',
    containers: Math.floor(Math.random() * 20),
    creator: creators[Math.floor(Math.random() * creators.length)],
    status: valueEnum[Math.floor(Math.random() * 10) % 4],
    createdAt: Date.now() - Math.floor(Math.random() * 100000),
    memo:
      i % 2 === 1
        ? '很长很长很长很长很长很长很长的文字要展示但是要留下尾巴'
        : '简短备注文案',
  });
}

const columns: ProColumns<TableListItem>[] = [
  // {
  //   title: '客户名称',
  //   width: 200,
  //   dataIndex: 'tenantName',
  //   search: false,
  // },
  // {
  //   title: '客户ID',
  //   dataIndex: 'tenantId',
  // },
  {
    title: '查询',
    dataIndex: 'keyword',
    hideInTable: true,
    fieldProps: {
      placeholder:'请输入要查询的内容'
    },
  },
  {
    title: '充值金额 (元)',
    // search: false,
    dataIndex: 'rechargeAmount',
    hideInSearch: true,
  },
  {
    title: '入账金额 ($)',
    hideInSearch: true,
    dataIndex: 'recordedAmount',
  },
  {
    title: '汇率',
    hideInSearch: true,
    dataIndex: 'exchangeRate',
    tooltip: '汇率（美元->人民币)',
  },
  {
    hideInSearch: true,
    title: '充值渠道',
    dataIndex: 'channelName',
    valueEnum: {
      0: '支付宝',
      1: '微信',
    },
  },
  {
    title: '充值时间',
    width: 140,
    hideInSearch: true,
    dataIndex: 'time',
    valueType: 'dateTime'
  },
];

export default () => {
  return (
      <ProTable<TableListItem>
        columns={columns}
        request={async (params:any) => {
          const msg = await getRechargeDetailAPI({
            start: (params.current - 1)*params.pageSize,
            len: params.pageSize,
            keyword: params.keyword,
          });
          return {
            data: msg?.data?.list || [],
            success: msg.status,
            total: msg.data.amount || 0,
          };
        }}
        rowKey="billId"
        pagination={{
          showQuickJumper: true,
          pageSize: 10,
        }}
        options={{
          fullScreen: true,
        }}
        search={{
          optionRender: false,
          collapsed: false,
          labelWidth: 'auto',
        }}
        dateFormatter="string"
        headerTitle=""
        toolBarRender={() => []}
      />
  );
};
