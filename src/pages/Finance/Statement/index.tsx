import { ProTable } from '@ant-design/pro-components';
import { useRef } from 'react';
import { Button, DatePicker } from 'antd';
import service from '@/services/home';
import {convertToTimestamp, formatTime} from '@/utils/format';
// import UploadBill from '../components/UploadBill';
import { history } from '@umijs/max';
const { getReconciliationListAPI } = service.UserHome;
const { RangePicker } = DatePicker;
const Statement = () => {
  const objRef = useRef<any>();
  const columns: any = [
    {
      title: '查询',
      dataIndex: 'keyword',
      hideInTable: true,
      fieldProps: {
        placeholder:'请输入要查询的内容'
      },
    },
    {
      title: '时间',
      dataIndex: 'time',
      hideInTable: true,
      renderFormItem: () => {
        return (
            <RangePicker showTime/>)
      },
    },
    {
      title: '订单号',
      width: 210,
      dataIndex: 'id',
      fixed: 'left',
      hideInSearch: true,
    },
    // {
    //   title: '客户简称',
    //   width: 150,
    //   dataIndex: 'tenantName',
    // },
    // {
    //   title: '客户ID',
    //   width:170,
    //   dataIndex: 'tenantId',
    // },
    {
      title: 'UPS运单号',
      width:180,
      dataIndex: 'expressCode',
      hideInSearch: true,
    },
    {
      title: '包裹',
      width:120,
      dataIndex: 'piecesNumber',
      hideInSearch: true,
    },
    {
      title: '实销金额 ($)',
      width:120,
      dataIndex: 'payment',
      hideInSearch: true,
    },
    // {
    //   title: '成本',
    //   width:120,
    //   dataIndex: 'cost',
    // },
    // {
    //   title: '利润',
    //   width:120,
    //   dataIndex: 'profit',
    // },
    {
      title: '对账日期',
      dataIndex: 'latestBillTime',
      hideInSearch: true,
      width: 180,
      // sorter: true,
      renderFormItem: () => (
        <RangePicker
          showTime={{
            format: 'HH:mm',
          }}
          format="YYYY-MM-DD HH:mm"
        />
      ),
      render: (text: any) => {
        return <span>{formatTime(text)}</span>;
      },
    },
    {
      title: '产品/渠道',
      width: 150,
      dataIndex: 'channelName',
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'option',
      width: 120,
      fixed: 'right',
      valueType: 'option',
      render: (text:any,record:any) => [
        <Button key="key" type="link" onClick={()=>{
          history.push(`/finance/statement/Details`,record)
        }}>
          详情
        </Button>,
      ],
    },
  ];
  const actionRef = useRef<any>();
  /* 刷新表格 */
  // const refresh = () => {
  //   actionRef.current?.reload();
  // };

  return (
    <>
    <ProTable<any>
      columns={columns}
      actionRef={actionRef}
      formRef={objRef}
      // cardBordered
      headerTitle="对账管理"
      request={async (params: any) => {
        const msg = await getReconciliationListAPI({
          start: (params.current - 1) * params.pageSize,
          len: params.pageSize,
          keyword: params.keyword,
          startTime:params.time && convertToTimestamp(params.time[0]),
          endTime:params.time && convertToTimestamp(params.time[1]),
        });
        return {
          data: msg?.data?.list || [],
          success: msg.status,
          total: msg.data.amount,
        };
      }}
      rowKey="id"
      search={{
        labelWidth: 'auto',
      }}
      options={{
        fullScreen: true,
      }}
      scroll={{ x: 1200 }}
      pagination={{
        pageSize: 10,
      }}
      // toolBarRender={() => [
      //   <UploadBill key="key" refresh={refresh} />,
      // ]}
    />
    </>
  );
};
export default Statement;
