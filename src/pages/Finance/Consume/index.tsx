import ProTable from '@ant-design/pro-table';
import service from '@/services/home';
const {getConsumptionDetailAPI} = service.UserHome;

const ConsumptionTable = () => {

  // 定义表格列配置
  const columns:any = [
    {
      title: '查询',
      dataIndex: 'keyword',
      hideInTable: true,
      fieldProps: {
        placeholder:'请输入要查询的内容'
      },
    },
    {
      title: '时间',
      dataIndex: 'time',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '编号',
      dataIndex: 'id',
      hideInSearch: true,
    },
    {
      title: '变动金额 ($)',
      dataIndex: 'amount',
      hideInSearch: true,
      render:(text:any)=>{
        return -Number(text)

      }
    },
    {
      title: '变动事项',
      dataIndex: 'reason',
      hideInSearch: true,
    },
    {
      title: '余额 ($)',
      dataIndex: 'totalBalance',
      hideInSearch: true,
    },
    {
      title: '关联编号-1',
      dataIndex: 'extraId_1',
      hideInSearch: true,
    },
    {
      title: '关联编号-2',
      dataIndex: 'extraId_2',
      hideInSearch: true,
    },
  ];

  return (
    <>
    <ProTable<any>
    columns={columns}
    // actionRef={actionRef}
    // cardBordered
    request={async (params:any = {}) => {
      const msg = await getConsumptionDetailAPI({
        start: (params.current - 1)*params.pageSize,
        len: params.pageSize,
        keyword: params.keyword,
      });
      return {
        data: msg?.data?.list || [],
        success: msg.status,
        total: msg.data.amount,
      };
    }}
    rowKey="id"
    search={{
      labelWidth: 'auto',
    }}
    options={{
      fullScreen: true,
    }}
    pagination={{
      pageSize: 10,
      // onChange: (page) => console.log(page),
    }}
    
  />
  </>
  );
};

export default ConsumptionTable;
