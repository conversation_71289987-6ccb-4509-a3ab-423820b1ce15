.myCardWarp :global(.ant-pro-card-body) {
  padding: 0;
}
.myCardWarp :global(.ant-table-thead >tr>th ) {
  background: #fff;
}
.myCardWarp :global( :where(.css-dev-only-do-not-override-k83k30).ant-table-wrapper .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before) {
  background: none;
}
.myCardWarp :global(:where(.css-k83k30).ant-table-wrapper .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before) {
  background: none !important;
}
.myCardWarp :global(:where(.css-dev-only-do-not-override-k83k30).ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr:last-child > td) {
  border: none !important;
}
.myCardWarp :global(:where(.css-dev-only-do-not-override-k83k30).ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr > td) {
  border: none !important;
}
.myCardWarp :global(:where(.css-k83k30).ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr > td) {
  border: none;
}
.myCardWarp :global(:where(.css-k83k30).ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr:last-child > td) {
  border: none;
}
.myCardWarp :global( :where(.css-dev-only-do-not-override-k83k30).ant-progress.ant-progress-status-success .ant-progress-bg) {
  background: #face8e;
}
.myCardWarp :global(:where(.css-dev-only-do-not-override-k83k30).ant-progress .ant-progress-bg) {
  background: #97aaf8;
}
.myCardWarp :global(:where(.css-k83k30).ant-progress.ant-progress-status-success .ant-progress-bg) {
  background: #face8e;
}
.myCardWarp :global(:where(.css-k83k30).ant-progress .ant-progress-bg) {
  background: #97aaf8;
}
.myCardWarp :global(.ant-progress-inner) {
  height: 50px ;
  border-radius: 6px;
}
.myCardWarp :global(.ant-progress-bg) {
  height: 50px !important;
  border-radius: 6px !important;
}
.myCardWarp .tableText {
  color: '#333';
  font-weight: 600;
}
