import { ProTable } from '@ant-design/pro-components';
import styles from '../style/index.less';
import { Avatar, Space, Tooltip } from 'antd';
import { UserOutlined } from '@ant-design/icons';
const modifyUrl = (url: any) => {
  if (url.startsWith('https')) {
    return url;
  } else {
    return 'https://static.kmfba.com/' + url;
  }
};
const OperationRecord = ({ logList }: any) => {
  
  const columns: any = [
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          名称
        </div>,
      ],
      dataIndex: 'operatorName',
      render: (text: any, record: any) => {
        return (
          <Space wrap size={16}>
            <div>
              <Avatar
                src={modifyUrl(record.operatorAvatar)}
                size={45}
                icon={<UserOutlined />}
              />
            </div>
            <div className={styles.tableText}>
              <div>{text}</div>
              <Tooltip title={record.content}>
                <div
                  style={{
                    fontSize: 12,
                    color: '#333',
                    fontWeight: 400,
                    maxWidth: 200,
                    textOverflow: 'ellipsis',
                    overflow: 'hidden',
                    whiteSpace: "nowrap"
                  }}
                >
                  {record.content}
                </div>
              </Tooltip>
            </div>
          </Space>
        );
      },
    },
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          时间
        </div>,
      ],
      width: 200,
      dataIndex: 'time',
      valueType: 'dateTime',
      ellipsis: true,
      render: (text: any) => {
        return <div style={{ color: '#707070'}}>{text}</div>;
      },
    },
  ];
  return (
    <div className={styles.myCardWarp}>
      <div
        style={{
          color: '#333',
          fontSize: '20px',
          fontWeight: 600,
          marginTop: 20,
        }}
      >
        操作记录
      </div>
      <ProTable
        // style={{padding:0}}
        dataSource={logList}
        rowKey="id"
        pagination={false}
        columns={columns}
        search={false}
        options={false}
        dateFormatter="string"
        // scroll={{ x: 800 }}
      />
    </div>
  );
};
export default OperationRecord;
