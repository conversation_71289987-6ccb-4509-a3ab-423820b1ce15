import styles from './index.less';
import { Col, Row } from 'antd';
import classnames from 'classnames';
import { useEffect, useState } from 'react';
const AmountDisplay = ({dataList}: any) => {
  // const [activeIndex, setActiveIndex] = useState(0); // 初始值为 0，表示默认选中第一个选项
  /* 可打金额 */
  const [amountMoney, setAmountMoney] = useState(0);
  useEffect(() => {
    if(Object.keys(dataList).length === 0) return;
    let num = (Number(dataList?.total_balance) + Number(dataList?.credit_amount)) -
    (Number(dataList?.frozen_amount) + Number(dataList?.deposit) + Number(dataList?.pre_payment_amount));
    setAmountMoney(num)
  }, [dataList]);
  return (
    <>
      <div className={styles.cardWarp}>
        <Row gutter={24}>
          <Col span={6} style={{ padding: 2 }}>
            <div className={styles.card1}>
              <div>可打单金额</div>
              {/* 保留两位小数 */}
              <div className={styles.amountMoney}>${(amountMoney)?.toFixed(2)}</div>
            </div>
          </Col>
          <Col span={18} style={{ padding: 2 }}>
            <Row>
              <Col span={8} style={{ padding: 2 }}>
                <div className={styles.card3}>
                <div className={classnames(styles.activeImg5,styles.activeImgBg1)} ></div>
                  <div>余额</div>
                  <div className={styles.amountMoney}>${dataList?.total_balance}</div>
                </div>
              </Col>
              <Col span={8} style={{ padding: 2 }}>
                <div className={styles.card3}>
                <div className={classnames(styles.activeImg5,styles.activeImgBg2)} ></div>
                  <div>信用额度</div>
                  <div className={styles.amountMoney}>${dataList?.credit_amount}</div>
                </div>
              </Col>
              <Col span={8} style={{ padding: 2 }}>
                <div className={styles.card3_bg}>
                {/* <div className={classnames(styles.activeImg5,styles.activeImgBg2)} ></div> */}
                  {/* <div>信用额度</div>
                  <div className={styles.amountMoney}>${dataList?.credit_amount}</div> */}
                </div>
              </Col>
              <Col span={8} style={{ padding: 2 }}>
                <div className={styles.card3}>
                <div className={classnames(styles.activeImg5,styles.activeImgBg3)} ></div>
                  <div>预扣费金额</div>
                  <div className={styles.amountMoney}>${dataList.pre_payment_amount}</div>
                </div>
              </Col>
              <Col span={8} style={{ padding: 2 }}>
                <div className={styles.card3}>
                <div className={classnames(styles.activeImg5,styles.activeImgBg4)} ></div>
                  <div>冻结资金</div>
                  <div className={styles.amountMoney}>${dataList?.frozen_amount}</div>
                </div>
              </Col>
              <Col span={8} style={{ padding: 2 }}>
                <div className={styles.card3}>
                <div className={classnames(styles.activeImg5,styles.activeImgBg5)} ></div>
                  <div>押金</div>
                  <div className={styles.amountMoney}>${dataList.deposit}</div>
                </div>
              </Col>
            </Row>
          </Col>
        </Row>
        <Row></Row>
      </div>
    </>
  );
};

export default AmountDisplay;
