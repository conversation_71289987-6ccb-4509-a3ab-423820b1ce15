.myCardWarp{
  :global(.ant-pro-card-body){
    padding: 0;
  }

  :global(.ant-table-thead >tr>th ){
    background: #fff;
    
  }
  

  :global(
  :where(.css-dev-only-do-not-override-k83k30).ant-table-wrapper .ant-table-thead >tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before){
    background: none;
  }

  :global(:where(.css-dev-only-do-not-override-k83k30).ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody >tr:last-child>td){
    border: none;
  }

  :global(:where(.css-dev-only-do-not-override-k83k30).ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody >tr >td ){
    border: none;
  }

  // :global( :where(.css-dev-only-do-not-override-k83k30).ant-progress.ant-progress-status-success .ant-progress-bg){
  //   background: #face8e;
  // }

  // :global(:where(.css-dev-only-do-not-override-k83k30).ant-progress .ant-progress-bg){
  //   background:#97aaf8 ;
  // }

  // :global(.ant-progress-inner){
  //   height:50px ;
  //   border-radius: 6px;
  // }

  // :global(.ant-progress-bg){
  //   height: 50px !important;
  //   border-radius: 6px !important;
  // }

  .tableText{
    color: '#333';
    font-weight: 600; 
  }
}