import { ProTable } from '@ant-design/pro-components';
import styles from './index.less';
// import { Progress } from 'antd';
const OrderList = ({ billList }: any) => {
  const columns: any = [
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          单号
        </div>,
      ],
      dataIndex: 'waybillId',
      render: (text: any) => {
        return (
          <div>
            <div style={{ color: '#333', fontWeight: 600 }}>{text}</div>
          </div>
        );
      },
    },
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          金额
        </div>,
      ],
      dataIndex: 'amount',
      render: (text: any) => {
        return <div style={{ color: '#333', fontWeight: 600 }}>{text} $</div>
      }
    },
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          包裹
        </div>,
      ],
      dataIndex: 'totalPiecesNumber',
      render: (text: any,record:any) => {
        return <div style={{ color: '#333', fontWeight: 600 }}>
          <div>
            {record.completedPiecesNumber}/{record.totalPiecesNumber}
            {/* <Progress steps={record.completedPiecesNumber} 
            showInfo={false} /> */}
          </div>
        </div>
      }
    },
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          时间
        </div>,
      ],
      dataIndex: 'time',
      valueType: 'dateTime',
      render: (text: any) => {
        return <div style={{ color: '#333', fontWeight: 600 }}>
          {text}
        </div>
      },
    },
  ];

  return (
    <div className={styles.myCardWarp}>
      <div
        style={{
          color: '#333',
          fontSize: '20px',
          fontWeight: 600,
          marginTop: 20,
        }}
      >
        最新出账单号
      </div>
      <ProTable
        // style={{padding:0}}
        dataSource={billList}
        rowKey="waybillId"
        pagination={false}
        columns={columns}
        search={false}
        options={false}
        dateFormatter="string"
      />
    </div>
  );
};
export default OrderList;
