.myCardWarp :global(.ant-pro-card-body) {
  padding: 0;
}
.myCardWarp :global(.ant-table-thead >tr>th ) {
  background: #fff;
}
.myCardWarp :global( :where(.css-dev-only-do-not-override-k83k30).ant-table-wrapper .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before) {
  background: none;
}
.myCardWarp :global(:where(.css-dev-only-do-not-override-k83k30).ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr:last-child > td) {
  border: none;
}
.myCardWarp :global(:where(.css-dev-only-do-not-override-k83k30).ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr > td) {
  border: none;
}
.myCardWarp .tableText {
  color: '#333';
  font-weight: 600;
}
