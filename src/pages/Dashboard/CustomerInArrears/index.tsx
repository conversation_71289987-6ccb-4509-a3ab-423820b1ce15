import { ProTable } from '@ant-design/pro-components';
import styles from '../style/index.less';
import { Avatar, Space } from 'antd';
import { UserOutlined } from '@ant-design/icons';
const CustomerInArrears = ({ insufficientFundClients }: any) => {
  const columns: any = [
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          名称
        </div>,
      ],
      dataIndex: 'fullName',
      render: (text: any,record:any) => {
        return <Space wrap size={16}>
        <div>
          <Avatar
            src={record.logo}
            size={45}
            icon={<UserOutlined />}
          />
        </div>
        <div className={styles.tableText}>
          <div>{text}</div>
          </div>
      </Space>;
      }
    },
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          可打金额
        </div>,
      ],
      dataIndex: 'containers',
      render: (text: any, record: any) => {
        return (
          <div className={styles.tableText}>
            {
              Number(record?.totalBalance) +
              Number(record?.creditAmount) -
              Number(record?.deposit) -
              Number(record?.prePaymentAmount) -
              Number(record?.frozenAmount)
            }
          </div>
        );
      },
    },
  ];
  return (
    <div className={styles.myCardWarp}>
      <div
        style={{
          color: '#333',
          fontSize: '20px',
          fontWeight: 600,
          marginTop: 20,
        }}
      >
        临期欠费客户
      </div>
      <ProTable
        // style={{padding:0}}
        dataSource={insufficientFundClients}
        rowKey="id"
        pagination={false}
        columns={columns}
        search={false}
        options={false}
        dateFormatter="string"
      />
    </div>
  );
};
export default CustomerInArrears;
