import { ProTable } from '@ant-design/pro-components';
import styles from '../style/index.less';
const LatestRechargeCustomers = ({rechargeRecords}:any) => {
  const columns:any = [
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          名称  
        </div>,
      ],
      dataIndex: 'tenantName',
      render:(text:any)=>{
        return <div className={styles.tableText}>{text}</div>
      }
    },
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          入账
        </div>,
      ],
      dataIndex: 'amount',
      render:(text:any)=>{
        return <div className={styles.tableText}>{text}</div>
      }
    },
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          时间
        </div>,
      ],
      dataIndex: 'time',
      valueType: 'dateTime',
      render:(text:any)=>{
        return <div className={styles.tableText}>{text}</div>
      }
    },
  ];
  return (
    <div className={styles.myCardWarp}>
      <div style={{color:'#333',fontSize:'20px',fontWeight:600,marginTop:20}}>最新充值客户</div>
      <ProTable
        // style={{padding:0}}
        dataSource={rechargeRecords}
        rowKey="id"
        pagination={false}
        columns={columns}
        search={false}
        options={false}
        dateFormatter="string"
      />
    </div>
  );
};
export default LatestRechargeCustomers;
