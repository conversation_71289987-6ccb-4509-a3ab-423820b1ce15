.warp1 {
  display: flex;
}
.activeItem {
  color: #fff;
  padding: 0 10px;
  background: #2967F5;
  border-radius: 12px;
  opacity: 0.5;
}
.activeText {
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  padding: 0 10px;
  color: #707070;
  line-height: 20px;
}
.cardWarp {
  margin-top: 20px;
}
.cardWarp .card1 {
  min-width: 134px;
  min-height: 114px;
  border-radius: 4px;
  background-image: url('../images/bulemode.png');
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: 100% 100%;
  color: rgba(255, 255, 255, 0.7);
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.cardWarp .card1 .amountMoney {
  font-size: 20px;
  color: #FFF;
}
.cardWarp .card2 {
  min-width: 134px;
  min-height: 114px;
  border-radius: 4px;
  background-image: url('../images/redmode.png');
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: 100% 100%;
  color: rgba(255, 255, 255, 0.7);
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.cardWarp .card2 .amountMoney {
  font-size: 20px;
  color: #FFF;
}
.cardWarp .card3 {
  height: 100%;
  background: #F9F9F9;
  border-radius: 4px;
  color: rgba(51, 51, 51, 0.7);
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.cardWarp .card3 .amountMoney {
  font-size: 20px;
  color: #333;
}
