// import { useEffect } from 'react';

import styles from './index.less';
// import classnames from 'classnames';
import { Chart, LineAdvance} from 'bizcharts';
import { Space } from 'antd';
import { useEffect, useState } from 'react';


export default ({wayBillIcon}:any) => {
  const [activeIndex, setActiveIndex] = useState(0); // 初始值为 0，表示默认选中第一个选项
  const [data, setData] = useState<any>([]);
  useEffect(() => {
    if(Object.keys(wayBillIcon).length === 0) return;
    if(activeIndex ===0){
      setData([...wayBillIcon?.oneDayWayBillIcon])
    }
    if(activeIndex ===1){
      setData([...wayBillIcon?.sevenDayWayBillIcon])
    }
    if(activeIndex ===2){
      setData([...wayBillIcon?.thirtyDayWayBillIcon])
    }
  }, [wayBillIcon,activeIndex]);
  return (
    <>
      <div style={{color:'#333',fontSize:'20px',fontWeight:600}}>订单趋势图</div>
      <div className={styles.warp1} style={{marginTop:'12px'}}>
        <Space size={[20, 20]}>
          <div
            className={
              activeIndex === 0 ? styles.activeItem : styles.activeText
            }
            onClick={() => setActiveIndex(0)}
          >
            1天
          </div>
          <div
            className={
              activeIndex === 1 ? styles.activeItem : styles.activeText
            }
            onClick={() => setActiveIndex(1)}
          >
            近7天
          </div>
          <div
            className={
              activeIndex === 2 ? styles.activeItem : styles.activeText
            }
            onClick={() => setActiveIndex(2)}
          >
            近30天
          </div>
        </Space>
      </div>
      <div style={{display: 'flex', marginTop: 12 }}>
        <div style={{width: '100%'}}>
          <Chart  autoFit  height={280} data={data} >
            <LineAdvance
              shape="smooth"
              point
              area
              position="days*amount"
            />
          </Chart>
        </div>
      </div>
    </>
  )
}