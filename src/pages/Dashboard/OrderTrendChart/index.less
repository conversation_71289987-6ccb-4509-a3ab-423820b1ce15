// :root {
//   --tc: #707070;
//   --tci: #fff;

//   --tcsbg: #2967F5;
// }

// .salesExtraWrap {
//   display: flex;

//   .salesItem {
//     margin-right: 29px;;
//     padding: 2px 8px;
//     border-radius: 12px;
//     font-size: 14px;

//     cursor: pointer;
//   }
//   .currentDate {
//     background: var(--tcsbg);
//     color: var(--tci);
//   }
// }

.warp1{
  display: flex;
}

.activeItem {
  color:#fff;
  padding: 0 10px;
  background: #2967F5;
  border-radius: 12px;
  opacity: 0.5;
}

.activeText{
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  padding: 0 10px;
  color: #707070;
  line-height: 20px;
}

.cardWarp{
  // display: flex;
  margin-top: 20px;

  .card1{
    min-width: 134px;
    min-height: 114px;
    border-radius: 4px;
    //背景图片
    background-image: url('../images/bulemode.png');
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    color: rgba(#fff, .7);
    padding: 10px;
    // margin-right: 5px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .amountMoney{
      font-size: 20px;
      color: #FFF;
    }
  }
  
  .card2{
    min-width: 134px;
    min-height: 114px;
    border-radius: 4px;
    //背景图片
    background-image: url('../images/redmode.png');
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
    color: rgba(#fff, .7);
    padding: 10px;
    // margin-right: 5px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .amountMoney{
      font-size: 20px;
      color: #FFF;
    }
  }
  
  .card3{
    height: 100%;
    background: #F9F9F9;
    border-radius: 4px;
    color: rgba(#333, .7);
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .amountMoney{
      font-size: 20px;
      color: #333;
    }
  }
}

