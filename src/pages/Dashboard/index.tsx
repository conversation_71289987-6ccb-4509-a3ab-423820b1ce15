import { ProCard } from '@ant-design/pro-components';
import TitleSection from './TitleSection';
import AmountDisplay from './AmountDisplay';
// import LatestRechargeCustomers from './LatestRechargeCustomers';
// import CustomerInArrears from './CustomerInArrears';
import OperationRecord from './OperationRecord';
import OrderTrendChart from  './OrderTrendChart'
import OrderList from './OrderList'
import service from '@/services/home';
import { useEffect, useState } from 'react';
const { getHomeDataAPI } = service.UserHome;
const Home2 = () => {
  /* 获取首页数据 */
  const [data, setData] = useState<any>({});
  const getHomeData = async () => {
    try {
      const { status, data } = await getHomeDataAPI({});
      console.log('data: ', data);
      if (status) {
        setData(data);
      }
    } catch (e) {
      console.log(e);
    }
  };
  useEffect(() => {
    getHomeData();
  }, []);
  return (
    <>
      <ProCard ghost gutter={[12, 8]}>
        <ProCard colSpan={12} bordered>
          {/* 标题 */}
          <TitleSection dataList={data.tenant} instance={data.instance} />
          {/* 近1天 7天等金额展示 */}
          <AmountDisplay dataList={{...data.tenant,...data.instance}} />
          {/* 最新充值客户 */}
          {/* <LatestRechargeCustomers rechargeRecords={data.rechargeRecords} /> */}
          {/* 临时欠费客户 */}
          {/* <CustomerInArrears insufficientFundClients={data.insufficientFundClients} /> */}
          {/* 操作记录 */}
          <OperationRecord  logList={data.logList} />
        </ProCard>
        <ProCard colSpan={12} bordered>
          {/* 订单趋势图 */}
          <OrderTrendChart wayBillIcon={data} />
          {/* 打单榜单 */}
          {/* 最新账单号 */}
          <OrderList billList={data.newestReconciliation} />
          
        </ProCard>
      </ProCard>
    </>
  );
};
export default Home2;
