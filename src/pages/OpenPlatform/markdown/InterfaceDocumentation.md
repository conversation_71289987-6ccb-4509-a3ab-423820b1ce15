# 国际快递打单系统

> **说明：以下所有请求入参必须包含appId，appSecret**



## 一、立即试算

### 1. Reuqest

> - Method: **POST**
> - URL: ```https://server-waybill.kmfba.com/waybill/tryCalc```
> - Headers： content-type:application/json

### 2. 请求参数

| 参数名    | 是否必填 | 说明                     |      |
| --------- | -------- | ------------------------ | ---- |
| appId     | 是       | 应用ID                   |      |
| appSecret | 是       | 应用密钥                 |      |
| request   | 是       | WaybillRequest对象引用名 |      |

#### WaybillRequest对象说明

| 参数名           | 是否必填 | 类型                       | 说明                                          |
| ---------------- | -------- | -------------------------- | --------------------------------------------- |
| outerOrderId     | 是       | String                     | 外部订单号                                    |
| signature        | 是       | Integer                    | 签收证明：0-不需要；1-需要                    |
| wightUnit        | 是       | String                     | 包裹中重量单位，取值范围：kg，lb              |
| lengthUnit       | 是       | String                     | 包裹中长、宽、高长度单位，取值范围：cm，in    |
| density          | 否       | BigDecimal                 | 密度，取值范围【50,55,60,65,70,77.5,85,92.5】 |
| remark           | 否       | String                     | 备注                                          |
| pieces           | 是       | List<WaybillPiecesRequest> | 包裹信息                                      |
| channelIds       | 否       | String                     | 渠道id，如果多个请用','分割                   |
| senderAddress    | 否       | Address                    | 发件人地址                                    |
| recipientAddress | 是       | Address                    | 收件人地址                                    |

#### WaybillPiecesRequest对象说明

| 参数名   | 是否必填 | 类型       | 说明     |
| -------- | -------- | ---------- | -------- |
| number   | 是       | Integer    | 包裹数量 |
| weight   | 是       | BigDecimal | 重量     |
| width    | 是       | BigDecimal | 宽度     |
| height   | 是       | BigDecimal | 高度     |
| length   | 是       | BigDecimal | 长度     |
| feature1 | 是       | String     | 参考号-1 |
| feature2 | 是       | String     | 参考号-2 |

#### Address对象说明

| 参数名       | 是否必填 | 类型   | 说明                       |
| ------------ | -------- | ------ | -------------------------- |
| country      | 是       | String | 国家(US) ,目前只支持美国US |
| province     | 是       | String | 州                         |
| city         | 是       | String | 城市                       |
| street       | 是       | String | 街道                       |
| zipCode      | 是       | String | 邮编                       |
| companyName  | 是       | String | 公司                       |
| contactName  | 是       | String | 联系人                     |
| contactPhone | 是       | String | 联系人电话                 |
| residential  | 否       | String | 地址：1-民用 , 0-商业      |

### 3. 返回参数

| 参数名 | 类型                   | 说明                                               |      |
| ------ | ---------------------- | -------------------------------------------------- | ---- |
| status | Integer                | 返回结果：正常返回-true，异常-false，响应状态：200 |      |
| data   | WaybillTestPriceResult | 试算响应结果，该结果要在预报上传                   |      |

#### WaybillTestPriceResult对象说明

| 参数名       | 类型       | 说明                 |      |
| ------------ | ---------- | -------------------- | ---- |
| chargeWeight | BigDecimal | 计费重               |      |
| withholdFee  | BigDecimal | 当前试算价格         |      |
| channelId    | String     | 渠道ID               |      |
| channelName  | String     | 渠道名称             |      |
| waybillId    | String     | 运单ID               |      |
| extra        | String     | 该字段是预报的返回值 |      |

#### 异常类

| 异常类                      | 状态 | 异常信息       |      |
| --------------------------- | ---- | -------------- | ---- |
| UnSupportedServiceException | 200  | 服务不支持     |      |
| NoAvailableChannelException | 200  | 没有可用的渠道 |      |
| TestPriceCheckException     | 200  | 试算异常信息   |      |
| UnsupportedUnitException    | 200  | 单位暂不支持   |      |

### 4. 示例

#### 请求对象

```xml
{
  "outerOrderId": "22222",
  "signature": "1",
  "wightUnit": "lb",
  "lengthUnit": "in",
	"density": 75
	"remark": "",
  "pieces": [
    {
      "number": "3",
      "weight": "3",
      "width": "2",
      "height": "2",
      "length": "2",
			"feature1": "2",
      "feature2": "1"
    }
  ],
  "senderAddress": {
    "country": "US",
    "province": "Texas",
    "city": "RIVIERA",
    "street": "1253 BARBOUR RD",
		"zipCode": "78379"
    "companyName": "TX/Texas - RIVIERA",
    "contactName": "MaLiYa",
    "contactPhone": "15943224455",
    "residential": 1 
	},
	"recipientAddress": {
		"country": "US",
    "province": "New Mexico",
    "city": "CROWNPOINT",
    "street": "291 ACOMA ST",
		"zipCode": "87313"
    "companyName": "William",
    "contactName": "William",
    "contactPhone": "15933322221",
    "residential": 1 
	}
}
```

#### 返回对象

```
{
	"data":{
		"channelId":"ch186c587a501854",
		"channelName":"美西UPS GROUND-YC",
		"chargeWeight":"147.27",
		"extra":"YCW",
		"waybillId":"wb18721b6d75d009",
		"withholdFee":"174.38"
	},
	"status":true
}
```

## 二、立即预报

### 1. Reuqest

> - Method: **POST**
> - URL: ```https://server-waybill.kmfba.com/waybill/create```
> - Headers： content-type:application/json

### 2. 请求参数

| 参数名    | 是否必填 | 说明                     |
| --------- | -------- | ------------------------ |
| appId     | 是       | 应用ID                   |
| appSecret | 是       | 应用密钥                 |
| request   | 是       | WaybillRequest对象引用名 |

#### WaybillRequest对象说明

| 参数名           | 是否必填 | 类型                       | 说明                                          |
| ---------------- | -------- | -------------------------- | --------------------------------------------- |
| outerOrderId     | 是       | String                     | 外部订单号                                    |
| signature        | 是       | Integer                    | 签收证明：0-不需要；1-需要                    |
| wightUnit        | 是       | String                     | 包裹中重量单位，取值范围：kg，lb              |
| lengthUnit       | 是       | String                     | 包裹中长、宽、高长度单位，取值范围：cm，in    |
| density          | 否       | BigDecimal                 | 密度，取值范围【50,55,60,65,70,77.5,85,92.5】 |
| remark           | 否       | String                     | 备注                                          |
| waybillId        | 否       | String                     | 试算返回结果中的我方运单id                    |
| pieces           | 是       | List<WaybillPiecesRequest> | 包裹信息                                      |
| channelIds       | 否       | String                     | 渠道id，如果多个请用','分割                   |
| senderAddress    | 否       | Address                    | 发件人地址                                    |
| recipientAddress | 是       | Address                    | 收件人地址                                    |

#### WaybillPiecesRequest对象说明

| 参数名   | 是否必填 | 类型       | 说明     |
| -------- | -------- | ---------- | -------- |
| number   | 是       | Integer    | 包裹数量 |
| weight   | 是       | BigDecimal | 重量     |
| width    | 是       | BigDecimal | 宽度     |
| height   | 是       | BigDecimal | 高度     |
| length   | 是       | BigDecimal | 长度     |
| feature1 | 是       | String     | 参考号-1 |
| feature2 | 是       | String     | 参考号-2 |

#### Address对象说明

| 参数名       | 是否必填 | 类型   | 说明                       |
| ------------ | -------- | ------ | -------------------------- |
| country      | 是       | String | 国家(US) ,目前只支持美国US |
| province     | 是       | String | 州                         |
| city         | 是       | String | 城市                       |
| street       | 是       | String | 街道                       |
| zipCode      | 是       | String | 邮编                       |
| companyName  | 是       | String | 公司                       |
| contactName  | 是       | String | 联系人                     |
| contactPhone | 是       | String | 联系人电话                 |
| residential  | 否       | String | 地址：1-民用 , 0-商业      |

### 3. 返回参数

| 参数名 | 类型    | 说明                                               |
| ------ | ------- | -------------------------------------------------- |
| status | Boolean | 返回结果：正常返回-true，异常-false，响应状态：200 |

#### 异常类

| 异常类                        | 状态 | 异常信息                             |
| ----------------------------- | ---- | ------------------------------------ |
| UnsupportedOperationException | 200  | 该实例不支持预报                     |
| InsufficientFundException     | 200  | 剩余可预报金额不足                   |
| ChannelNotExistsException     | 200  | {channalId} + 该渠道为空，不支持预报 |
| FailedException               | 200  | 该渠道不可用                         |
| UnsupportedUnitException      | 200  | 单位暂不支持                         |

### 4. 示例

#### 请求对象

```xml
{
  "outerOrderId": "22222",
  "signature": "1",
  "wightUnit": "lb",
  "lengthUnit": "in",
	"density": 75
	"remark": "",
  "waybillId": "wb18721b6d75d009",
  "withholdFee": "174.38",
  "pieces": [
    {
      "number": "3",
      "weight": "3",
      "width": "2",
      "height": "2",
      "length": "2",
			"feature1": "2",
      "feature2": "1"
    }
  ],
  "senderAddress": {
    "country": "US",
    "province": "Texas",
    "city": "RIVIERA",
    "street": "1253 BARBOUR RD",
		"zipCode": "78379"
    "companyName": "TX/Texas - RIVIERA",
    "contactName": "MaLiYa",
    "contactPhone": "15943224455",
    "residential": 1 
	},
	"recipientAddress": {
		"country": "US",
    "province": "New Mexico",
    "city": "CROWNPOINT",
    "street": "291 ACOMA ST",
		"zipCode": "87313"
    "companyName": "William",
    "contactName": "William",
    "contactPhone": "15933322221",
    "residential": 1 
	}
}
```

#### 返回对象

```
{
	"status":true
}
```

## 三、获取面单信息

### 1. Reuqest

> - Method: **GET**
> - URL: ```https://server-waybill.kmfba.com/waybill/getLabel```
> - Headers： content-type:application/json;charset=UTF-8

### 2. 请求参数

| 参数名    | 是否必填 | 说明              |
| --------- | -------- | ----------------- |
| appId     | 是       | 应用ID            |
| appSecret | 是       | 应用密钥          |
| id        | 是       | 我方运单waybillId |

### 3. 返回参数

| 参数名 | 类型       | 说明                                               |
| ------ | ---------- | -------------------------------------------------- |
| status | Boolean    | 返回结果：正常返回-true，异常-false，响应状态：200 |
| data   | JsonObject | 返回的对象                                         |

#### data对象

| 参数名 | 类型   | 说明     |
| ------ | ------ | -------- |
| ossUrl | String | 面单地址 |

#### 异常类

| 异常类                  | 状态 | 异常信息       |      |
| ----------------------- | ---- | -------------- | ---- |
| InvalidServiceException | 200  | 无效的服务     |      |
| EmptyObjectException    | 200  | 运单不存在     |      |
| OssReceiptUrlException  | 200  | 面单地址未返回 |      |

### 4. 示例

#### 请求对象

```xml
"id":"wb18721b6d75d009"
```

#### 返回对象

```
{
	"data":{
		"ossUrl":"http://yangcheng-backend.oss-cn-hangzhou.aliyuncs.com/xxxx"
	},
	"status":true
}
```

## 四、取消运单

### 1. Reuqest

> - Method: **GET**
> - URL: ```https://server-waybill.kmfba.com/waybill/cancel```
> - Headers： content-type:application/json;charset=UTF-8

### 2. 请求参数

| 参数名    | 是否必填 | 说明              |
| --------- | -------- | ----------------- |
| appId     | 是       | 应用ID            |
| appSecret | 是       | 应用密钥          |
| id        | 是       | 我方运单waybillId |

### 3. 返回参数

| 参数名 | 类型    | 说明                                               |
| ------ | ------- | -------------------------------------------------- |
| status | Boolean | 返回结果：正常返回-true，异常-false，响应状态：200 |

#### 异常类

| 异常类                    | 状态 | 异常信息                           |      |
| ------------------------- | ---- | ---------------------------------- | ---- |
| UncheckedException        | 200  | 已对账，不允许取消                 |      |
| ChannelNotExistsException | 200  | 渠道不存在                         |      |
| FailedException           | 200  | 没有找到上游订单编号\|上游退款失败 |      |

### 4. 示例

#### 请求对象

```xml
"id":"wb18721b6d75d009"
```

#### 返回对象

```
{
	"status":true
}
```

## 五、运单列表

### 1. Reuqest

> - Method: **GET**
> - URL: ```https://server-waybill.kmfba.com/waybill/list```
> - Headers： content-type:application/json;charset=UTF-8

### 2. 请求参数

| 参数名    | 是否必填 | 说明                                                   |
| --------- | -------- | ------------------------------------------------------ |
| appId     | 是       | 应用ID                                                 |
| appSecret | 是       | 应用密钥                                               |
| keyword   | 否       | 搜索关键字                                             |
| start     | 否       | 分页起始(注意不是从0，1，2开始)，比如0,10,...，默认是0 |
| len       | 否       | 分页偏移量，默认是10                                   |

### 3. 返回参数

| 参数名 | 类型       | 说明                                               |      |
| ------ | ---------- | -------------------------------------------------- | ---- |
| status | Integer    | 返回结果：正常返回-true，异常-false，响应状态：200 |      |
| data   | JSONObject | 响应结果                                           |      |

#### JSONObject对象说明

| 参数名 | 类型                  | 说明     |      |
| ------ | --------------------- | -------- | ---- |
| amount | Integer               | 分页总数 |      |
| list   | List<WaybillResponse> | 分页列表 |      |

#### WaybillResponse对象说明

| 参数名             | 类型       | 说明                                                         |      |
| ------------------ | ---------- | ------------------------------------------------------------ | ---- |
| id                 | String     | 运单ID                                                       |      |
| channelName        | String     | 产品名称                                                     |      |
| status             | Integer    | 运单状态：1-预报中,0-已预报,2-预报失败,3-部分出账,4-全部出账 |      |
| outerOrderId       | String     | 渠道名称                                                     |      |
| piecesNumber       | Integer    | 运单ID                                                       |      |
| insertTime         | Timestamp  | 该字段是预报的返回值                                         |      |
| expressCode        | String     | ups运单号                                                    |      |
| totalWeight        | BigDecimal | 总重量                                                       |      |
| chargeWeight       | BigDecimal | 计费重量(试算生成)                                           |      |
| withholdFee        | BigDecimal | 预扣费                                                       |      |
| releaseWithholdFee | BigDecimal | 已释放预扣费                                                 |      |
| remark             | String     | 备注                                                         |      |
| printTimes         | Integer    | 打单次数                                                     |      |
| payment            | BigDecimal | 实扣金额                                                     |      |
| latestBillTime     | Timestamp  | 最新对账时间                                                 |      |
| completedPieces    | Integer    | 已完成包裹数量                                               |      |
| labelUrl           | String     | 面单非完整地址，如果获取面单，请调用获取面单接口             |      |

#### 异常类

| 异常类                  | 状态 | 异常信息   |      |
| ----------------------- | ---- | ---------- | ---- |
| EmptyObjectException    | 200  | 运单不存在 |      |
| InvalidServiceException | 200  | 无效的服务 |      |

### 4. 示例

#### 请求对象

```xml
"keyword":"快递",
"start": 0,
"len": 10
```

#### 返回对象

```
{
  "data": {
    "amount": 21,
    "list": [
      {
        "channelName": "",
        "chargeWeight": "0.00",
        "completedPieces": 0,
        "expressCode": "",
        "failedReason": "",
        "feature1": "",
        "feature2": "",
        "id": "wb1876e6e8ffc064",
        "insertTime": 1681184952000,
        "labelUrl": "",
        "outerOrderId": "T20221031001",
        "piecesNumber": 1,
        "printTimes": 0,
        "recipientAddressSnapshot": "",
        "releaseWithholdFee": "0.00",
        "remark": "",
        "senderAddressSnapshot": "",
        "status": 2,
        "tenantId": "",
        "tenantName": "",
        "totalWeight": "1.37",
        "withholdFee": "0.00"
      },
      {
        "channelName": "美西UPS GROUND-YC",
        "chargeWeight": "44.45",
        "completedPieces": 0,
        "expressCode": "1Z4192XR0308820022",
        "failedReason": "",
        "feature1": "",
        "feature2": "",
        "id": "wb1876e6cfc9b487",
        "insertTime": 1681184856000,
        "labelUrl": "ins186ce5a2607773/2023/04/11/90d1c09226f94e599eac64c9bf2b7d4d.pdf",
        "outerOrderId": "3332",
        "piecesNumber": 2,
        "printTimes": 0,
        "recipientAddressSnapshot": "",
        "releaseWithholdFee": "0.00",
        "remark": "",
        "senderAddressSnapshot": "",
        "status": 0,
        "tenantId": "",
        "tenantName": "",
        "totalWeight": "44.00",
        "withholdFee": "61.86"
      },
      {
        "channelName": "",
        "chargeWeight": "0.00",
        "completedPieces": 0,
        "expressCode": "",
        "failedReason": "",
        "feature1": "",
        "feature2": "",
        "id": "wb1876e63e59e429",
        "insertTime": 1681184254000,
        "labelUrl": "",
        "outerOrderId": "T20221031001",
        "piecesNumber": 1,
        "printTimes": 0,
        "recipientAddressSnapshot": "",
        "releaseWithholdFee": "0.00",
        "remark": "",
        "senderAddressSnapshot": "",
        "status": 2,
        "tenantId": "",
        "tenantName": "",
        "totalWeight": "1.37",
        "withholdFee": "0.00"
      },
      {
        "channelName": "",
        "chargeWeight": "0.00",
        "completedPieces": 0,
        "expressCode": "",
        "failedReason": "",
        "feature1": "",
        "feature2": "",
        "id": "wb1876e63e5ad997",
        "insertTime": 1681184254000,
        "labelUrl": "",
        "outerOrderId": "T20221031002",
        "piecesNumber": 2,
        "printTimes": 0,
        "recipientAddressSnapshot": "",
        "releaseWithholdFee": "0.00",
        "remark": "",
        "senderAddressSnapshot": "",
        "status": 2,
        "tenantId": "",
        "tenantName": "",
        "totalWeight": "4.54",
        "withholdFee": "0.00"
      },
      {
        "channelName": "",
        "chargeWeight": "0.00",
        "completedPieces": 0,
        "expressCode": "",
        "failedReason": "",
        "feature1": "",
        "feature2": "",
        "id": "wb1876e63e5c7152",
        "insertTime": 1681184254000,
        "labelUrl": "",
        "outerOrderId": "T20221031004",
        "piecesNumber": 5,
        "printTimes": 0,
        "recipientAddressSnapshot": "",
        "releaseWithholdFee": "0.00",
        "remark": "",
        "senderAddressSnapshot": "",
        "status": 2,
        "tenantId": "",
        "tenantName": "",
        "totalWeight": "18.14",
        "withholdFee": "0.00"
      },
      {
        "channelName": "",
        "chargeWeight": "0.00",
        "completedPieces": 0,
        "expressCode": "",
        "failedReason": "",
        "feature1": "",
        "feature2": "",
        "id": "wb1876e5fee09138",
        "insertTime": 1681183994000,
        "labelUrl": "",
        "outerOrderId": "T20221031001",
        "piecesNumber": 1,
        "printTimes": 0,
        "recipientAddressSnapshot": "",
        "releaseWithholdFee": "0.00",
        "remark": "",
        "senderAddressSnapshot": "",
        "status": 2,
        "tenantId": "",
        "tenantName": "",
        "totalWeight": "1.37",
        "withholdFee": "0.00"
      },
      {
        "channelName": "",
        "chargeWeight": "0.00",
        "completedPieces": 0,
        "expressCode": "",
        "failedReason": "",
        "feature1": "",
        "feature2": "",
        "id": "wb1876e5fee0b087",
        "insertTime": 1681183993000,
        "labelUrl": "",
        "outerOrderId": "T20221031002",
        "piecesNumber": 2,
        "printTimes": 0,
        "recipientAddressSnapshot": "",
        "releaseWithholdFee": "0.00",
        "remark": "",
        "senderAddressSnapshot": "",
        "status": 2,
        "tenantId": "",
        "tenantName": "",
        "totalWeight": "4.54",
        "withholdFee": "0.00"
      },
      {
        "channelName": "",
        "chargeWeight": "0.00",
        "completedPieces": 0,
        "expressCode": "",
        "failedReason": "",
        "feature1": "",
        "feature2": "",
        "id": "wb1876e5fee0e838",
        "insertTime": 1681183993000,
        "labelUrl": "",
        "outerOrderId": "T20221031003",
        "piecesNumber": 4,
        "printTimes": 0,
        "recipientAddressSnapshot": "",
        "releaseWithholdFee": "0.00",
        "remark": "",
        "senderAddressSnapshot": "",
        "status": 2,
        "tenantId": "",
        "tenantName": "",
        "totalWeight": "9.07",
        "withholdFee": "0.00"
      },
      {
        "channelName": "",
        "chargeWeight": "0.00",
        "completedPieces": 0,
        "expressCode": "",
        "failedReason": "",
        "feature1": "",
        "feature2": "",
        "id": "wb1876e5fee14975",
        "insertTime": 1681183993000,
        "labelUrl": "",
        "outerOrderId": "T20221031004",
        "piecesNumber": 5,
        "printTimes": 0,
        "recipientAddressSnapshot": "",
        "releaseWithholdFee": "0.00",
        "remark": "",
        "senderAddressSnapshot": "",
        "status": 2,
        "tenantId": "",
        "tenantName": "",
        "totalWeight": "18.14",
        "withholdFee": "0.00"
      },
      {
        "channelName": "",
        "chargeWeight": "0.00",
        "completedPieces": 0,
        "expressCode": "",
        "failedReason": "",
        "feature1": "",
        "feature2": "",
        "id": "wb1876e5c88c3418",
        "insertTime": 1681183771000,
        "labelUrl": "",
        "outerOrderId": "T20221031002",
        "piecesNumber": 2,
        "printTimes": 0,
        "recipientAddressSnapshot": "",
        "releaseWithholdFee": "0.00",
        "remark": "",
        "senderAddressSnapshot": "",
        "status": 2,
        "tenantId": "",
        "tenantName": "",
        "totalWeight": "4.54",
        "withholdFee": "0.00"
      }
    ]
  },
  "status": true
}
```

## 六、运单详情

### 1. Reuqest

> - Method: **GET**
> - URL: ```https://server-waybill.kmfba.com/waybill/getDetail```
> - Headers： content-type:application/json;charset=UTF-8

### 2. 请求参数

| 参数名    | 是否必填 | 说明              |
| --------- | -------- | ----------------- |
| appId     | 是       | 应用ID            |
| appSecret | 是       | 应用密钥          |
| id        | 是       | 我方运单waybillId |

### 3. 返回参数

| 参数名 | 类型            | 说明                                               |      |
| ------ | --------------- | -------------------------------------------------- | ---- |
| status | Integer         | 返回结果：正常返回-true，异常-false，响应状态：200 |      |
| data   | WaybillResponse | 响应结果                                           |      |

#### WaybillResponse对象说明

| 参数名             | 类型       | 说明                                                         |      |
| ------------------ | ---------- | ------------------------------------------------------------ | ---- |
| id                 | String     | 运单ID                                                       |      |
| channelName        | String     | 产品名称                                                     |      |
| status             | Integer    | 运单状态：1-预报中,0-已预报,2-预报失败,3-部分出账,4-全部出账 |      |
| outerOrderId       | String     | 关联订单号                                                   |      |
| density            | BigDecimal | 密度                                                         |      |
| piecesNumber       | Integer    | 包裹数量                                                     |      |
| insertTime         | Timestamp  | 插入时间                                                     |      |
| expressCode        | String     | ups运单号                                                    |      |
| totalWeight        | BigDecimal | 总重量                                                       |      |
| chargeWeight       | BigDecimal | 计费重量(试算生成)                                           |      |
| withholdFee        | BigDecimal | 预扣费                                                       |      |
| releaseWithholdFee | BigDecimal | 已释放预扣费                                                 |      |
| remark             | String     | 备注                                                         |      |
| printTimes         | Integer    | 打单次数                                                     |      |
| recipientAddress   | Address    | 收件人信息                                                   |      |
| senderAddress      | Address    | 发件人信息                                                   |      |
| failedReason       | String     | 预报失败原因                                                 |      |
| payment            | BigDecimal | 实扣金额                                                     |      |
| latestBillTime     | Timestamp  | 最新对账时间                                                 |      |
| completedPieces    | Integer    | 已完成包裹数量                                               |      |
| labelUrl           | String     | 面单非完整地址，如果获取面单，请调用获取面单接口             |      |

#### Address对象说明

| 参数名       | 是否必填 | 类型   | 说明                       |
| ------------ | -------- | ------ | -------------------------- |
| country      | 是       | String | 国家(US) ,目前只支持美国US |
| province     | 是       | String | 州                         |
| city         | 是       | String | 城市                       |
| street       | 是       | String | 街道                       |
| zipCode      | 是       | String | 邮编                       |
| companyName  | 是       | String | 公司                       |
| contactName  | 是       | String | 联系人                     |
| contactPhone | 是       | String | 联系人电话                 |
| residential  | 是       | String | 地址：1-民用 , 0-商业      |

#### 异常类

| 异常类                  | 状态 | 异常信息   |      |
| ----------------------- | ---- | ---------- | ---- |
| EmptyObjectException    | 200  | 运单不存在 |      |
| InvalidServiceException | 200  | 无效的服务 |      |

### 4. 示例

#### 请求对象

```xml
"id":"wb18721b6d75d009"
```

#### 返回对象

```
{
  "channelName": "美西UPS GROUND-YC",
  "chargeWeight": "44.45",
  "completedPieces": 0,
  "expressCode": "1Z4192XR0308820022",
  "failedReason": "",
  "id": "wb1876e6cfc9b487",
  "insertTime": 1681184856000,
  "labelUrl": "ins186ce5a2607773/2023/04/11/90d1c09226f94e599eac64c9bf2b7d4d.pdf",
  "outerOrderId": "3332",
  "density": "55.5"
  "payment": "0.00",
  "piecesNumber": 2,
  "printTimes": 0,
  "recipientAddress": {
    "city": "RHINELAND",
    "cityId": "000459d4b00e856933c2f23a570e7f7d",
    "companyName": "ceshi11",
    "contactName": "ceshi",
    "contactPhone": "***********",
    "country": "US",
    "county": "",
    "enabled": 1,
    "extraId": "",
    "fbaCode": "",
    "hostInstanceId": "ins185b32f4c4a242",
    "id": "addr18754875bd8655",
    "instanceId": "ins186ce5a2607773",
    "isFBA": 0,
    "mnemonicCode": "007",
    "province": "Missouri",
    "provinceShortName": "MO",
    "remoteLevel": 2,
    "residential": 1,
    "source": "",
    "street": "3624 ABNER ROAD",
    "tenantId": "ct186ce5a25f4407",
    "updateTime": 1680750370000,
    "zipCode": "65069"
  },
  "releaseWithholdFee": "0.00",
  "remark": "",
  "senderAddress": {
    "city": "CONTINENTAL DIVIDE",
    "cityId": "00073ce083eb48234545737d00fbfdf2",
    "companyName": "Tony",
    "contactName": "Tony",
    "contactPhone": "***********",
    "country": "US",
    "county": "",
    "enabled": 1,
    "extraId": "",
    "fbaCode": "",
    "hostInstanceId": "",
    "id": "00073ce083eb48234545737d00fbfdf2",
    "instanceId": "ins185b32f4c4a242",
    "isFBA": 0,
    "mnemonicCode": "02",
    "province": "New Mexico",
    "provinceShortName": "NM",
    "remoteLevel": 2,
    "residential": 1,
    "source": "",
    "street": "1304 BALD EAGLE RD",
    "tenantId": "ct186caeae2c2951",
    "updateTime": 1679456303000,
    "zipCode": "87312"
  },
  "status": 0,
  "totalWeight": "44.00",
  "withholdFee": "61.86"
}
```

## 七、运单包裹列表

### 1. Reuqest

> - Method: **GET**
> - URL: ```https://server-waybill.kmfba.com/waybill/pieces/list```
> - Headers： content-type:application/json;charset=UTF-8

### 2. 请求参数

| 参数名    | 是否必填 | 说明                                                         |
| --------- | -------- | ------------------------------------------------------------ |
| appId     | 是       | 应用ID                                                       |
| appSecret | 是       | 应用密钥                                                     |
| id        | 是       | 运单id                                                       |
| start     | 否       | 分页起始(注意不是从0，1，2开始)，比如0,10,...，默认是0       |
| len       | 否       | 分页偏移量，默认是10。**备注：如果查询所有子单，建议把len调大到9999** |

### 3. 返回参数

| 参数名 | 类型       | 说明                                               |      |
| ------ | ---------- | -------------------------------------------------- | ---- |
| status | Integer    | 返回结果：正常返回-true，异常-false，响应状态：200 |      |
| data   | JSONObject | 响应结果                                           |      |

#### JSONObject对象说明

| 参数名 | 类型                        | 说明     |      |
| ------ | --------------------------- | -------- | ---- |
| amount | Integer                     | 分页总数 |      |
| list   | List<WaybillPiecesResponse> | 分页列表 |      |

#### WaybillPiecesResponse对象说明

| 参数名     | 类型       | 说明                                                     |      |
| ---------- | ---------- | -------------------------------------------------------- | ---- |
| id         | String     | 子订单号                                                 |      |
| status     | String     | 状态：1-预报中,0-已预报,2-预报失败,3-部分出账,4-全部出账 |      |
| weight     | Integer    | 重量                                                     |      |
| width      | String     | 宽度                                                     |      |
| height     | Integer    | 高度                                                     |      |
| length     | Timestamp  | 长度                                                     |      |
| insertTime | String     | 时间                                                     |      |
| payment    | BigDecimal | 扣费金额                                                 |      |

#### 异常类

| 异常类                  | 状态 | 异常信息   |      |
| ----------------------- | ---- | ---------- | ---- |
| InvalidServiceException | 200  | 无效的服务 |      |

### 4. 示例

#### 请求对象

```xml
"id":"wb1876e6cfc9b487",
"start": 0,
"len": 10
```

#### 返回对象

```
{
  "data": {
    "amount": 2,
    "list": [
      {
        "height": "2.00",
        "id": "wb1876e6cfc9b4870001",
        "insertTime": 1681184856000,
        "length": "2.00",
        "payment": "0.00",
        "status": 0,
        "weight": "22.00",
        "width": "2.00"
      },
      {
        "height": "2.00",
        "id": "wb1876e6cfc9b4870002",
        "insertTime": 1681184856000,
        "length": "2.00",
        "payment": "0.00",
        "status": 0,
        "weight": "22.00",
        "width": "2.00"
      }
    ]
  },
  "status": true
}
```

## 
