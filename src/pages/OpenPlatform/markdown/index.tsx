import React, { useEffect, useState } from 'react';
// import ReactDOM from 'react-dom'
import ReactMarkdown from 'react-markdown';
import md from './InterfaceDocumentation.md';
import remarkGfm from 'remark-gfm'; // 划线、表、任务列表和直接url等的语法扩展
import rehypeRaw from 'rehype-raw'; // 解析标签，支持html语法
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'; // 代码高亮
import 'github-markdown-css/github-markdown-light.css'; // 目前只引入浅色主题
// import 'github-markdown-css'; // 项目加了主题切换就用此行引入
import { ProCard } from '@ant-design/pro-components';
import styles from './index.less';
const OmsViewMarkdown = () => {
  const [mdContent, setMdContent] = useState<any>('');
  useEffect(() => {
    fetch(md)
      .then((res) => res.text())
      .then((text) => setMdContent(text));
  }, []);

  return (
    <div className={styles.markdownBg}>
      <ProCard>
        <ReactMarkdown
          className="markdown-body"
          // eslint-disable-next-line react/no-children-prop
          children={mdContent}
          remarkPlugins={[remarkGfm, { singleTilde: false }]}
          rehypePlugins={[rehypeRaw]} 
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                <SyntaxHighlighter
                  children={String(children).replace(/\n$/, '')}
                  // style={tomorrow}
                  language={match[1]}
                  PreTag="div"
                  {...props}
                />
              ) : (
                <code className={className} {...props}>
                  {children}
                </code>
              );
            },
          }}
        />
      </ProCard>
    </div>
  );
};

export default OmsViewMarkdown;
