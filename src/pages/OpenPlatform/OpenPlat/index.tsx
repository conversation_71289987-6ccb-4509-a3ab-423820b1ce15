import React from 'react';
import styles from './index.less';
import { useEffect, useState, useRef } from 'react';
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Space,
  Switch,
  Typography,
} from 'antd';
import service from '@/services/home';
import SuperTables from '@/components/SuperTables';
import {
  getOpenAdd,
  getOpenEnable,
  getOpenList,
  getOpenRefresh,
} from '@/services/home/<USER>';
import { CopyOutlined, PlusOutlined, RedoOutlined } from '@ant-design/icons';
import { formatTime } from '@/utils/format';
import { REQUESTADDRESS } from '@/globalData';
import LogDashboard from '@/components/LogDashboard';
import { ProTable } from '@ant-design/pro-components';
const { Paragraph } = Typography;
const { TextArea } = Input;
export default () => {
  const actionRef = useRef<any>();
  const [form] = Form.useForm();
  //   const [toList, IsAccessible] = usePermissionFiltering();
  const { getOpenPlatformAPI, setOpenPlatformAPI } = service.UserHome;
  //   const RequestPath = 'https://server-waybill.kmfba.com/';
  const [appid, setAppid] = useState<string>();
  const [appSecret, setAppSecret] = useState<string>();
  //   const [appIdstatus, setAppIdstatus] = useState<string>();
  //   const [block, setBlock] = useState<boolean>(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [secretOpen, setSecretOpen] = useState(false);
  const [appUserId, setAppUserId] = useState('');
  const focusRef = useRef(null);
  //   const columns: any = [
  //     { key: 0, title: '请求路径', contect: RequestPath, btn: '复制' },
  //     { key: 1, title: 'appId', contect: appid, btn: '复制' },
  //   ];

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const columns: any = [
    {
      title: '名字',
      dataIndex: ['apiUser', 'name'],
      width: 100,
      hideInSearch: true,
    },
    {
      title: 'appSecret',
      dataIndex: 'appSecret',
      width: 300,
      hideInSearch: true,
    },
    { title: '备注', dataIndex: 'remark', width: 100, hideInSearch: true },
    {
      title: '添加人',
      dataIndex: ['creator', 'name'],
      width: 120,
      hideInSearch: true,
    },
    {
      title: '添加时间',
      dataIndex: 'refreshTime',
      width: 160,
      hideInSearch: true,
      fieldFormat: (value: any) => {
        return value?.refreshTime ? formatTime(value?.refreshTime) : '';
      },
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      width: 100,
      hideInSearch: true,
      render: (_: any, recode: any) => (
        <Switch
          defaultChecked={recode?.enabled == 1}
          onChange={(e) => {
            forbidden(e, recode?.apiUserId);
          }}
        />
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      fixed: 'right',
      dataIndex: 'operation',
      render: (_: any, recode: any) => {
        return (
          <Space>
            {' '}
            <a
              onClick={() => {
                setAppUserId(recode?.apiUserId);
                setSecretOpen(true);
              }}
            >
              重置
            </a>{' '}
            <LogDashboard
              extraId_1={recode?.apiUserId}
              btnType="link"
              btnText="日志"
            />
          </Space>
        );
      },
    },
  ];
  const getOpenPlatform = async () => {
    const res = await getOpenPlatformAPI({ service_id: 'Waybill' });
    // console.log('res: ', res);
    if (res.status) {
      setAppid(res.data.appId);
      setAppSecret(res.data.appSecret);
      if (!res.data?.appSecret) {
        setAppSecret('未设置');
      }
    }
  };
  const setOpenPlatform = async () => {
    const res = await setOpenPlatformAPI({ service_id: 'Waybill' });
    if (res.status) {
      setAppid(res.data.appId);
      setAppSecret(res.data.appSecret);
      if (!res.data?.appSecret) {
        setAppSecret('未设置');
      }
    }
  };
  const copyContent = (e, content) => {
    e.preventDefault();
    // 复制
    navigator.clipboard.writeText(content);
    message.success('复制成功');
  };

  useEffect(() => {
    // 组件强制更新
    if (focusRef && focusRef.current) {
      focusRef.current.focus();
    }
  }, [focusRef.current]);

  useEffect(() => {
    getOpenPlatform();
  }, []);
  const onFinish = async (value: any) => {
    try {
      const { status } = await getOpenAdd(value);
      if (status) {
        message.success('添加成功');
        setIsModalOpen(false);
        refresh();
      }
    } catch (error) {}
  };
  const forbidden = async (enabled: Boolean, appUserId: string) => {
    try {
      const { status } = await getOpenEnable({
        enabled: enabled ? 1 : 0,
        appUserId,
      });
      if (status) {
        message.success('操作成功');
        refresh();
      }
    } catch (error) {}
  };
  //   重置
  const getRefresh = async () => {
    try {
      const { status } = await getOpenRefresh({
        appUserId,
      });
      if (status) {
        message.success('重置成功');
        setSecretOpen(false);
        refresh();
      }
    } catch (error) {}
  };
  // const refresh = () => actionRef.current?.refresh();
  const refresh = () => actionRef.current?.reload();

  return (
    <div className={styles.platform}>
      <SuperTables
        instanceId={`open_list`}
        columns={columns}
        rightFrozenColCount={1}
        ref={actionRef}
        rowSelection={() => {}}
        request={async (params: any) => {
          const msg = await getOpenList({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
          });
          return {
            data: msg?.data?.list || [],
            success: msg.status,
            total: msg?.data?.total,
          };
        }}
        isWarpTabs={true}
        toolBarRender={() => (
          <Space>
            <div className={styles.allocation}>
              <span>请求路径</span>
              <div>{REQUESTADDRESS}</div>
              <Paragraph
                copyable={{ text: `${REQUESTADDRESS}` }}
                style={{ marginLeft: '-22px', marginTop: '8px', zIndex: 99 }}
              ></Paragraph>
            </div>
            <div className={styles.allocation_appId}>
              <span>appId</span>
              <div>{appid}</div>
              <Paragraph
                copyable={{ text: `${appid}` }}
                style={{ marginLeft: '-22px', marginTop: '8px', zIndex: 99 }}
              ></Paragraph>
            </div>
          </Space>
        )}
        toolBarRenderRight={() => (
          <Button onClick={showModal} icon={<PlusOutlined />}>
            新增密钥
          </Button>
        )}
      />
      {/* <ProTable
        instanceId={`open_list`}
        columns={columns}
        rightFrozenColCount={1}
        actionRef={actionRef}
        // rowSelection={() => {}}
        request={async (params: any) => {
          const msg = await getOpenList({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
          });
          return {
            data: msg?.data?.list || [],
            success: msg.status,
            total: msg?.data?.total,
          };
        }}
        isWarpTabs={true}
        search={false}
        toolBarRender={() => (
          <Space>
            <div className={styles.allocation}>
              <span>请求路径</span>
              <div>{REQUESTADDRESS}</div>
              <Paragraph
                copyable={{ text: `${REQUESTADDRESS}` }}
                style={{ marginLeft: '-22px', marginTop: '8px', zIndex: 99 }}
              ></Paragraph>
            </div>
            <div className={styles.allocation_appId}>
              <span>appId</span>
              <div>{appid}</div>
              <Paragraph
                copyable={{ text: `${appid}` }}
                style={{
                  marginLeft: '-22px',
                  marginTop: '8px',
                  zIndex: 99,
                  marginRight: '15px',
                }}
              ></Paragraph>
            </div>
            <Button onClick={showModal} icon={<PlusOutlined />}>
              新增密钥
            </Button>
          </Space>
        )}
        // toolBarRenderRight={() => (
        // <Button onClick={showModal} icon={<PlusOutlined />}>
        //   新增密钥
        // </Button>
        // )}
      /> */}
      <Modal
        title="新增密钥"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
        afterClose={() => form.resetFields()}
        footer={[
          <Space key="1">
            <Button
              key="back"
              onClick={handleCancel}
              style={{
                width: '88px',
                height: '37px',
                borderRadius: '4px',
              }}
            >
              取消
            </Button>
            <Button
              key="submit"
              type="primary"
              onClick={() => form?.submit()}
              style={{
                width: '88px',
                height: '37px',
                borderRadius: '4px',
              }}
            >
              确定
            </Button>
          </Space>,
        ]}
      >
        <Form form={form} labelCol={{ span: 4 }} onFinish={onFinish}>
          <Form.Item
            label="名称"
            name="name"
            className={styles.add_configName}
            rules={[{ required: true, message: '必填项不能为空' }]}
          >
            <Input placeholder="请输入名称" />
          </Form.Item>
          <Form.Item
            label="备注"
            name="remark"
            className={styles.add_configRemark}
          >
            <TextArea placeholder="请输入备注" maxLength={100} />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title="重置密钥"
        destroyOnClose
        open={secretOpen}
        onOk={getRefresh}
        onCancel={() => setSecretOpen(false)}
        footer={[
          <Space key="1">
            <Button
              key="back"
              // onClick={handleCancel}
              style={{
                width: '88px',
                height: '37px',
                borderRadius: '4px',
              }}
              onClick={() => setSecretOpen(false)}
            >
              取消
            </Button>
            <Button
              key="submit"
              type="primary"
              // onClick={() => form?.submit()}
              style={{
                width: '88px',
                height: '37px',
                borderRadius: '4px',
              }}
              onClick={getRefresh}
            >
              确定
            </Button>
          </Space>,
        ]}
      >
        重置密钥以后，原来外部系统有对接使用过该密钥的，需要同步修改，
        否则可能会造成无法连接
      </Modal>
    </div>
  );
};
