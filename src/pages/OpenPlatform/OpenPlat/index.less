.page {
  background-color: #ffffff;
  // height: 100%;
  padding: 30px 0;
  border-radius: 6px;
  min-height: 500px;
  .bgBox {
    height: auto !important;
    :global {
      .ant-btn {
        font-weight: 400;
        color: #4071ff;
        font-size: 14px;
      }
    }
    .resetting {
      :global {
        .ant-btn {
          font-weight: 400;
          color: #4071ff;
          font-size: 14px;
        }
      }
    }
    .create {
      margin-left: 28px;
    }
    height: 44px;
    margin-bottom: 26px;
    display: flex;
    align-items: center;
    .bgBoxtitle {
      font-size: 14px;
      font-weight: 400;
      color: #707070;
      text-align: right;
      width: 100px;
    }
    .contect {
      width: 700px;
      height: 44px;
      line-height: 44px;
      background: #fbfbfb;
      border-radius: 4px;
      margin-left: 32px;
      padding-left: 16px;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }
  }
}
.bottom {
  display: flex;
  align-items: center;
}
.noset {
  padding-left: 32px;
}
.allocation {
  display: flex;
  > :first-child {
    display: inline-block;
    width: 64px;
    font-weight: 400;
    font-size: 14px;
    color: #5f6984;
    margin-top: 7px;
  }
  margin-right: 20px;
  > :nth-child(2) {
    display: flex;
    align-items: center;
    padding-left: 10px;
    width: 380px;
    height: 38px;
    background: #f4f5f7;
    border-radius: 6px;
  }
}
.allocation_appId {
  display: flex;
  > :first-child {
    display: inline-block;
    width: 49px;
    font-weight: 400;
    font-size: 14px;
    color: #5f6984;
    margin-top: 7px;
  }
  > :nth-child(2) {
    display: flex;
    align-items: center;
    padding-left: 10px;
    width: 240px;
    height: 38px;
    background: #f4f5f7;
    border-radius: 6px;
  }
}
.add_configName {
  :global {
    .ant-col {
      label {
        font-weight: 400;
        font-size: 14px;
        color: #5f6984;
      }
    }
    .ant-input {
      width: 384px;
      height: 36px;
      background: #f4f5f7;
      border-radius: 6px;
    }
  }
}
.add_configRemark {
  :global {
    .ant-col {
      label {
        font-weight: 400;
        font-size: 14px;
        color: #5f6984;
      }
    }
    .ant-input {
      width: 384px;
      height: 100px;
      background: #f4f5f7;
      border-radius: 6px;
    }
  }
}
.platform {
  :global {
    .ant-pro-table-list-toolbar-right {
      width: 100% !important;
      display: flex;
      justify-content: space-between;
    }
  }
}
