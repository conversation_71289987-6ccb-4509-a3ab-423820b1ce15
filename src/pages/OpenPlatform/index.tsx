import { useState } from 'react';
import OpenPlat from './OpenPlat';
import OmsViewMarkdown from './markdown/index';
import TabsType from '@/components/TabsType';
// import TabsType from '@/components/TabsType';

const Recharge = () => {
  const [activeKey, setActiveKey] = useState<string>('1');
  const TAB_ITEMS = [
    {
      label: '配置',
      key: '1',
    },
    {
      label: '接口文档',
      key: '2',
    },
  ];
  const TAB_COMPONENTS: any = {
    '1': <OpenPlat />,
    '2': <OmsViewMarkdown />,
  };
  return (
    <>
      <TabsType
        defaultActiveKey={activeKey}
        onChange={setActiveKey}
        items={TAB_ITEMS}
      />
      {TAB_COMPONENTS[activeKey]}
    </>
  );
};

export default Recharge;
