.headStyle {
  min-height: 55px;
  background: #FFF;
  padding: 12px;
  display: flex;
  align-items: center;
  border-radius: 12px;
  font-size: 12px;
  width: 100%;
}
.content {
  text-align: left;
  padding-left: 5px;
  width: 100%;
  padding-right: 10px;
  overflow: hidden;
}
.contentName {
  color: black;
  margin-left: 5px;
  font-weight: 500;
}
.contentName > span {
  color: black;
  font-weight: 600;
}
.contentName .contentNameSpan {
  font-size: 14px;
}
