import styles from './index.less';
import { Avatar, Card, List, Tooltip } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { formatTime } from '@/utils/format';
const modifyUrl = (url: any) => {
  if (url.startsWith('https')) {
    return url;
  } else {
    return 'https://static.kmfba.com/' + url;
  }
};

const listItem: React.CSSProperties = {
  padding: 0,
  background: '#FFFFFF',
  border: 'none',
};

const OperationRecord = ({ logList }: any) => {
  return (
    <div className={styles.myCardWarp}>
      <div
        style={{
          color: '#333',
          fontSize: '18px',
          fontWeight: 600,
          marginTop: 32,
          textAlign: 'left',
        }}
      >
        操作记录
      </div>
      <List
        style={{ marginTop: 14, borderRadius: 20, overflow: 'hidden' }}
        dataSource={logList}
        renderItem={(item: any) => (
          <List.Item style={listItem}>
            <div className={styles.headStyle}>
              <div style={{width:'25%'}}>
                {formatTime(item.time)}
              </div>
              <div style={{display:'flex',width:'20%',alignItems:'center',justifyContent: 'center'}}>
                <Avatar
                  src={modifyUrl(item.operatorAvatar)}
                  size={20}
                  icon={<UserOutlined />}
                />
                <div className={styles.contentName}>{item.operatorName}</div>
              </div>
              <div style={{width:'50%'}} className={styles.content}>
                <div style={{ clear: 'both', whiteSpace: 'nowrap' }}>
                  <Tooltip
                    placement="bottom"
                    title={item.content}
                    color={'#F6F6F8'}
                    overlayInnerStyle={{
                      color: 'black',
                    }}
                  >
                    <div
                      style={{
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {item.content}
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>
          </List.Item>
        )}
      />
    </div>
  );
};
export default OperationRecord;
