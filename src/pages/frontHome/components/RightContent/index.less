@pro-header-hover-bg: rgba(0, 0, 0, 0.025);

.menu {
  :global(.anticon) {
    margin-right: 8px;
  }
  :global(.ant-dropdown-menu-item) {
    min-width: 160px;
  }
}

.right {
  display: flex;
  float: right;
  height: 48px;
  margin-left: auto;
  overflow: hidden;

  background: rgb(248, 248, 250);
  
  .action {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0 12px;
    cursor: pointer;
    transition: all 0.3s;
    > span {
      vertical-align: middle;
    }
    &:hover {
      background: rgb(248, 248, 250);
    }
    &:global(.opened) {
      background: rgb(248, 248, 250);
    }
  }
  .search {
    padding: 0 12px;
    &:hover {
      background: transparent;
    }
  }
  .account {
    .avatar {
      margin-right: 8px;
      color: @primary-color;
      vertical-align: top;
      background: rgb(248, 248, 250);
    }
  }
}

// .dark {
//   .action {
//     &:hover {
//       background: #252a3d;
//     }
//     &:global(.opened) {
//       background: #252a3d;
//     }
//   }
// }

@media only screen and (max-width: @screen-md) {
  :global(.ant-divider-vertical) {
    vertical-align: unset;
  }
  .name {
    display: none;
  }
  .right {
    position: absolute;
    top: 0;
    right: 12px;
    .account {
      .avatar {
        margin-right: 0;
      }
    }
    .search {
      display: none;
    }
  }
}
.triangle{
  /*宽高为0*/
  width: 0;
  height: 0;
  /*在三角形底边设置一个边界颜色/
  border-top: 20px solid red;
  /*其它3边设置相同颜色，*/
  border-bottom: 0 ;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 6px solid #BCBCC9;
}
