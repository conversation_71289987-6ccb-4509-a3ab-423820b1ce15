.layout {
  background: #F6F6F8;
  align-items: center;
}
.layout .leftCard {
  background: #F6F6F8;
}
.layout .leftCard :global(.ant-pro-card-body) {
  padding: 5px 12px !important;
}
.layout .HeaderName {
  font-weight: 600;
  font-size: 20px;
}
.layout .rightCard {
  background: #F6F6F8;
}
.layout .rightCard :global(.ant-pro-card-body) {
  padding: 5px 12px !important;
}
.layout .layoutCard {
  background: #F6F6F8;
}
.mapBg {
  height: 518px;
  background: #FFFFFF;
  box-shadow: 0px 30px 40px 0px #F0EFF6;
  border-radius: 20px;
  width: 100%;
  position: relative;
  padding: 8px;
}
.mapBg .map {
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: 50%;
  width: 93%;
  background: url(image/map.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: 0 0;
  height: 460px;
  margin: auto;
}
.title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  line-height: 37px;
  margin-left: 16px;
  margin-bottom: 20px;
}
.h2Tit {
  font-size: 16px;
  font-weight: 600;
  color: #4071FF;
  margin-top: -2px;
}
.descriptionCard {
  width: 100%;
  height: 60px;
  background: #fff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  padding-left: 12px;
  color: #333;
  font-weight: 600;
}
.descriptionCard .ocard {
  color: orangered;
  margin-right: 5px;
}
