.name{
    font-size: 14px;
    border: none;
}
.table{
    box-shadow: 0px 30px 40px 0px #F0EFF6;
    font-weight: 600;
    :global(.ant-table-thead ) {
            padding: 15px 20px !important;
    }
    :global(.ant-table-tbody >tr >td){
        border: none!important;
    }
     :global(.ant-table-tbody >.ant-table-row){
        height: 40px;
    }
    :global(.ant-table-thead>tr>th ) {
        background-color: white!important;
}
    :global(.ant-table-thead>tr>th::before ) {
        width: 0!important;
    }
    
}
.USimg{
    display: inline-block;
    width: 19px;
    height: 9px;
    // background-image: url('../images/US.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
.USflex{
    display: flex;
    align-items: center;
}