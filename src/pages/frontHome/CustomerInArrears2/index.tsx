import { Avatar, Space, Table, Tooltip } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import styles from './index.less';
import { formatTime } from '@/utils/format';
const modifyUrl = (url: any) => {
  if (url.startsWith('https')) {
    return url;
  } else {
    return 'https://static.kmfba.com/' + url;
  }
};
const CustomerInArrears = ({ insufficientFundClients }: any) => {
  const columns: any = [
    {
      title: [
        <div
          style={{ paddingLeft: '16px', fontWeight: 400, color: '#B7B7B7' }}
          key="1"
        >
          操作人
        </div>,
      ],
      dataIndex: 'operatorName',
      width:120,
      render: (text: any, record: any) => {
        return (
          <Space wrap size={5} style={{ paddingLeft: '16px' }}>
            <div>
              <Avatar
                src={modifyUrl(record.operatorAvatar)}
                size={20}
                icon={<UserOutlined />}
              />
            </div>
            <div className={styles.tableText}>
              <div style={{ fontWeight: 400 }}>{text}</div>
            </div>
          </Space>
        );
      },
    },
    {
      title: [
        <div style={{ fontWeight: 400, color: '#B7B7B7' }} key="1">
          操作记录
        </div>,
      ],
      dataIndex: 'content',
      ellipsis: {
        showTitle: false,
      },
      render: (text: any) => {
        return (
          <Tooltip placement="topLeft" title={text}>
            <div style={{ fontWeight: 400, color: '#333' }}>{text}...</div>
          </Tooltip>
        );
      },
    },
    {
      title: [
        <div style={{ fontWeight: 400, color: '#B7B7B7' }} key="1">
          操作时间
        </div>,
      ],
      dataIndex: 'time',
      ellipsis: true,
      render: (text: any) => {
        return (
          <div className={styles.tableText}>
            <div style={{ fontWeight: 400 }}>{formatTime(text)}</div>
          </div>
        );
      },
    },
  ];
  return (
    <div className={styles.myCardWarp} style={{marginBottom:32}}>
      <div
        style={{
          color: '#333',
          fontSize: '18px',
          fontWeight: 600,
          textAlign: 'left',
          marginTop: 32,
          position: 'relative',
        }}
      >
        操作记录
      </div>
      <Table
        className={styles.table}
        style={{
          borderRadius: '20px',
          overflow: 'hidden',
          marginTop: '14px',
          minHeight: 360,
          background: '#fff',
        }}
        columns={columns}
        dataSource={insufficientFundClients}
        size="middle"
        rowKey="id"
        pagination={false}
      />
    </div>
  );
};
export default CustomerInArrears;
