import { useEffect, useState } from 'react';
import { getHomeDataAPI } from '@/services/home/<USER>';
import { ProCard } from '@ant-design/pro-components';
import { Layout, Typography } from 'antd';
import styles from './index.less';
import { Col, Row } from 'antd';
import AmountDisplay from './AmountDisplay';
// import OperationRecord from './OperationRecord';
import OrderList from './OrderList';
import CustomerInArrears2 from './CustomerInArrears2';
const { Paragraph } = Typography;
import MapComponents from './MapComponents';
// import CustomHeader from '@/components/CustomHeader';

// const headerStyle: React.CSSProperties = {
//   height: 60,
//   lineHeight: '64px',
//   backgroundColor: '#F6F6F8',
//   display: 'flex',
//   justifyContent: 'space-between',
//   alignItems: 'center',
//   paddingLeft: '20px',
//   paddingRight: '20px',
// };
const contentStyle: React.CSSProperties = {
  textAlign: 'center',
  minHeight: 120,
  lineHeight: '120px',
  backgroundColor: '#F6F6F8',
};
const Procardbody: React.CSSProperties = {
  padding: 0,
};
export default () => {
  const { Content } = Layout;
  const [data, setData] = useState<any>({});
  //金额对象
  const [dataObj,setDataObj] = useState<any>({});
  const [title, setTitle] = useState<string>('');
  const getHomeData = async () => {
    try {
      const { status, data } = await getHomeDataAPI({});
      if (status) {
        setData(data);
        localStorage.setItem('hNAEM', data?.tenant?.fullName);
        setTitle(data?.tenant?.fullName);
        setDataObj({...data?.tenant,...data?.instance})
      }
    } catch (e) {
      console.log(e); 
    }
  };
  useEffect(() => {
    getHomeData();
  }, []);

  /* 可打金额 */
  const [amountMoney, setAmountMoney] = useState(0);
  useEffect(() => {
    if(Object.keys(dataObj).length === 0) return;
    let num = (Number(dataObj?.total_balance) + Number(dataObj?.credit_amount)) -
    (Number(dataObj?.frozen_amount) + Number(dataObj?.deposit) + Number(dataObj?.pre_payment_amount));
    setAmountMoney(num)
  }, [dataObj]);

  return (
    <Row>
      <Col span={24}>
        {/* <CustomHeader data={data}/> */}
        <div className={styles.title}>
          <div>{title}</div>
          <Paragraph className={styles.h2Tit} copyable>
            {data?.instance?.id || ''}
          </Paragraph>
        </div>
        <div style={{padding:'12px'}}>
          <div className={styles.descriptionCard}>
            可打金额<span className={styles.ocard}>$({(amountMoney)?.toFixed(2)||'-'})</span> = 余额<span className={styles.ocard}>$({dataObj?.total_balance||'-'})</span>  + 信用额度<span className={styles.ocard}>$({dataObj?.credit_amount || '-'}) </span>  - 押金<span className={styles.ocard}>$({dataObj?.deposit ||'-'})</span>  - 冻结资金<span className={styles.ocard}>$({dataObj?.frozen_amount || '-'})</span> - 预扣费金额<span className={styles.ocard}>$({dataObj?.pre_payment_amount||'-'})</span>
          </div>
        </div>
        <div className={styles.layout}>
          <ProCard className={styles.layoutCard} bodyStyle={Procardbody}>
            {/* 头部 */}
            {/* <Header style={headerStyle}>
              <TitleSection dataList={data.tenant} instance={data.instance}/>
            </Header> */}
            {/* 内容 */}
            <Content style={contentStyle}>
              {/* 内容左边 */}
              <Row style={{ width: '100%' }}>
                <Col span={11}>
                  <ProCard className={styles.leftCard}>
                    {/* 卡片金额数据 */}
                    <AmountDisplay
                      dataList={{ ...data.tenant, ...data.instance }}
                    />
                    {/* 新操作记录  */}
                    <CustomerInArrears2
                      insufficientFundClients={data.logList}
                    />
                    {/* 操作记录 */}
                    {/* <OperationRecord logList={data.logList} /> */}
                  </ProCard>
                </Col>
                <Col span={13}>
                  <ProCard className={styles.rightCard}>
                    {/* map */}
                    <div className={styles.mapBg}>
                      {/* <div className={styles.map}></div> */}
                      <MapComponents />
                    </div>
                    {/* 最近出账单号 */}
                    <OrderList billList={data.newestReconciliation} />
                  </ProCard>
                </Col>
              </Row>
            </Content>
          </ProCard>
        </div>
      </Col>
    </Row>
  );
};
