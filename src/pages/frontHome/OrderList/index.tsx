import moment from 'moment';
import styles from './index.less';
import { Progress, Table } from 'antd';
const OrderList = ({ billList }: any) => {
  // console.log(444,billList)
  // const data1 = [
  //   {'waybillId':'KM666666','amount':6666,'totalPiecesNumber':6,'completedPiecesNumber':1,'time':666666},
  //   {'waybillId':'KM888888','amount':8888,'totalPiecesNumber':8,'completedPiecesNumber':3,'time':888888}
  // ]
  
  const columns: any = [
    {
      title: [
        <div style={{ color: '#AEAEAE' ,paddingLeft:10}} key="1">
          单号
        </div>,
      ],
      dataIndex: 'waybillId',
      render: (text: any) => {
        return (
          <div>
            <div style={{ color: '#333', fontWeight: 600 ,paddingLeft:10}}>{text}</div>
          </div>
        );
      },
      width:'20%'
    },
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">

          金额
        </div>,
      ],
      dataIndex: 'amount',
      render: (text: any) => {
        return (
          <div className={styles.USflex} style={{ color: '#333', fontWeight: 600 }}>
            <div className={styles.USimg}></div>&ensp;<div>{text}</div>
          </div>
        )
      },
      width:'20%'
    },
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          包裹
        </div>,
      ],
      dataIndex: 'totalPiecesNumber',
      render: (text: any,record:any) => {
        let num:any = record.completedPiecesNumber/record.totalPiecesNumber
        return <div style={{ color: '#333', fontWeight: 600 }}>
          <div style={{display:'flex',alignItems:'center'}}>
            <div>{record.completedPiecesNumber}/{record.totalPiecesNumber}</div>
            &ensp;
            <div style={{width:'80%'}}><Progress style={{margin:0,paddingBottom:3}} className={styles.progress} percent={Math.round(num*100)} showInfo={false} /></div>
          </div>
        </div>
      },
      width:'35%'
    },
    {
      title: [
        <div style={{ color: '#AEAEAE' }} key="1">
          时间
        </div>,
      ],
      dataIndex: 'time',
      valueType: 'dateTime',
      render: (text: any,record:any) => {
        return <div style={{ color: '#333', fontWeight: 600 }}>
          {record?.time && moment(record?.time).format('YYYY-MM-DD HH:mm:ss')}
        </div>
      },
      width:'25%'
    },
  ];

  return (
    <div className={styles.myCardWarp}>
      <div
        style={{
          color: '#333',
          fontSize: '18px',
          fontWeight: 600,
          marginTop: 32,
          textAlign:'left',
          position:'relative'
        }}
      >
        最新出账单号
      </div>
      <Table
        className={styles.table}
        style={{ borderRadius: '20px', overflow: 'hidden', marginTop: '14px',minHeight:360 ,background:'#fff'}}
        columns={columns}
        dataSource={billList}
        size="middle"
        rowKey="waybillId"
        pagination={false}
      />
    </div>
  );
};
export default OrderList;
