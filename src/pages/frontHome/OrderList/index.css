.name {
  font-size: 14px;
  border: none;
}
.table {
  font-weight: 600;
  box-shadow: 0px 30px 40px 0px #F0EFF6;
}
.table :global(.ant-table-thead) {
  padding: 15px 20px !important;
}
.table :global(.ant-table-tbody >tr >td) {
  border: none !important;
}
.table :global(.ant-table-thead>tr>th) {
  background-color: white !important;
}
.table :global(.ant-table-thead>tr>th::before) {
  width: 0 !important;
}
.USimg {
  display: inline-block;
  width: 19px;
  height: 9px;
  background-image: url('../image/US.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.USflex {
  display: flex;
  align-items: center;
}
.progress :global(.ant-progress) {
  margin-inline-end: 0!important;
  margin-bottom: 0!important;
}
