import styles from './index.less';
import { Col, Row } from 'antd';
import { useEffect, useState } from 'react';
const AmountDisplay = ({ dataList }: any) => {
  // const [activeIndex, setActiveIndex] = useState(0); // 初始值为 0，表示默认选中第一个选项
  /* 可打金额 */
  const [amountMoney, setAmountMoney] = useState(0);
  useEffect(() => {
    if(Object.keys(dataList).length === 0) return;
    let num = (Number(dataList?.total_balance) + Number(dataList?.credit_amount)) -
    (Number(dataList?.frozen_amount) + Number(dataList?.deposit) + Number(dataList?.pre_payment_amount));
    setAmountMoney(num)
  }, [dataList]);
  return (
    <>
      <div className={styles.cardWarp}>
        <Row justify={'space-between'}>
          <Col span={9}>
            <div className={styles.card1}>
              <div className={styles.card1img}></div>
              <div className={styles.card1wrap}>
                <div style={{ fontSize: '12px', color: '#FFFFFF' }}>
                  可打单金额
                </div>
                <div className={styles.amountMoney}>
                {(amountMoney)?.toFixed(2)}
                </div>
                <div className={styles.card1US}></div>
              </div>
            </div>
          </Col>
          <Col span={7}>
            <div className={styles.card2}>
              <div className={styles.card2img}></div>
              <div className={styles.card2wrap}>
                <div style={{ color: '#AEAEAE', fontSize: '12px' }}>余额</div>
                <div className={styles.amountMoney}>
                {dataList?.total_balance}
                </div>
                <div className={styles.card2US}></div>
              </div>
            </div>
          </Col>
          <Col span={7}>
            <div className={styles.card3}>
              <div className={styles.card3img}></div>
              <div className={styles.card3wrap}>
                <div style={{ color: '#AEAEAE', fontSize: '12px' }}>
                  信用额度
                </div>
                <div className={styles.amountMoney}>
                {dataList?.credit_amount}
                </div>
                <div className={styles.card3US}></div>
              </div>
            </div>
          </Col>
        </Row>
  
        <div style={{boxShadow: '0px 30px 40px 0px #F0EFF6',borderRadius:'20px',overflow:'hidden',marginTop:'20px',position: 'relative',
zIndex: 2,background:'#fff',paddingLeft:18}}>
          <Row>
            <Col span={8}>
              <div
                className={styles.card4}
              >
                <div className={styles.card4img}></div>
                <div className={styles.card4wrap}>
                  <div style={{ color: '#AEAEAE', fontSize: '12px' }}>
                    预扣费金额
                  </div>
                  <div className={styles.amountMoney}>
                    {dataList.pre_payment_amount}
                  </div>
                  <div className={styles.card4US}></div>
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.card4}>
                <div className={styles.card5img}></div>
                <div className={styles.card4wrap}>
                  <div style={{ color: '#AEAEAE', fontSize: '12px' }}>
                    冻结资金
                  </div>
                  <div className={styles.amountMoney}>
                    {dataList?.frozen_amount}
                  </div>
                  <div className={styles.card4US}></div>
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div
                className={styles.card4}
              >
                <div className={styles.card6img}></div>
                <div className={styles.card4wrap}>
                  <div style={{ color: '#AEAEAE', fontSize: '12px' }}>
                    押金
                  </div>
                  <div className={styles.amountMoney}>{dataList.deposit}</div>
                  <div className={styles.card4US}></div>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </>
  );
};

export default AmountDisplay;
