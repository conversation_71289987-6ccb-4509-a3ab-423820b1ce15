export default (initialState: API.UserInfo) => {
  const { currentUser={} }: any = initialState;
  const { authorities=[] } = currentUser;
  // 在这里按照初始化数据定义项目中的权限，统一管理
  // 参考文档 https://next.umijs.org/docs/max/access
  const SuperAdministrator = authorities.includes('Administrator');
  const DataManagement = SuperAdministrator || authorities.includes('Waybill:Address:FullAccess') || authorities.includes('Waybill:Address:ReadOnlyAccess');
  const AddressLibrary = DataManagement || authorities.includes('Waybill:Address:FullAccess' ) || authorities.includes('Waybill:Address:ReadOnlyAccess');
  const AddressLibraryReadOnly = authorities.includes('Waybill:Address:ReadOnlyAccess');
  const ReconciliationManagement = SuperAdministrator || authorities.includes('Waybill:Reconciliation:FullAccess') || authorities.includes('Waybill:Reconciliation:ReadOnlyAccess');
  const ReconciliationManagementReadOnly = authorities.includes('Waybill:Reconciliation:ReadOnlyAccess');
  const ConsumptionDetails = SuperAdministrator || authorities.includes('Waybill:Bill:FullAccess') || authorities.includes('Waybill:Bill:ReadOnlyAccess');
  const ConsumptionDetailsReadOnly = authorities.includes('Waybill:Bill:ReadOnlyAccess');
  const Waybill = SuperAdministrator || authorities.includes('Waybill:Waybill:FullAccess') || authorities.includes('Waybill:Waybill:ReadOnlyAccess');
  const WaybillReadOnly = authorities.includes('Waybill:Waybill:ReadOnlyAccess'); 
  const OpenAPI = SuperAdministrator || authorities.includes('Waybill:OpenAPI:FullAccess') || authorities.includes('Waybill:OpenAPI:ReadOnlyAccess');
  const OpenAPIReadOnly = authorities.includes('Waybill:OpenAPI:ReadOnlyAccess');
  const RAM = SuperAdministrator || authorities.includes('Waybill:RAM:FullAccess') || authorities.includes('Waybill:RAM:ReadOnlyAccess');
  const RAMReadOnly = authorities.includes('Waybill:RAM:ReadOnlyAccess');
  //Waybill:Waybill:TruckFullAccess
  const TruckFullAccess = authorities.includes('Waybill:Waybill:TruckFullAccess');
  

  /***
   * 这里有个坑 根据后端要求：
   * 如果只分配了只读权限，那么在地址库页面，只能看到地址库的内容，但是不能进行任何操作，包括新增、编辑、删除等
   * 如果分配了完全权限，那么在地址库页面，可以看到地址库的内容，也可以进行任何操作，包括新增、编辑、删除等
   * 如果分配了只读权限和完全权限，两个同时存在 会导致只读权限失效，只有完全权限生效
   * 也就是说 根据 “需求” 当只设置只读 时才是只读  都设置时 只读的优先级不高 只读失效。
   * 如果后面改掉这个“问题”删除后面箭头函数即可
   *  */
  return {
    SuperAdministrator, //是否有超级管理员权限
    DataManagement, ////资料管理权限
    AddressLibrary,//地址库权限
    AddressLibraryReadOnly:()=>AddressLibraryReadOnly && !authorities.includes('Waybill:Address:FullAccess'),//地址库只读
    ReconciliationManagement,//对账管理
    ReconciliationManagementReadOnly:()=>ReconciliationManagementReadOnly &&!authorities.includes('Waybill:Reconciliation:FullAccess') ,//对账管理只读
    ConsumptionDetails,//消费明细
    ConsumptionDetailsReadOnly:()=>ConsumptionDetailsReadOnly && !authorities.includes('Waybill:Bill:FullAccess'),//消费明细只读
    Waybill,//运单管理
    WaybillReadOnly:()=> WaybillReadOnly && !authorities.includes('Waybill:Waybill:FullAccess'),//运单管理只读
    OpenAPI,//开放平台
    OpenAPIReadOnly:()=> OpenAPIReadOnly&& !authorities.includes('Waybill:OpenAPI:FullAccess'),//开放平台只读
    RAM,
    RAMReadOnly:()=> RAMReadOnly && !authorities.includes('Waybill:RAM:FullAccess'),
    TruckFullAccess,
  };
};

