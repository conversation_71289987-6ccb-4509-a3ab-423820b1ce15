// 运行时配置
import type { RunTimeLayoutConfig } from 'umi';
import { history } from 'umi';
import {
  ChromeFilled,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
// import RightContent from './components/RightContent';
import HeadAssembly from './components/HeadAssembly';
import layouttoken from './layouttoken';
import { errorConfig } from './requestErrorConfig';
import { useState } from 'react';
import {
  PageLoading,
  ProBreadcrumb,
  SettingDrawer,
} from '@ant-design/pro-components';
import { REQUESTADDRESS } from '@/globalData';
import defaultSettings from '../config/defaultSettings';
import { ModuleRegistry } from 'ag-grid-community';
import {
  AllEnterpriseModule,
  LicenseManager,
  IntegratedChartsModule,
} from 'ag-grid-enterprise';
import { AgChartsEnterpriseModule } from 'ag-charts-enterprise';

// Register all Community and Enterprise features
ModuleRegistry.registerModules([
  AllEnterpriseModule,
  IntegratedChartsModule.with(AgChartsEnterpriseModule),
]);

LicenseManager.setLicenseKey(
  'AgGridLicense66fwc79n[NORMAL][v0102]_NDA3MDk2NjQwMDAwMA==80908dd5fb71b58d3ce28b2ed320216d',
);

const loginPath = '/403';

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://next.umijs.org/docs/api/runtime-config#getinitialstat
export async function getInitialState(): Promise<{
  settings?: any;
  currentUser?: any;
  loading?: boolean;
  fetchUserInfo?: () => Promise<any | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      let msg: any = {};
      await fetch(
        `${REQUESTADDRESS}/authing/identity?service_id=Waybill&type=json`,
        { mode: 'cors', credentials: 'include' },
      )
        .then((res) => res.json())
        .then((body) => {
          const { status, data } = body;
          if (status) {
            localStorage.setItem('user', data.user);
            localStorage.setItem('token', data.accessToken);
            localStorage.setItem('instance', JSON.stringify(data.instance));
            msg.data = data;
            if (history.location.pathname === '/403') {
              history.push('/');
            }
            // history.push('/');
          } else {
            history.push('/403');
          }
        })
        .catch(() => {
          history.push('/403');
          console.log('错误');
        });
      return msg.data;
    } catch (error) {
      history.push(loginPath);
    }
    return undefined;
  };
  //
  if (history.location.pathname !== '/404') {
    const currentUser = await fetchUserInfo();
    return {
      fetchUserInfo,
      currentUser,
      settings: defaultSettings,
    };
  }
  return {
    fetchUserInfo,
    settings: defaultSettings,
  };
}

export const layout: RunTimeLayoutConfig | any = ({
  initialState,
  setInitialState,
}: // setInitialState,
any) => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);

  return {
    onPageChange: () => {
      // 如果没有授权登录，重定向到 无权限页面
      if (!localStorage.getItem('token')) {
        history.push('/403');
      }
    },
    siderWidth: 208,
    title: '鸣锐打单云平台',
    waterMarkProps: {
      content: '浙江扬程国际物流信息科技有限公司',
    },
    // rightContentRender: () => <RightContent />,
    rightContentRender: false,
    menu: {
      locale: false,
    },
    headerContentRender: () => <ProBreadcrumb />,
    collapsed: isCollapsed,
    postMenuData: (menuData: any) => {
      const { currentUser } = initialState;
      const { waybillManagementService = {} } = currentUser;
      const result = menuData.filter((item: any) => item.name === '后台管理');
      //获取当前url地址
      const url = window.location.href;
      
      if (waybillManagementService?.console_page?.length && !result.length) {
        menuData.push({
          path:url?.includes('staging') ? 'https://staging-web-waybill-console.kmfba.com': waybillManagementService?.console_page,
          name: '后台管理',
          icon: <ChromeFilled />,
        });
      }
      return menuData;
    },
    collapsedButtonRender: (collapsed: any) => {
      return (
        <div
          style={{
            display: 'flex',
            // justifyContent: !collapsed ? '' : 'center',
            padding: '20px',
            animation: '.8',
            cursor: 'pointer',
          }}
          onClick={() => {
            setIsCollapsed(!isCollapsed);
          }}
        >
          {collapsed ? (
            <MenuUnfoldOutlined style={{ fontSize: 18, color: '#333' }} />
          ) : (
            <MenuFoldOutlined style={{ fontSize: 18, color: '#333' }} />
          )}
        </div>
      );
    },
    // unAccessible: <div>unAccessible</div>,
    childrenRender: (children: any) => {
      if (initialState?.loading) return <PageLoading />;
      return (
        <>
          <HeadAssembly />
          <div>{children}</div>
          {false && (
            <SettingDrawer
              disableUrlParams
              enableDarkTheme
              settings={initialState?.settings}
              onSettingChange={(settings) => {
                setInitialState((preInitialState: any) => ({
                  ...preInitialState,
                  settings,
                }));
              }}
            />
          )}
        </>
      );
    },
    ...initialState?.settings,
    token: layouttoken,
    disableMobile: true,
  };
};

/* 乾坤 生命周期钩子 */
export const qiankun = {
  // 应用加载之前
  async bootstrap(props: any) {
    console.log('app1 应用加载之前', props);
  },
  // 应用 render 之前触发
  async mount(props: any) {
    console.log('app1 应用 render 之前触发', props);
  },
  // 应用卸载之后触发
  async unmount(props: any) {
    console.log('app1 应用卸载之后触发', props);
  },
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
};
