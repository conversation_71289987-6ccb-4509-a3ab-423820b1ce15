import { request } from '@umijs/max';
import { REQUESTADDRESS_W } from '@/globalData';
/* 获取视图 */
export const getViewAPI = (params: any) => {
    return request(`${REQUESTADDRESS_W}/view/get`, {
        method: 'get',
        params,
    });
};
// 创建视图
export const getCreateAPI = (data: any) => {
    return request(`${REQUESTADDRESS_W}/view/create`, {
        method: 'post',
        data,
        requestType: 'json',
    });
};
// 已经保存过的视图
export const getListAPI = (params: any) => {
    return request(`${REQUESTADDRESS_W}/view/getList`, {
        method: 'get',
        params,
    });
};

/* 删除视图  */
export const deleteViewAPI = (params: any) => {
    return request(`${REQUESTADDRESS_W}/view/remove`, {
        method: 'post',
        params,
    });
};

/* 设置默认视图 */
export const setDefaultViewAPI = (params: any) => {
    return request(`${REQUESTADDRESS_W}/view/setDef`, {
        method: 'post',
        params,
    });
};

/* 更新视图 */
export const updateViewAPI = (data: any) => {
    return request(`${REQUESTADDRESS_W}/view/update`, {
        method: 'post',
        data,
        requestType: 'json',
    });
};
