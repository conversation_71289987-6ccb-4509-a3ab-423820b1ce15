import { request } from '@umijs/max';
import { REQUESTADDRESS, REQUESTADDRESS_W } from '@/globalData';
/* 获取用户登录token  */
export const getUserToken = (params: any) => {
  // return request('https://dev-server-home.kmfba.com/api/authing/identity', {
  //   method: 'GET',
  //   params,
  // });
  return request(`${REQUESTADDRESS}/authing/identity`, {
    method: 'GET',
    params,
  });
};
/* 账号信息 */
/* 获取用户信息 */
export const getUserInfo = (params: any) => {
  return request(`${REQUESTADDRESS}/user/getProfile`, {
    method: 'GET',
    params,
  });
};
/* 开放品台 新增配置 */
export const getOpenAdd = (data: any) => {
  return request(`${REQUESTADDRESS}/open/add`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};
/* 开放品台 状态操作 */
export const getOpenEnable = (params: any) => {
  return request(`${REQUESTADDRESS}/open/enable`, {
    method: 'get',
    params,
    // requestType: 'json',
  });
};
/* 日志记录 */
export const getLogListAPI = async (params: any) => {
  return request(`${REQUESTADDRESS_W}/log/getList`, {
    method: 'GET',
    params,
  });
};
/* 开放品台 重置 */
export const getOpenRefresh = (params: any) => {
  return request(`${REQUESTADDRESS}/open/refresh`, {
    method: 'get',
    params,
  });
};
/* 开放品台 配置列表 */
export const getOpenList = (params: any) => {
  return request(`${REQUESTADDRESS}/open/getList`, {
    method: 'get',
    params,
  });
};
/* 上传头像 */
export const uploadAvatarAPI = (data: any) => {
  return request(`${REQUESTADDRESS}/home/<USER>/uploadAvatar`, {
    method: 'POST',
    data,
  });
};

/* 获取账号信息 */
export const getAccountInformationAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/getAccount`, {
    method: 'GET',
    params,
  });
};
/* 身份校验 */
export const identityCheckAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/getIdentityCheckTypes`, {
    method: 'GET',
    params,
  });
};
/* 重置密码 */
export const resetPasswordAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/modifyPassword`, {
    method: 'GET',
    params,
  });
};

/* 短信邮箱密码校验 */
export const checkCodeAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/check`, {
    method: 'post',
    data: params,
  });
};
/* 发送邮箱验证吗 */
export const sendEmailCodeAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/sendEmailCode`, {
    method: 'GET',
    params,
  });
};

/* 绑定手机号 */
export const bindPhoneAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/bindPhone`, {
    method: 'GET',
    params,
  });
};
/* 解绑手机 */
export const unbindPhoneAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/unbindPhone`, {
    method: 'GET',
    params,
  });
};
/* 绑定邮箱 */
export const bindEmailAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/bindEmail`, {
    method: 'GET',
    params,
  });
};
/* 解绑邮箱 */
export const unbindEmailAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/unbindEmail`, {
    method: 'GET',
    params,
  });
};

/* 获取手机验证码 */
export const getPhoneCodeAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/sendSMSCode`, {
    method: 'GET',
    params,
  });
};

/* 修改名字 */
export const changeNameAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/modifyName`, {
    method: 'GET',
    params,
  });
};

/* 修改默认企业 */
export const modifyDefaultEnterpriseAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/user/setDefaultTenant`, {
    method: 'GET',
    params,
  });
};
/* 退出登录 */
export const logoutAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/authing/quit`, {
    method: 'GET',
    params,
  });
};
/* 获取操作日志 */
export const getOperationLog = (params: any) => {
  return request(`${REQUESTADDRESS_W}/log/getList`, {
    method: 'get',
    params,
  });
};

/* 后面空了 在抽出来 */
/* 资料管理 */
/* 前台获取地址库列表 */
export const getAddressInformationAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/getList`, {
    method: 'GET',
    params,
  });
};
/* 省市区模糊搜索 */
export const getSearchAddressAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/city/search`, {
    method: 'GET',
    params,
  });
};
/* 获取省市区数据 US */
export const getUSAddressAPI = (params: any) => {
  return request(`https://web-common.kmfba.com/location/country.json`, {
    method: 'get',
    params,
  });
};

/* 地址库添加地址 */
export const addAddressAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/add`, {
    method: 'GET',
    params,
  });
};

/* 地址库 删除地址 */
export const deleteAddressAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/remove`, {
    method: 'GET',
    params,
  });
};
/* 修改地址库信息 */
export const modifyAddressAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/modify`, {
    method: 'GET',
    params,
  });
};
/* 后台获取地址库 获取系统地址库/FBA */
export const getAddressLibraryAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/getStreetList`, {
    method: 'get',
    params,
  });
};
/* 获取发件地址 */
export const getSenderAddressAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/getSendAddressList`, {
    method: 'get',
    params,
  });
};
/* 获取 区域 地址 */
export const getAreaAddressAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/getChannelGroupList`, {
    method: 'get',
    params,
  });
};

/* 获取渠道产品列表 */
export const getChannelProductAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/getClientChannelList`, {
    method: 'get',
    params,
  });
};

/* 获取渠道产品详情 */
export const getChannelProductDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/get`, {
    method: 'get',
    params,
  });
};

/* 获取消费明细列表 */
export const getConsumptionDetailAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/bill/getList`, {
    method: 'get',
    params,
  });
};

/* 运单创建 立即试算 */
export const getWaybillEstimateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/tryCalc`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 多票创建 */
export const getWaybillBatchCreateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/multiCreate`, {
    method: 'post',
    data,
  });
};

/* 客户渠道条数 */
export const getChannelCountAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/channel/getClientChannelCount`, {
    method: 'post',
    data: params.Address,
    requestType: 'json',
  });
};

/* 运单导出 */
export const waybillExportAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/download`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};

/* 运单创建 立即预报 */
export const getWaybillReportAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/create`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 获取运单列表 */
export const getWaybillListAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/list`, {
    method: 'post',
    data,
  });
};
/* 运单列表详情 */
export const waybillListDetailsAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/one`, {
    method: 'get',
    params,
  });
};
/* 获取包裹信息 */
export const getPackageInformationAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/pieces/list`, {
    method: 'get',
    params,
  });
};
/* 取消运单 */
export const cancelWaybillAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/cancel`, {
    method: 'get',
    params,
  });
};
/* 批量撤销运单 */
export const batchCancelAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/batchCancel`, {
    method: 'post',
    data,
    // requestType: 'json',
  });
};
/* 省市区查询 */
export const addressFuzzySearchAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/city/search`, {
    method: 'get',
    params,
  });
};

/* 支付金额接口 */
export const payMoneyAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/recharge/service/alipay`, {
    method: 'post',
    data: params,
    requestType: 'json',
  });
};

/* 对账管理查询列表 */
export const getReconciliationListAPI = (data: {
  start: string | number;
  len: string | number;
  insertTime?: any;
  endTime?: any;
  key?: string | number; //运单id 子运单号
  keyword?: string;
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/waybill/list`, {
    method: 'post',
    data,
  });
};

/* 对账管理 配置列表 */
export const getReconciliationConfigListAPI = (params: {
  start: string | number;
  len: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/list`, {
    method: 'get',
    params,
  });
};

/* 对账管理 新增配置 */
export const addReconciliationConfigAPI = (params: {
  name: string; //配置名称
  issueNo: 'A'; //期号
  subExpressCode: 'B'; //ups
  outerOrderId: 'C'; //关联订单号
  amount: 'D'; //金额
  reason: 'E'; //原因
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/create`, {
    method: 'post',
    data: params,
  });
};

/* 对账管理 删除配置 */
export const deleteReconciliationConfigAPI = (params: {
  id: string; //配置id
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/delete`, {
    method: 'post',
    data: params,
  });
};

/* 对账管理 修改配置 */
export const updateReconciliationConfigAPI = (params: {
  id: string; //配置id
  name: string; //配置名称
  issueNo: string; //期号
  subExpressCode: string; //ups
  outerOrderId: string; //关联订单号
  amount: string; //金额
  reason: string; //原因
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/update`, {
    method: 'post',
    data: params,
  });
};

/* 上传Excel */
export const uploadExcelAPI = (params: {
  multiFile: any;
  configId: string; //配置id
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/upload`, {
    method: 'post',
    data: params,
  });
};

/* 对账运单详情 */
export const getReconciliationWaybillDetailAPI = (params: {
  id: string; //运单id
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/waybill/one`, {
    method: 'get',
    params,
  });
};

/* 运单详情明细 */
export const getWaybillDetailAPI = (params: {
  id: string; //运单id
  len: string | number;
  start: string | number;
}) => {
  return request(`${REQUESTADDRESS_W}/reconciliation/waybill/getBills`, {
    method: 'get',
    params,
  });
};

/* 面单下载 */
export const downloadWaybillAPI = (params: {
  id: string; //运单id
}) => {
  return request(`${REQUESTADDRESS_W}/waybill/getLabel`, {
    method: 'get',
    params,
  });
};
/* 发票下载  单个*/
export const downloadInvoiceAPI = (params: {
  id: string; //运单id
}) => {
  return request(`${REQUESTADDRESS_W}/waybill/getInvoice`, {
    method: 'get',
    params,
  });
};
/* 发票下载  批量*/
export const downloadInvoicesAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/downloadInvoices`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};
/* 权限管理 */
/* 获取角色列表 */
export const getRoleListAPI = (params: {
  start?: string | number;
  len?: string | number;
}) => {
  return request(`${REQUESTADDRESS}/ram/getRoleList`, {
    method: 'get',
    params,
  });
};

/* 是否启用角色 */
export const enableRoleAPI = (params: {
  roleId: string | number;
  enabled: number | string;
}) => {
  return request(`${REQUESTADDRESS}/ram/enableRole`, {
    method: 'post',
    data: params,
  });
};

/* 获取角色权限 */
export const getRolePermissionAPI = (params: { roleId?: string | number }) => {
  return request(`${REQUESTADDRESS}/ram/getRole`, {
    method: 'get',
    params,
  });
};

/* 获取当前实例权限目录 */
export const getPermissionCatalogAPI = (params: {
  roleId?: string | number;
}) => {
  return request(`${REQUESTADDRESS}/ram/getInstanceRuleList`, {
    method: 'get',
    params,
  });
};

/* 创建角色 */
export const createRoleAPI = (params: {
  name: string;
  authorities: string;
}) => {
  return request(`${REQUESTADDRESS}/ram/createRole`, {
    method: 'post',
    data: params,
  });
};

/* 删除角色 */
export const deleteRoleAPI = (params: { roleId: string | number }) => {
  return request(`${REQUESTADDRESS}/ram/deleteRole`, {
    method: 'post',
    data: params,
  });
};

/* 编辑角色获取权限 */
export const getEditRolePermissionAPI = (params: {
  roleId: string | number;
}) => {
  return request(`${REQUESTADDRESS}/ram/getRoleRuleList`, {
    method: 'get',
    params,
  });
};
/* 编辑角色权限 */
export const editRolePermissionAPI = (params: {
  roleId: string | number;
  authorities: string;
  name?: string;
}) => {
  return request(`${REQUESTADDRESS}/ram/modifyRoleAuthorities`, {
    method: 'post',
    data: params,
  });
};
/* 用户列表 */
export const getUserListAPI = (params: {
  start: string | number;
  len: string | number;
}) => {
  return request(`${REQUESTADDRESS}/ram/getUserList`, {
    method: 'get',
    params,
  });
};
/* 添加用户 */
export const addUserAPI = (params: {
  name: string;
  roleIds: string;
  email: string;
}) => {
  return request(`${REQUESTADDRESS}/ram/addUser`, {
    method: 'post',
    data: params,
  });
};
/* 编辑用户 */
export const editUserAPI = (params: {
  uid: string | number;
  name: string;
  roleIds: string;
}) => {
  return request(`${REQUESTADDRESS}/ram/modifyUserRoles`, {
    method: 'post',
    data: params,
  });
};
/* 删除用户 */
export const deleteUserAPI = (params: { uid: string | number }) => {
  return request(`${REQUESTADDRESS}/ram/removeUser`, {
    method: 'post',
    data: params,
  });
};

/* 财务管理 充值明细 */
export const getRechargeDetailAPI = (params: {
  start: string | number;
  len: string | number;
  keyword?: string;
}) => {
  return request(`${REQUESTADDRESS_W}/recharge/service/query`, {
    method: 'get',
    params,
  });
};

/* 首页数据 */
export const getHomeDataAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/instance/getOverview`, {
    method: 'get',
    params,
  });
};

/* 获取开放平台 */
export const getOpenPlatformAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/open/get`, {
    method: 'post',
    params,
  });
};

/* 设置开放平台 */
export const setOpenPlatformAPI = (params: any) => {
  return request(`${REQUESTADDRESS}/open/refresh`, {
    method: 'post',
    params,
  });
};
/*获取仓库地址*/
export const getWarehouseAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/getWarehouseList`, {
    method: 'post',
    params,
  });
};

/* 获取渠道 */
export const getChannelAPI = (params: any) => {
  return request(`${REQUESTADDRESS_W}/truck/getChannelList`, {
    method: 'get',
    params,
  });
};

/* 卡派试算 */
export const getCardEstimateAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/tryCal`, {
    method: 'post',
    data,
    requestType: 'json',
  });
};

/* 保存报价记录 */
export const saveQuoteRecordAPI = (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/savePriceRecord`, {
    method: 'post',
    data,
  });
};
/* 报价记录列表 */
export const getPriceList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/truck/getPriceRecordList`, {
    method: 'get',
    params,
  });
};
/* 报价记录详情 */
export const getPriceDetail = (data: any) => {
  return request(`${REQUESTADDRESS_W}/truck/getPriceRecord`, {
    method: 'post',
    data,
  });
};
/* 修改记录的成功状态 */
export const getRecordSucceeded = (params: any) => {
  return request(`${REQUESTADDRESS_W}/truck/recordSucceeded`, {
    method: 'get',
    params,
  });
};
/* 导出面单 */
export const getDownloadLabelsSheet = (data: any) => {
  return request(`${REQUESTADDRESS_W}/waybill/downloadLabels`, {
    method: 'post',
    data,
    responseType: 'blob',
    getResponse: true,
    requestType: 'json',
  });
};
/* 获取国家列表 */
export const getCountryList = (params: any) => {
  return request(`${REQUESTADDRESS_W}/address/getCountryList`, {
    method: 'GET',
    params,
  });
};
