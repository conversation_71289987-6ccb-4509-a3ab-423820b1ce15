import { Button, message } from 'antd';
import { useRef } from 'react';
import * as XLSX from 'xlsx';
function ExcelReader({ onChange, btnText, disabled, children }: any) {
  const fileInputRef = useRef<any>(null);

  const handleFile = (event: any) => {
    //限制文件类型
    const fileType = event.target.files[0].type;
    if (
      fileType !==
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      message.error('请上传excel文件');
      return;
    }
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.onload = (event: any) => {
      const binaryString = event.target.result;
      const workbook = XLSX.read(binaryString, { type: 'binary' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const newData = XLSX.utils.sheet_to_json(worksheet);
      onChange(newData, file);
      /* 清空数据 */
      fileInputRef.current.value = '';
    };
    reader.readAsBinaryString(file);
  };
  const handleClick = () => {
    fileInputRef.current.click();
  };

  return (
    <div>
      {children ? (
        <div onClick={handleClick}>{children}</div>
      ) : (
        <Button type="link" disabled={disabled} onClick={handleClick}>
          {btnText}
        </Button>
      )}
      <input
        type="file"
        onChange={handleFile}
        ref={fileInputRef}
        style={{ display: 'none' }}
      />
    </div>
  );
}

export default ExcelReader;
