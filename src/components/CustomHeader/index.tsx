import { Affix, Breadcrumb } from 'antd';
import { useState } from 'react';
import GlobalHeaderRight from './components/RightContent';
import styles from './index.less';
import { useLocation } from 'react-router';
import { Link } from '@umijs/max';
import TitleSection from './components/TitleSection';

const breadcrumbNameMap:any = {
  '/home':'概览',
  '/home2':'新概览',
  '/waybillManagement' : '运单管理',
  '/waybillManagement/waybill':'运单查询',
  '/waybillManagement/waybill/details' : '运单详情',
  '/waybillManagement/createWaybill' : '运单创建',
  '/finance' : '财务管理',
  '/finance/recharge' :  '充值明细' ,
  '/finance/statement' : '对账管理' ,
  '/finance/consume' : '消费明细' ,
  '/finance/statement/Details' : '对账管理-详情',
  '/dataManagement' : '资料管理',
  '/dataManagement/address' : '地址库' ,
  '/dataManagement/channelProducts' : '渠道产品',
  '/dataManagement/channelProducts/channelDetails' :'渠道详情',
  '/OpenPlatform' : '开放平台' ,
  '/permission' : '访问控制' ,
  '/permission/role' : '角色管理' ,
  '/permission/user' : '用户管理' ,
  '/logs' : '操作日志' ,
}

const CustomHeader = ({data}:any) => {
  const [top, setTop] = useState(0);
  const  location  = useLocation();
  const pathSnippets = location.pathname.split("/").filter((i) => i);
  const extraBreadcrumbItems = pathSnippets.map((_, index) => {
      const url = `/${pathSnippets.slice(0, index + 1).join("/")}`;
      return (
          <Breadcrumb.Item key={url}>
              <Link to={url}></Link>
              {breadcrumbNameMap[url]}
          </Breadcrumb.Item>
      );
  });
  const breadcrumbItems = [
      <Breadcrumb.Item key="home"><Link to="/">首页</Link></Breadcrumb.Item>
  ].concat(extraBreadcrumbItems);
  return (
    <>
      <Affix offsetTop={top}>
        <div className={styles.CusHeader}>
          <div className="demo">
            {data ? <TitleSection dataList={data.tenant} instance={data.instance}/> : <Breadcrumb className={styles.Breadcrumb}>
              {breadcrumbItems}
            </Breadcrumb>}
          </div>
          <GlobalHeaderRight />
        </div>
      </Affix>
    </>
  );
};
export default CustomHeader;
