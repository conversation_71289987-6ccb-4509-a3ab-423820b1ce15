import styles from './index.less';
import { Typography } from 'antd';
const { Paragraph } = Typography;
const TitleSection = ({ dataList, instance }: any) => {
  return (
    <>
      <div style={{ height: '100%',paddingLeft:15,marginTop:12 }}>
        <div className={styles.wrapText}>
          <span className={styles.title}>{dataList?.fullName || ''}</span>
        </div>
        {/* <span className={styles.rq}>有效期：2023</span> */}
        <Paragraph className={styles.h2Tit} copyable>
          {instance?.id || ''}
        </Paragraph>
      </div>
    </>
  );
};
export default TitleSection;
