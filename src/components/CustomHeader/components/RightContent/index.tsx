import { Space } from 'antd';
import React from 'react';
import { useModel } from 'umi';
import Avatar from './AvatarDropdown';
import styles from './index.less';
export type SiderTheme = 'light' | 'dark';

const GlobalHeaderRight: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  // console.log('initialState: ', initialState);

  if (!initialState || !initialState.settings) {
    return null;
  }

  let className = styles.right;

  return (
    <Space className={className}>
      <Avatar menu />
    </Space>
  );
};

export default GlobalHeaderRight;
