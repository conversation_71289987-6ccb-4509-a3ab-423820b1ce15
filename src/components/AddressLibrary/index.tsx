import React, {useEffect, useRef, useState} from 'react';
import { Button, Modal, Pagination, Space, Tabs, message } from 'antd';
import { CheckCard } from '@ant-design/pro-components';
import service from '@/services/home';
import styles from '../index.less';
import classNames from 'classnames';
import Search from "antd/es/input/Search";
import {getWarehouseAPI} from "@/services/home/<USER>";
// import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
const { getAddressInformationAPI, getAddressLibraryAPI,getSenderAddressAPI } = service.UserHome;
const AddressLibrary = (props: any) => {
  const { onChangeAddressLibrary, btnTitle, channelId, isFBA,isShippingAddress } = props;
  const [page, setPage] = useState(1);
  const [pageSize,setPageSize]=useState(10);
  // console.log('props: ', props);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 被选中的地址信息
  const [selectedAddress, setSelectedAddress] = useState<any>(null);
  //总条数
  const [total, setTotal] = useState<any>(0);
  // 地址列表
  const [addressList, setAddressList] = useState<any>([]);
  const [keyword, setKeyword] = useState<any>();
  const [loading, setLoading] = useState(true);
  const [activeKey, setKey] = useState('1');
  const inputRef = useRef(null);
  /* 获取常用地址库地址 */
  const getAddressInformation = async (page: any, pageSize: any) => {
    setLoading(true);
    try {
      if (activeKey === '1') {
        const { status, data } = await getAddressInformationAPI({
          start: (page - 1) * pageSize,
          len: pageSize,
          keyword:keyword,
        });
        if (status) {
          setTotal(data.amount);
          setAddressList([...data.list]);
          setLoading(false);
        }
      } else if(activeKey === '2') {
        const { status, data } = await getWarehouseAPI({
          start: (page - 1) * pageSize,
          len: pageSize,
          type: 1,
          keyword:keyword,
        });
        if (status) {
          setTotal(data.amount);
          setAddressList([...data.list]);
          setLoading(false);
        }
      }
      if(isShippingAddress){
        const { status, data } = await getSenderAddressAPI({
          start: (page - 1) * pageSize,
          len: pageSize,
          keyword:keyword,
        });
        if (status) {
          setTotal(data.amount);
          setAddressList([...data.list]);
          setLoading(false);
        }
      }
      
      setLoading(false);
    } catch (err) {
      console.log('地址库选择抛出异常: ', err);
      setLoading(false);
    }
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    //判断selectedAddress是否为空
    if (!selectedAddress) {
      return message.error('请选择地址');
    }
    onChangeAddressLibrary({
      ...selectedAddress,
      checked: true,
      channelIdADD: channelId,
    });
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  useEffect(() => {
    if (isModalOpen) {
      //打开的时候获取地址列表从第一页开始
      getAddressInformation(1, pageSize);
    }
  }, [isModalOpen,activeKey,keyword]);

  const items = [
    {
      key: '1',
      label: `常用地址`,
      disabled: isFBA,
      // children: `Content of Tab Pane 1`,
    },
    {
      key: '2',
      label: `FBA地址`,
      // children: `Content of Tab Pane 2`,
    },
  ];
  const onChange = (key: any) => {
    setKey(key);
  };
  useEffect(() => {
    if (isFBA) {
      setKey('2');
    }
  }, [isFBA]);
  return (
    <>
      <div
        onClick={showModal}
        style={{ display: 'inline-block', cursor: 'pointer' }}
      >
        {!btnTitle ? '从地址库选择' : <a>{btnTitle}</a>}
      </div>
      <Modal
        width="740px"
        title={isShippingAddress ===true ? '选择发件地址' : '选择地址'}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={[
          <Space key="btn">
            <Pagination
              key="paginat"
              total={total}
              showSizeChanger
              // showQuickJumper
              current={page}
              // defaultCurrent={1}
              // defaultPageSize={2}
              // pageSizeOptions={[2, 4, 6, 8, 10]}
              showTotal={(total) => `共 ${total} 条`}
              onChange={(page, pageSize) => {
                //存储一下别的页面中用
                setPage(page);
                setPageSize(pageSize);
                getAddressInformation(page, pageSize);
              }}
            />
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={handleOk}>
              确定
            </Button>
          </Space>,
        ]}
      >
        {
          isShippingAddress ===true ? <div style={{marginBottom:22}}></div> :<Tabs activeKey={activeKey} items={items} onChange={onChange} />
        }
        <div style={{ maxHeight: '500px', overflow: 'auto' }}>
          <div style={{marginBottom:'10px'}}>
          <Search
              placeholder="请输入"
              allowClear
              enterButton="搜索"
              size="middle"
              ref={inputRef}
              style={{width:'300px'}}
              onSearch={(value)=>{setKeyword(value)}}
          />
          </div>
          {/* 地址选项列表 */}
          <CheckCard.Group
            value={selectedAddress}
            loading={loading}
            // 选项变化回调
            onChange={(value) => {
              setSelectedAddress(value);
            }}
            className={styles.isNoCheckCard}
          >
            {/* 渲染地址选项 */}
            {addressList.map((item: any,index:any) => (
              <CheckCard
                style={{ height: '102px' }}
                key={index}
                value={item}
                // 地址卡片标题
                title={
                  <div
                    className={classNames(
                      selectedAddress?.id === item.id ? styles.choice : null,
                    )}
                  >
                    <Space>
                      <div>
                        <span>{item.name || item.contactName || item.province}</span>
                        {item.isFBA === 1 ? (
                          <span className={styles.isFBA}>FBA</span>
                        ) : null}
                      </div>
                      <div
                        className={classNames(
                          selectedAddress?.id === item.id
                            ? styles['lable-name']
                            : styles['lable-name-a'],
                        )}
                      >
                        {item.contactPhone}
                      </div>
                      <div
                        className={classNames(
                          selectedAddress?.id === item.id
                            ? styles['lable-name']
                            : styles['lable-name-a'],
                        )}
                      >
                        {item.companyName}
                      </div>
                    </Space>
                  </div>
                }
                // 地址卡片描述
                description={
                  <div style={{ color: '#707070' }}>
                    {item.street},{item.city},{item.provinceShortName},
                    {item.country},{item.zipCode}
                  </div>
                }
                // 地址卡片右侧操作
                // extra={
                //   <Space key="operation">
                //     <EditOutlined style={{ color: '#979797' }} />
                //     <DeleteOutlined style={{ color: '#979797' }} />
                //   </Space>
                // }
              />
            ))}
            {/* </Row> */}
          </CheckCard.Group>
        </div>
      </Modal>
    </>
  );
};

export default React.memo(AddressLibrary);
