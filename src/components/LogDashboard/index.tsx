import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Button, Drawer, List, Space, Tag } from 'antd';
import { useRequest } from 'ahooks';
// import { getLogListAPI } from '@/services/financeApi';
// import { getAvataUrl } from '@/shared/Enumeration';
import dayjs from 'dayjs';
import { getLogListAPI } from '@/services/home/<USER>';
import { getAvataUrl } from '@/utils/secret';

interface LogDashboardProps {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  extraId_1?: string;
  extraId_2?: string;
  holdFlag?: boolean;
  type?: any;
  styles?: any;
  sourceForecast?: boolean;
}

const LogDashboard: React.FC<LogDashboardProps> = ({
  btnText,
  btnType,
  extraId_1,
  extraId_2,
  holdFlag,
  type,
  styles,
  sourceForecast,
}: LogDashboardProps) => {
  const [open, setOpen] = useState(false);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(100);

  const { runAsync, data, loading } = useRequest(getLogListAPI, {
    manual: true,
  });

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const fetchData = () => {
    runAsync({
      extraId_1: extraId_1,
      extraId_2: extraId_2,
      start: (current - 1) * pageSize,
      len: pageSize,
      name: sourceForecast ? 'printTailLabel' : undefined,
    });
  };

  useEffect(() => {
    if (open) {
      fetchData();
    }
  }, [open, current, pageSize]);

  return (
    <>
      {type === '预配舱' ? (
        <a
          onClick={showDrawer}
          style={{ color: holdFlag ? 'rgb(255, 239, 195)' : '#1677ff' }}
        >
          {btnText}
        </a>
      ) : (
        <Button
          // style={{ color: holdFlag ? 'rgb(255, 239, 195)' : '#1677ff' }}
          type={btnType}
          onClick={showDrawer}
          style={styles}
        >
          {btnText}
        </Button>
      )}
      <Drawer width={'45vw'} title="日志记录" onClose={onClose} open={open}>
        <List
          pagination={{
            position: 'bottom',
            current: current,
            pageSize: pageSize,
            total: data?.data?.total || 0,
            onChange: (page, size) => {
              setCurrent(page);
              if (size !== pageSize) {
                setPageSize(size);
              }
            },
            // showSizeChanger: true,
            // showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          size="small"
          dataSource={data?.data?.list}
          loading={loading}
          rowKey="id"
          renderItem={(item: any) => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={getAvataUrl(item?.operatorAvatar)} />}
                title={
                  <Space>
                    <div className="color-#b1b1b1">{item?.operatorName}</div>
                    {item?.hostInstanceId ===
                      localStorage.getItem('instanceId') && (
                      <Tag bordered={false} color="red" className="text-12px">
                        OMS
                      </Tag>
                    )}
                    {item?.serviceModuleDesc && (
                      <Tag
                        bordered={false}
                        color="orange"
                        className="text-12px"
                      >
                        {item?.serviceModuleDesc}
                      </Tag>
                    )}

                    <div className="text-12px color-#b1b1b1">
                      {dayjs(item?.time).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                    <div className="text-12px color-#b1b1b1">IP:{item?.ip}</div>
                  </Space>
                }
                description={
                  <>
                    <div
                      className="color-#414141 m-y-0.5em"
                      style={{ wordBreak: 'break-word' }}
                    >
                      {item?.content}
                    </div>
                  </>
                }
              />
            </List.Item>
          )}
        />
      </Drawer>
    </>
  );
};

export default LogDashboard;
