.tab{
    display: flex;
    background-color: #F6F6F8;
    padding: 0 8px ;
    // padding: 10px 15px 0 15px;
    font-size: 14px;

    :global{
        .anticon-close{
            display: none;
        }
    }
}

.tab-item{
    position: relative;
    background-color: #ececf5;
    padding: 10px 15px;
    border-radius: 12px 12px 0 0;
    cursor: pointer;
    transition: .2s;
    color:#707070;
    font-size: 13px;
}

.tab-item::before,.tab-item::after{
    position: absolute;
    bottom: 0;
    content: '';
    width: 20px;
    height: 20px;
    border-radius: 100%;
    box-shadow: 0 0 0 40px transparent;
    transition: .2s;
}

.tab-item::before{
    left: -20px;
    clip-path: inset(50% -10px 0 50%);
}

.tab-item::after{
    right: -20px;
    clip-path: inset(50% 50% 0 -10px);
}

.tab-item:hover{
    background-color: #fff;

    :global{
        .anticon-close{
            display: inline-block;
        }
    }
}

.tab-item:hover::before,
.tab-item:hover::after{
    box-shadow: 0 0 0 30px #fff;
}

.tab-item.active{
    background-color: #fff;
    z-index: 1;
}

.tab-item.active::before,
.tab-item.active::after{
    box-shadow: 0 0 0 30px #fff;
}

