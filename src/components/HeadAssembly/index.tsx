// import { useEffect, useState } from 'react';
import styles from './index.less';
import RightContent from '@/components/RightContent';
import classNames from 'classnames';
import { ProBreadcrumb } from '@ant-design/pro-components';
// import TabsList from '../TabsList';

const HeadAssembly = () => {
  return (
    <>
      <div className={classNames(styles.warp, styles.acssHead)}>
        <ProBreadcrumb />
        <RightContent />
      </div>
      {/* <div className={styles.acssHead} style={{top:54}}>
        <TabsList />
      </div> */}
    </>
  );
};
export default HeadAssembly;
