import { ReloadOutlined } from '@ant-design/icons';
import {
  SuperTable,
  ListColumn,
  // register,
  CustomComponent,
} from '@xiaoshengwpp/react-super-table';
import { useMount, usePagination, useSize } from 'ahooks';
import { ConfigProvider, message, Pagination, Space, Tooltip } from 'antd';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import ReactDom from 'react-dom/client';
import renderSuperTableColumns from './RenderSuperTableColumns';
import themes from './themes';
import TotalNumberOfPages from './TotalNumberOfPages';
import { ColumnsType, StatItem } from './typeTs';
// import shaixuan from '@/assets/svg/shaixuan.svg';
// import FilterModal from './components/FilterModal';
import { useSnapshot } from 'umi';
import {
  columnStore,
  paramsStore,
  screenStore,
  settingsStore,
  rowHeightStore,
} from './store/screenStore';
import { ProCard } from '@ant-design/pro-components';
import SearchFilters from './components/SearchFilters/index';
// import RoamingGuidance from './components/RoamingGuidance';
// import Loding from './components/Loding/icon';
import Loading from './components/Loding';
import SetUpModal from './components/SetUpModal';
import SaveView from './components/SaveView';
import { manageCheckedData } from './checkingMethod';
import RowHeightSetting from './components/RowHeightSetting';
import SearchForF from './components/SearchForF';

// register.icon('shaixuan', {
//   type: 'svg',
//   svg: shaixuan,
//   width: 18,
//   height: 18,
//   name: 'shaixuan',
//   positionType: 'right',
//   marginRight: 0,
//   hover: {
//     width: 22,
//     height: 22,
//     bgColor: 'rgba(101, 117, 168, 0.1)',
//   },
//   // tooltip: {
//   //   style: { arrowMark: true },
//   //   title: '筛选',
//   //   placement: 'right',
//   // },
//   cursor: 'pointer',
// });

interface SuperTablesRef {
  refresh: () => void;
  reload: () => void;
}

/**
 * @description
 * <AUTHOR>
 * @date 2024-10-12
 * @interface Props
 * @param {('standard' | 'adaptive' | 'autoWidth')} [widthMode] - 宽度模式
 * @param {any[]} [dataSource] - 数据源
 * @param {any} [request] - 请求
 * @param {any} [rowSelection] - 行选择
 * @param {ColumnsType[]} columns - 列配置
 * @param {StatItem[]} [TotalConfiguration] - 总计配置
 * @param {any} [toolBarRender] - 工具栏渲染
 * @param {boolean} [isApiSorter] - 是否开启接口排序
 * @param {boolean} [isFreeze] - 是否开启冻结
 * @param {number} [rightFrozenColCount] - 右侧冻结列数量
 * @param {string} instanceId  - 实例id 必填 命名规则：当前页路由_表格名称*  //TODO: 全局必须唯一
 * @param {any} [filters] - 筛选条件 旧的组件引入  参数和之前一致
 * @param {boolean} [isWarpTabs] - 外部是否有 tabs 适配 tabs 响应式
 * @param {boolean} [enableLineBreak] - 适配换行
 * @param {boolean} [isPageCacheEnabled] - 是否开启分页缓存
 * @param {boolean} [warpHeight] - 高度适配
 * @param {any} [toolBarRenderRight] - 右侧工具栏渲染
 * @param {boolean} [isHidePagination] - 是否隐藏分页
 * @param {boolean} [isHideToolBar] - 是否隐藏工具栏
 * @param {boolean} [ghost] - 是否开启幽灵模式
 * @param {any} [onDBLClickCell] - 双击单元格
 * @param {number} [bottomFrozenRowCount] - 底部冻结行数
 * @param {any} [aggregation] - 聚合配置
 * @param {boolean} [autoQueryEnabled] - 是否开启自动查询
 * @param {boolean} [defaultQueryState] - 默认查询条件展开还是收起
 * @param {boolean} [isTree] - 是否开启树形结构
 * @param {any} [sortState] - 排序配置
 */

interface Props {
  widthMode?: 'standard' | 'adaptive' | 'autoWidth';
  dataSource?: any[];
  request?: any;
  children?: any;
  rowSelection?: any;
  columns: ColumnsType[];
  TotalConfiguration?: StatItem[];
  toolBarRender?: any;
  isApiSorter?: boolean;
  isFreeze?: boolean;
  rightFrozenColCount?: number;
  instanceId: string;
  height?: string | number;
  filters?: any;
  isWarpTabs?: boolean;
  enableLineBreak?: boolean;
  isPageCacheEnabled?: boolean;
  warpHeight?: number;
  toolBarRenderRight?: any;
  isHidePagination?: boolean;
  isHideToolBar?: boolean;
  ghost?: boolean;
  onDBLClickCell?: any;
  bottomFrozenRowCount?: number;
  aggregation?: any;
  autoQueryEnabled?: boolean;
  isAutoFetch?: boolean;
  isTree?: boolean;
  sortState?: any;
  defaultQueryState?: boolean;
}

const SuperTables = forwardRef<SuperTablesRef, Props>(
  (
    {
      children,
      widthMode,
      dataSource,
      request,
      rowSelection,
      columns,
      TotalConfiguration,
      toolBarRender,
      isApiSorter = false,
      isFreeze = true,
      rightFrozenColCount = 1,
      instanceId,
      height,
      filters = {},
      isWarpTabs,
      enableLineBreak = false,
      isPageCacheEnabled = false,
      warpHeight = 0,
      toolBarRenderRight,
      isHideToolBar = false,
      isHidePagination = false,
      ghost = false,
      onDBLClickCell,
      bottomFrozenRowCount = 0,
      aggregation = null,
      autoQueryEnabled,
      isAutoFetch,
      isTree = false,
      sortState,
      defaultQueryState = false,
    },
    ref,
  ) => {
    const RefTable = useRef<any>(null);
    const HEADER_HEIGHT = warpHeight ? warpHeight : isWarpTabs ? 190 + 50 : 190;

    const [messageApi, contextHolder] = message.useMessage();
    const [params, setParams] = useState<any>({});
    const [selectedData, setSelectedData] = useState<any[]>([]);
    const snapScreen = useSnapshot(screenStore);
    const snapParams = useSnapshot(paramsStore);
    const snapSettings = useSnapshot(settingsStore);
    const snapColumnStore = useSnapshot(columnStore);
    const snapRowHeightStore = useSnapshot(rowHeightStore);
    const tableSearchRef = useRef<any>(null);
    const size = useSize(tableSearchRef);
    const [tableHeight, setTableHeight] = useState<string>(
      `calc(100vh - ${HEADER_HEIGHT}px`,
    );
    const [records, setRecords] = useState<any[]>([]);
    const [condition, setCondition] = useState<any>({});
    const refreshRef = useRef<any>(null);
    const settingRef = useRef<any>(null);
    const clickCountRef = useRef(0);
    const [sortStateObj, setSortStateObj] = useState<any>({});
    const [isItTheFirstTime, setIsItTheFirstTime] = useState(false);
    const tableListUUIDDataRef = useRef<any[]>([]);
    const selectedDataRef = useRef<any[]>([]);
    const selectedCellRowRef = useRef<any>(null);
    const [searchVisible, setSearchVisible] = useState(false);
    const resetScreenStore = useCallback(
      (tableInstanceId?: string | Array<string> | undefined) => {
        if (tableInstanceId && typeof tableInstanceId === 'string') {
          screenStore[tableInstanceId] = {};
        }
        if (tableInstanceId && Array.isArray(tableInstanceId)) {
          tableInstanceId.forEach((item: string) => {
            screenStore[item] = {};
          });
        }

        if (!tableInstanceId) {
          screenStore[instanceId] = {};
        }
        setParams({
          ...params,
          columnsFilter: snapScreen[instanceId],
        });
      },
      [instanceId],
    );

    useMount(() => {
      setIsItTheFirstTime(true);
    });
    /* 搜索兼容 增加userSoleId */
    const tableFilter = useMemo(() => {
      if (!filters) return {};
      const newFilters = Object.fromEntries(
        Object.entries(filters).map(([key, value]) => [
          key,
          {
            ...(typeof value === 'object' ? value : { value }),
            userSoleId: key,
          },
        ]),
      );
      setCondition(newFilters);
      return newFilters;
    }, [JSON.stringify(filters), Object?.keys(filters)?.length]);

    const { data, loading, pagination, refresh }: any = usePagination(
      (paginationParams) => {
        if (!request) return false;

        if (
          (!snapSettings[instanceId]?.autoFetch || isAutoFetch === false) &&
          isItTheFirstTime
        ) {
          setIsItTheFirstTime(false);
          return false;
        }

        if (isPageCacheEnabled || snapSettings[instanceId]?.cachePagination) {
          paramsStore[instanceId] = paginationParams;
        }

        if (rowSelection) {
          setSelectedData([]);
          rowSelection([]);
          selectedDataRef.current = [];
        }
        return request({
          condition: { ...condition },
          ...(paginationParams || {
            current: snapParams[instanceId]?.current || pagination.current,
            pageSize: snapParams[instanceId]?.pageSize || pagination.pageSize,
          }),
          // ...paginationParams,
          ...params,
        });
      },
      {
        debounceWait: 600,
        defaultCurrent: snapParams[instanceId]?.current || 1,
        defaultPageSize: snapParams[instanceId]?.pageSize || 100,
        refreshDeps: [params, condition],
        onSuccess: (res: any) => {
          if (res?.success) {
            if (res?.data?.length) {
              if (isTree) {
                const list = res?.data;
                const addTableUUID = (items: any[]) => {
                  const stack: any[] = [...items];
                  let counter = 0;
                  while (stack.length > 0) {
                    const item = stack.pop();
                    counter++;
                    item.tableUUID = `table_${counter}`;
                    if (
                      Array.isArray(item.children) &&
                      item.children.length > 0
                    ) {
                      stack.push(...item.children);
                    }
                  }
                  return items;
                };
                const processedList = addTableUUID(list);
                tableListUUIDDataRef.current = processedList;
              }

              if (rowSelection) {
                const list = res?.data || [];
                const newList = list.filter((record: any) => record.check);
                rowSelection(newList);
                setSelectedData(newList);
              }
            }
            if (records?.length > 0) {
              RefTable.current?.setRecords(res?.data || [], {
                sortState: null,
              });
              if (sortStateObj?.field) {
                RefTable.current?.updateSortState(
                  {
                    field: sortStateObj.field,
                    order: sortStateObj.order || 'normal',
                  },
                  false,
                );
              }
            } else {
              if (
                snapSettings[instanceId]?.cacheSort &&
                isApiSorter === false
                // false //TODO: 先临时关闭
              ) {
                // console.log('执行了'); TODO:   先记录
                RefTable.current?.setRecords([...res?.data]);
              } else {
                setRecords([...res?.data]);
              }
            }

            if (res?.data?.length === 1) {
              setTimeout(() => {
                RefTable.current?.setCellCheckboxState(1, 0, true);
              }, 100);
              setSelectedData([res?.data[0]]);
              if (rowSelection) {
                rowSelection([res?.data[0]]);
              }
            }
          }
        },
        // defaultParams: [
        //   {
        //     current: 1,
        //     pageSize: 100,
        //   },
        // ],
      },
    );

    useImperativeHandle(
      ref,
      () => ({
        refresh,
        reload: refresh,
        tableInstance: { ...RefTable.current },
        resetScreenStore,
      }),
      [refresh],
    );

    const onDropdownMenuClick = useCallback((e: any) => {
      if (e.menuKey === '复制') {
        const value = RefTable.current?.getCopyValue();
        if (!value) {
          // navigator.clipboard.writeText(RefTable.current?.getCellValue(e.col,e.row));
          messageApi.warning('未监听到选中区域,请选择目标后再复制');
          return;
        }
        navigator.clipboard.writeText(value);
        messageApi.success('操作成功');
      }
      if (!rowSelection) return;
      const isKey = isTree ? 0 : 1;
      if (e.menuKey === '选中' || e.menuKey === '取消选中') {
        const { start, end } = selectedCellRowRef?.current || {};
        if (!start || !end) return;
        const targetState = e.menuKey === '选中';
        const startRow = Math.min(start.row, end.row);
        const endRow = Math.max(start.row, end.row);
        for (let row = startRow; row <= endRow; row++) {
          RefTable.current?.setCellCheckboxState(isKey, row, targetState);
        }
      }
    }, []);

    const onSortClick = useCallback((col: any) => {
      // console.log('col: ', col);
      if (isApiSorter === false) {
        return true;
      }
      clickCountRef.current++;
      const sortState =
        col.order === 'normal'
          ? 'asc'
          : col.order === 'asc'
          ? 'desc'
          : 'normal';
      // const sortState = clickCountRef.current % 3 === 1 ? 'asc' : clickCountRef.current % 3 === 2 ? 'desc' : 'normal';
      RefTable.current?.updateSortState(
        {
          field: col.field,
          order: sortState,
        },
        false,
      );

      // 清空其他列的排序状态
      Object.keys(screenStore[instanceId] || {}).forEach((key) => {
        if (screenStore[instanceId][key].sort) {
          screenStore[instanceId][key].sort = '';
        }
      });

      if (Array.isArray(col.field)) {
        col.field = col.field.reduce(
          (result: string, current: string, index: number) => {
            if (index === 0) {
              return current;
            }
            return result + current.charAt(0).toUpperCase() + current.slice(1);
          },
          '',
        );
      }

      const sortValueMap = {
        normal: '',
        asc: 1,
        desc: -1,
      };

      settingsStore[instanceId] = {
        ...settingsStore[instanceId],
        sortState: (sortState as keyof typeof sortValueMap) ?? '',
      };

      screenStore[instanceId] = {
        ...screenStore[instanceId],
        [col.field]: {
          ...screenStore?.[instanceId]?.[col.field],
          sort: sortValueMap[sortState as keyof typeof sortValueMap] ?? '',
        },
      };

      setSortStateObj({
        field: col.field,
        order: sortState,
      });

      return false;
    }, []);

    const renderColumns = useMemo(() => {
      // console.log('columns: 渲染次数');
      if (!snapColumnStore[instanceId]) {
        return renderSuperTableColumns(
          columns,
          instanceId,
          isApiSorter,
          isTree,
        );
      }

      // const hideMap = new Map(
      //   snapColumnStore[instanceId].map((snapCol: any) => [
      //     Array.isArray(snapCol.dataIndex) ? snapCol.dataIndex.join('.') : snapCol.dataIndex,
      //     snapCol.hide
      //   ])
      // );

      // const newSnapColumnStore = snapColumnStore[instanceId]
      // console.log('newSnapColumnStore: ', newSnapColumnStore);

      const newColumns = snapColumnStore[instanceId].reduce(
        //TODO: 这里需要优化
        (acc: any[], snapCol: any) => {
          // 如果该列被隐藏，则跳过
          if (snapCol.hide === true) {
            return acc;
          }

          const snapDataIndex = Array.isArray(snapCol.dataIndex)
            ? snapCol?.dataIndex?.join('.')
            : snapCol?.dataIndex;

          const originalCol = columns.find((col) => {
            const colDataIndex = Array.isArray(col?.dataIndex)
              ? col?.dataIndex?.join('.')
              : col?.dataIndex;
            return colDataIndex === snapDataIndex;
          });

          if (originalCol) {
            acc.push({
              ...originalCol,
              width: snapCol.width,
              // hide: hideMap?.get(snapDataIndex) || false
            });
          }

          return acc;
        },
        [],
      );
      return renderSuperTableColumns(
        newColumns,
        instanceId,
        isApiSorter,
        isTree,
      );
      // }, [columns?.length, instanceId, snapColumnStore[instanceId]]);
    }, [columns?.length, instanceId]);

    useEffect(() => {
      if (
        snapScreen[instanceId] &&
        Object.keys(snapScreen[instanceId]).length > 0
      ) {
        setParams({
          columnsFilter: snapScreen[instanceId],
          // condition: { ...condition },
        });
      }
    }, [snapScreen[instanceId], instanceId]);

    useEffect(() => {
      if (size?.height) {
        const newHeight = `calc(100vh - ${HEADER_HEIGHT}px - ${size.height}px)`;
        setTableHeight(newHeight);
      }
    }, [size?.height]);

    const onCheckboxStateChange = useCallback((e: any) => {
      if (isTree) {
        const newList = manageCheckedData(
          tableListUUIDDataRef.current,
          e,
          selectedDataRef.current,
        );
        selectedDataRef.current = newList;
        if (rowSelection) {
          rowSelection(newList);
          setSelectedData([...newList]);
        }
      } else {
        const checkList = RefTable.current.getCheckboxState('check');
        const list = RefTable.current?.records || [];
        const newList = list.filter((record: any, index: number) => {
          const checkState = checkList[index];
          return checkState === true;
        });
        if (rowSelection) {
          rowSelection(newList);
          setSelectedData(newList);
        }
      }
    }, []);

    const selectedCell = (e: any) => {
      if (!rowSelection) return;
      selectedCellRowRef.current = e?.ranges[0];
      //   // // if (e.ranges?.[0] && !e.ranges?.[0]?.skipBodyMerge) {
      //   // if (e.ranges?.[0]) {
      //   //   const { start, end } = e.ranges[0];

      //   //   // if (start.row === end.row && start.column === end.column) {
      //   //   //   RefTable.current.setCellCheckboxState(0, start.row, false);
      //   //   //   RefTable.current.setCellCheckboxState(0, start.row, true);
      //   //   //   return;
      //   //   // }

      //   //   const startRow = Math.min(start.row, end.row);
      //   //   const endRow = Math.max(start.row, end.row);
      //   //   const firstCellState = RefTable.current.getCellCheckboxState(
      //   //     0,
      //   //     startRow,
      //   //   );
      //   //   const targetState = !firstCellState;

      //   //   for (let row = startRow; row <= endRow; row++) {
      //   //     RefTable.current.setCellCheckboxState(0, row, targetState);
      //   //   }
      //   // } else {
      //   //   const { row } = e;
      //   //   const currentState = RefTable.current.getCellCheckboxState(0, row);
      //   //   RefTable.current.setCellCheckboxState(0, row, !currentState);
      //   // }
    };

    const onCopyData = (e: any) => {
      if (e.copyData) {
        navigator.clipboard.writeText(e.copyData);
        messageApi.success('操作成功');
      }
      // RefTable.current?.stateManager?.setCustomSelectRanges([
      //   {
      //     range: {
      //       start: {
      //         col: e?.cellRange[0]?.start?.col,
      //         row: e?.cellRange[0]?.start?.row,
      //       },
      //       end: {
      //         col: e?.cellRange[0]?.end?.col,
      //         row: e?.cellRange[0]?.end?.row,
      //       },
      //     },
      //     style: {
      //       cellBorderColor: 'blue',
      //       cellBorderLineWidth: 1,
      //       cellBorderLineDash: [5, 5],
      //     },
      //   },
      // ]);
    };

    const onResizeColumnEnd = (e: any) => {
      if (!snapSettings[instanceId]?.cacheColumnWidth) return;
      if (!Array.isArray(snapColumnStore[instanceId])) return;
      const newColWidths = isFreeze
        ? e.colWidths.slice(2)
        : e.colWidths.slice(1);
      // const newColWidths = e.colWidths.slice(2);//TODO: 这里yao优化
      const visibleColumns = snapColumnStore[instanceId].filter(
        (col: any) => col.hide !== true,
      );

      const updatedColumnStore = [...snapColumnStore[instanceId]].map((col) => {
        const visibleIndex = visibleColumns.findIndex((visibleCol: any) => {
          const visibleDataIndex = Array.isArray(visibleCol.dataIndex)
            ? visibleCol.dataIndex.join('.')
            : visibleCol.dataIndex;
          const currentDataIndex = Array.isArray(col.dataIndex)
            ? col.dataIndex.join('.')
            : col.dataIndex;

          return visibleDataIndex === currentDataIndex;
        });

        if (visibleIndex !== -1 && newColWidths[visibleIndex]) {
          return {
            ...col,
            width: newColWidths[visibleIndex],
          };
        }

        return col;
      });

      columnStore[instanceId] = updatedColumnStore;
    };

    // useEffect(() => {
    //   if (RefTable.current) {
    //     document.addEventListener('keydown', (e) => {
    //       if ((e.ctrlKey || e.metaKey) && e.code === 'KeyF') {
    //         e.preventDefault();
    //         setSearchVisible(true);
    //       }
    //     });
    //   }
    //   return () => {
    //     document.removeEventListener('keydown', (e) => {
    //       if ((e.ctrlKey || e.metaKey) && e.code === 'KeyF') {
    //         e.preventDefault();
    //         setSearchVisible(true);
    //       }
    //     });
    //   };
    // }, [RefTable.current]);

    return (
      <div style={{ width: '100%', height: '100%', overflow: 'hidden' }}>
        {contextHolder}
        <SearchForF
          visible={searchVisible}
          onClose={() => setSearchVisible(false)}
          table={RefTable.current}
        />
        <div ref={tableSearchRef}>
          {Object?.keys(tableFilter)?.length > 0 && (
            <SearchFilters
              tableFilter={tableFilter}
              callBack={(e: any) => {
                let newE = e;
                if (Object?.keys(e)?.length === 0) {
                  newE = tableFilter;
                }
                setCondition({ ...newE });
              }}
              defaultQueryState={defaultQueryState}
              autoQueryEnabled={
                autoQueryEnabled
                  ? autoQueryEnabled
                  : snapSettings[instanceId]?.manualSearch
              }
            />
          )}
        </div>
        <ProCard ghost={ghost}>
          {/* <Spin spinning={loading} indicator={<Loding />} tip="正在加载中..."> */}
          {/* </Spin> */}

          <Loading loading={loading} text="加载中">
            <div>
              <div className="flex justify-between mb-10px h-26px">
                <ConfigProvider componentSize="small">
                  {toolBarRender ? toolBarRender() : <Space> </Space>}
                </ConfigProvider>
                {!isHideToolBar ? (
                  <Space className="mr-6px" size={10}>
                    {toolBarRenderRight ? (
                      toolBarRenderRight()
                    ) : (
                      <Space> </Space>
                    )}
                    {/* <RoamingGuidance
                      refs={RefTable}
                      refs2={refreshRef}
                      refs3={settingRef}
                    /> */}
                    {/* <FilterModal columns={columns} /> */}
                    <RowHeightSetting instanceId={instanceId} />
                    <div ref={refreshRef}>
                      <Tooltip title="刷新" placement="bottom">
                        <ReloadOutlined
                          className="hover:text-#1677ff"
                          style={{ fontSize: 16, fontWeight: 500 }}
                          spin={loading}
                          onClick={refresh}
                        />
                      </Tooltip>
                    </div>
                    <div ref={settingRef}>
                      <SetUpModal
                        instanceId={instanceId}
                        isPageCacheEnabled={isPageCacheEnabled}
                        columns={columns}
                      />
                    </div>
                    <SaveView instanceId={instanceId} />
                  </Space>
                ) : (
                  <Space> </Space>
                )}
              </div>
              <div
                style={{
                  transition: 'height 0.3s ease',
                  height: height || tableHeight,
                }}
                // onMouseDown={useCallback((e: any) => {
                //   if (e.button === 0) {
                //     document.body.style.userSelect = 'none';
                //     const handleMouseUp = () => {
                //       document.body.style.userSelect = '';
                //       document.removeEventListener('mouseup', handleMouseUp);
                //     };
                //     document.addEventListener('mouseup', handleMouseUp);
                //   }
                // }, [])}
              >
                <SuperTable
                  onDropdownMenuClick={onDropdownMenuClick}
                  onSortClick={onSortClick}
                  // height={height || tableHeight}
                  onReady={(instance: any, isInitial: boolean) => {
                    if (isInitial) {
                      RefTable.current = instance;
                    }
                  }}
                  onDblClickCell={onDBLClickCell}
                  enableLineBreak={enableLineBreak}
                  widthMode={widthMode}
                  records={dataSource || records || []}
                  autoFillWidth={true}
                  hover={{ highlightMode: 'row' }}
                  dragHeaderMode="column"
                  menu={{
                    contextMenuItems: ['复制', '选中', '取消选中'],
                  }}
                  keyboardOptions={{
                    selectAllOnCtrlA: true,
                    copySelected: true,
                  }}
                  rowSeriesNumber={{
                    title: '行号',
                    width: 'auto',
                    headerStyle: {
                      fontSize: 14,
                      fontWeight: '400',
                      fontFamily: 'PingFang SC, Arial, sans-serif',
                      bgColor: '#fbfbfd',
                      color: '#556c7e',
                    },
                    ...(isTree && {
                      cellType: 'checkbox',
                      enableTreeCheckbox: true,
                      tree: true,
                    }),
                    // style: {
                    //   color: 'red'
                    // }
                    // format: (col: any, row: any, table: any) => {
                    //   if(table?.isAggregation(col,row)){
                    //     return '合计:';
                    //   }else{
                    //     return row;
                    //   }
                    // }
                  }}
                  sortState={sortState}
                  aggregation={aggregation}
                  defaultRowHeight={snapRowHeightStore[instanceId] || 40}
                  columnResizeMode="header"
                  resize={{
                    disableDblclickAutoResizeColWidth: true,
                  }}
                  animationAppear={false}
                  overscrollBehavior="auto"
                  rightFrozenColCount={isFreeze ? rightFrozenColCount : 0}
                  frozenColCount={isFreeze ? 2 : 1}
                  bottomFrozenRowCount={bottomFrozenRowCount}
                  ReactDOM={ReactDom}
                  tooltip={{
                    renderMode: 'html',
                    isShowOverflowTextTooltip: true,
                  }}
                  theme={themes}
                  onCheckboxStateChange={onCheckboxStateChange}
                  select={{
                    outsideClickDeselect: true,
                  }}
                  onSelectedCell={selectedCell}
                  onCopyData={onCopyData}
                  onResizeColumnEnd={onResizeColumnEnd}
                >
                  {rowSelection && !isTree && (
                    <ListColumn
                      field={'check'}
                      headerType="checkbox"
                      cellType="checkbox"
                      width={50}
                      // cellType={(args:any)=>{
                      //   const {table ,row,col} = args
                      //   if(table.isAggregation(col,row)){
                      //     return 'text'
                      //   }else{
                      //     return 'checkbox'
                      //   }
                      // }}
                    />
                  )}
                  {/* <ListColumn
                  title="No"
                  width={62}
                  fieldFormat={(data: any, col: any, row: any) => {
                    console.log(row);
                    return row-1;
                  }}
                /> */}
                  {renderColumns}
                  {children}
                  {/* <CustomComponent  //TODO:这个方案 状态不好维护 先保留 待会看看 
                width="100%"
                height="100%"
                displayMode="cell"
                col={clickCol}
                row={clickRow}
                anchor="bottom-right"
                dx={-35}
                // dy={12}
            >
              <ScreenModal
                isOpen={isScreenModalOpen}
                iconClickItem={iconClickItem}
                onClose={(isOutsideClick) => {
                  if (isOutsideClick) {
                    setClickCol(-1);
                    setClickRow(-1);
                    setIsScreenModalOpen(false);
                  }
                }}
              />
            </CustomComponent> */}
                </SuperTable>
              </div>
              {!isHidePagination && (
                <div className="flex justify-between mt-5px border-1 border-solid rd-3px border-#E1E4E8 p-8px bg-#fbfbfd hover:bg-#F3F8FF select-none">
                  <div className="flex-grow overflow-hidden mr-4">
                    <TotalNumberOfPages
                      rowSelection={rowSelection}
                      selectedData={selectedData}
                      TotalConfiguration={TotalConfiguration}
                      dataSource={dataSource || data?.data || []}
                    />
                  </div>
                  <div className="flex-shrink-0">
                    <Pagination
                      current={dataSource?.length ? 1 : pagination.current}
                      pageSize={
                        dataSource?.length ? 10000 : pagination.pageSize
                      }
                      total={dataSource?.length || data?.total}
                      onChange={pagination.onChange}
                      showSizeChanger
                      size="small"
                      pageSizeOptions={[
                        100, 500, 1000, 2000, 3000, 5000, 10000,
                      ]}
                      showTotal={(total) => `共 ${total} 条`}
                    />
                  </div>
                </div>
              )}
            </div>
          </Loading>
        </ProCard>
      </div>
    );
  },
);

export { CustomComponent };

export default SuperTables;
