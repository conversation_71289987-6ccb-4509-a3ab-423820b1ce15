.cell-red {
  color: #fff;
  background-color: #e34343;
}
// .ag-set-filter-list {
//   display: grid !important;
//   grid-template-columns: repeat(2, 1fr);
//   column-gap: 12px;
// }

// .ag-set-filter-item {
//   padding: 4px 8px;
//   min-height: 32px;
//   white-space: nowrap;
// }

// .ag-center-cols-container {
//   -webkit-transform: translateZ(0);
//   -ms-transform: translateZ(0);
//   transform: translateZ(0);
// }
.wrap-table-super-table-max {
  :global {
    .ag-row-total .ag-selection-checkbox,
    .ag-row-total .ag-row-number {
      display: none !important;
    }

    // .ag-header-cell-menu-button {
    //   display: none !important;
    // }
    // .ag-header-cell-menu-button:hover {
    //   display: block !important;
    // }
  }
}
