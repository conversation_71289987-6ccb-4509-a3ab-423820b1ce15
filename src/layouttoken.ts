export default {
  bgLayout: '#F6F6F8',
  // colorBgAppListIconHover: 'rgba(0,0,0,0.06)',
  // colorTextAppListIconHover: 'rgba(255,255,255,0.95)',
  // colorTextAppListIcon: 'rgba(255,255,255,0.85)',
  sider: {
    colorTextCollapsedButton: '#4071FF',//展开收起按钮 hover 字体颜色
    colorMenuBackground: '#FFFFFF',//menu 的背景颜色
    colorBgMenuItemCollapsedHover: '',//收起 menuItem 的 hover 背景颜色
    colorBgMenuItemCollapsedSelected: '',//收起 menuItem 的选中背景颜色
    colorBgMenuItemCollapsedElevated: '',//收起 menuItem 的弹出菜单背景颜色
    colorMenuItemDivider: '#FFFFFF',//menuItem 分割线的颜色
    colorBgMenuItemHover: '#F6F6F8',//menuItem 的 hover 背景颜色
    colorBgMenuItemSelected: '#F6F6F8',//menuItem 的选中背景颜色
    colorTextMenuSelected: '#00195B',//menuItem 的选中字体颜色
    colorTextMenuItemHover: '#00195B',//menuItem 的 hover 字体颜色
    colorTextMenuActive: '#00195B',//menuItem hover 的选中字体颜色
    colorTextMenu: '#818DAE',//menuItem 的字体颜色
    colorTextMenuSecondary: '',//menu 的二级字体颜色，比如 footer 和 action 的 icon
    paddingInlineLayoutMenu: '',
    paddingBlockLayoutMenu: '',
    colorTextMenuTitle: '#00195B',//sider 的标题字体颜色
    colorTextSubMenuSelected: '',
    colorBgCollapsedButton:'',//展开收起按钮背景颜色
    colorTextCollapsedButtonHover:''//展开收起按钮 hover 时字体颜色
  },
  header: {
    colorBgHeader: '#F6F6F8',//header 的背景颜色
    colorHeaderTitle: '#00195B',//sider 的标题字体颜色
    // colorBgMenuItemHover: '',
    // colorBgMenuItemSelected: '',
    // colorTextMenuSelected: '',//colorTextMenuSelected
    // colorTextMenuActive: '',
    // colorTextMenu: '',//menuItem 的字体颜色
    // colorTextMenuSecondary: '',//menu 的二级字体颜色，比如 footer 和 action 的 icon
    // colorBgRightActionsItemHover: '',
    // colorTextRightActionsItem: '',
    // heightLayoutHeader: '',
  },
  pageContainer:{
    paddingBlockPageContainerContent:'0 20 20 20',
    paddingInlinePageContainerContent:20,
  }
};
