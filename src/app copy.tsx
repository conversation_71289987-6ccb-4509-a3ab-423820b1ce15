// 运行时配置
import type { RunTimeLayoutConfig } from 'umi';
import { history } from 'umi';
// import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import RightContent from './components/RightContent';
import layouttoken from './layouttoken';
import { errorConfig } from './requestErrorConfig';
import { useState } from 'react';
import {  ProBreadcrumb,  } from '@ant-design/pro-components';
import { REQUESTADDRESS } from '@/globalData';
// import services from '@/services/home';
// const { getUserToken } = services.UserHome;
const loginPath = '/403';

// export const render = async (oldRender: any) => {
//   await fetch('https://dev-server-home.kmfba.com/authing/identity?service_id=Waybill&type=json',{mode:'cors',credentials:'include'})
//       .then((res) => res.json())
//       .then((body) => {
//         const { status, data } = body;
//         if(status){
//           localStorage.setItem('user', data.user);
//           localStorage.setItem('accessToken', data.accessToken);
//         }
//       })
//       .catch(() => {
//         console.log('错误');
//       });

//   const isLogin = localStorage.getItem('accessToken');
//   if (!isLogin) {
//     history.push('/403');
//   }
//   history.push('/');
//   oldRender();
// };



// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://next.umijs.org/docs/api/runtime-config#getinitialstat
export async function getInitialState(): Promise<{
  settings?: any;
  currentUser?: any;
  loading?: boolean;
  fetchUserInfo?: () => Promise<any | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      let msg:any = {}; 
      await fetch(`${REQUESTADDRESS}/authing/identity?service_id=Waybill&type=json`,{mode:'cors',credentials:'include'})
      .then((res) => res.json())
      .then((body) => {
        const { status, data } = body;
        if(status){
          localStorage.setItem('user', data.user);
          localStorage.setItem('token', data.accessToken);
          msg.data = data.user
          if(history.location.pathname === '/403'){
            history.push('/');
          }
          // history.push('/');
        }else{
          history.push('/403');
        }
      })
      .catch(() => {
        history.push('/403');
        console.log('错误');
      });
      return msg.data;
    } catch (error) {
      history.push(loginPath);
    }
    return undefined;
  };
  // 
  if (history.location.pathname !== '/404') {
    const currentUser = await fetchUserInfo();
    return {
      fetchUserInfo,
      currentUser,
      settings: { layout: 'mix' },
    };
  }
  return {
    fetchUserInfo,
    settings: { layout: 'mix' },
  };
}
export const layout: RunTimeLayoutConfig | any = ({
initialState,
  // setInitialState,
}: // setInitialState,
any) => {
  // console.log('initialState: ', initialState);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);
  return {
    onPageChange: () => {
      // const { location } = history;
      // 如果没有授权登录，重定向到 无权限页面
      if (!localStorage.getItem('token')) {
        history.push('/403');
      }
    },
    siderWidth: 208,
    title: '鸣锐打单云平台',
    logo: 'https://web-common.kmfba.com/avatars/logo.png',
    layout: 'mix',

    waterMarkProps: {
      content: '浙江扬程国际物流信息科技有限公司',
    },
    rightContentRender: () => <RightContent />,
    menu: {
      locale: false,
    },
    headerContentRender: () => <ProBreadcrumb />,
    collapsed: isCollapsed,
    collapsedButtonRender: (collapsed: any) => {
      return (
        <div
          style={{
            display: 'flex',
            justifyContent: !collapsed ? 'flex-end' : 'center',
            padding: '10px',
            animation: '.8',
            cursor: 'pointer',
          }}
          onClick={() => {
            setIsCollapsed(!isCollapsed);
          }}
        >
          {collapsed ? (
            <MenuUnfoldOutlined style={{ fontSize: 18, color: '#333' }} />
          ) : (
            <MenuFoldOutlined style={{ fontSize: 18, color: '#333' }} />
          )}
        </div>
      );
    },
    menuHeaderRender: undefined,
    unAccessible: <div>unAccessible</div>,
    // childrenRender: (children: any) => {
    //   if (initialState?.loading) return <PageLoading />;
    //   return (
    //     <>
    //       {children}
    //       <SettingDrawer
    //         disableUrlParams
    //         enableDarkTheme
    //         settings={initialState?.settings}
    //         onSettingChange={(settings) => {
    //           setInitialState((preInitialState: any) => ({
    //             ...preInitialState,
    //             settings,
    //           }));
    //         }}
    //       />
    //     </>
    //   );
    // },
    ...initialState?.settings,
    token: layouttoken,
  };
};

/* 乾坤 生命周期钩子 */
export const qiankun = {
  // 应用加载之前
  async bootstrap(props: any) {
    console.log('app1 应用加载之前', props);
  },
  // 应用 render 之前触发
  async mount(props: any) {
    console.log('app1 应用 render 之前触发', props);
  },
  // 应用卸载之后触发
  async unmount(props: any) {
    console.log('app1 应用卸载之后触发', props);
  },
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
};
