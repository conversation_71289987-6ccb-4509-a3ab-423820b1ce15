import { useSnapshot } from 'umi';
import { store } from '../stores';
import { useMemo, useCallback } from 'react';
import { ReactNode } from 'react';

/**
 * 权限项类型定义
 */
type AuthorityItem = string;

/**
 * 权限数据验证器函数类型
 */
type DataValidator = (path: string, data: any) => boolean;

/**
 * 可访问权限类型，可以是字符串或字符串数组
 */
type Accessible = string | string[] | any;

/**
 * 列表项类型
 */
interface ListItem {
  accessible?: Accessible;
  dataValidator?: DataValidator | boolean;
  children?: ListItem[];
  Access?: boolean;
  label?: ReactNode;
  key?: string;
  [key: string]: any;
}


export default function usePermissionFiltering() {
  const snap = useSnapshot(store);
  const currentUser = snap.initialState?.currentUser || {};
  const authorities: AuthorityItem[] = currentUser.authorities || [];
  const authorityData = currentUser.authorityData || {};

  /**
   * 检查是否有访问权限
   * @param accessible 权限标识，可以是字符串或字符串数组
   * @param dataValidator 数据验证器，用于进一步验证权限
   * @returns 是否有访问权限
   */
  const IsAccessible = useCallback(
    (accessible: Accessible, dataValidator?: DataValidator): boolean => {
      if (!accessible) return false;

      const accessibleArr = typeof accessible === 'string'
        ? accessible.split(',')
        : accessible;

      for (const path of accessibleArr) {
        const authDataArr = authorityData[path];
        if (authDataArr && dataValidator) {
          try {
            for (const authData of authDataArr) {
              try {
                if (dataValidator(path, authData)) {
                  return true;
                }
              } catch (err) {
                console.error('权限数据验证失败', { path, authData, error: err });
              }
            }
          } catch (err) {
            console.error('权限数据处理失败', { path, error: err });
          }
        } else {
          if (authorities.some(a => a.indexOf(path) !== -1)) {
            return true;
          }
        }
      }

      return false;
    },
    [authorities, authorityData]
  );

  /**
   * 判断对象是否为数组
   */
  const isArray = useCallback((obj: any): boolean => {
    return Array.isArray(obj);
  }, []);

  /**
   * 处理列表项的子项
   */
  const processChildren = useCallback(
    (item: ListItem, toListFn: (list: ListItem[]) => ListItem[] | false): void => {
      if (
        item.hasOwnProperty('children') &&
        isArray(item.children)
      ) {
        const result = toListFn(item.children as ListItem[]);
        // 只有当结果是数组时才赋值
        if (result !== false) {
          item.children = result;
        }
      }
    },
    [isArray]
  );

  /**
   * 根据权限过滤列表
   * @param list 要过滤的列表
   * @returns 过滤后的列表
   */
  const toList = useCallback(
    (list: any): any => {
      // 弱类型校验，如果不是数组，直接返回原值
      if (!isArray(list)) {
        // 不再报错，而是直接返回原值
        return list;
      }

      try {
        const newList = list.map((item: any) => {
          const newItem: any = { ...item };

          if (newItem.children && isArray(newItem.children)) {
            newItem.children = [...newItem.children];
          }

          if (
            newItem.hasOwnProperty('accessible') &&
            !newItem.hasOwnProperty('dataValidator')
          ) {
            newItem.Access = IsAccessible(newItem.accessible as Accessible, undefined);
            processChildren(newItem, toList);
          }

          if (
            newItem.hasOwnProperty('accessible') &&
            newItem.hasOwnProperty('dataValidator')
          ) {
            if (
              typeof newItem.accessible !== 'string' &&
              !isArray(newItem.accessible)
            ) {
              newItem.Access = false;
              processChildren(newItem, toList);
            } else {
              if (typeof newItem.dataValidator !== 'function') {
                newItem.dataValidator = false;
                processChildren(newItem, toList);
              } else {
                processChildren(newItem, toList);
                newItem.Access = IsAccessible(
                  newItem.accessible as Accessible,
                  newItem.dataValidator as DataValidator
                );
              }
            }
          }

          return newItem;
        });

        const filteredList = newList.filter((item: any) => {
          if (item.hasOwnProperty('Access')) {
            return !!item.Access; // 确保返回布尔值
          }

          // 如果没有 Access 属性，默认为有权限
          item.Access = true;
          return true;
        });

        return filteredList.map((item: any) => {
          const hasSpecialProps = (
            item.hasOwnProperty('Access') ||
            item.hasOwnProperty('dataValidator') ||
            item.hasOwnProperty('accessible')
          );

          if (hasSpecialProps) {
            // 提取需要处理的属性
            const { Access, dataValidator, accessible, ...rest } = item;
            const newItem = { ...rest };

            // 如果有 Access 属性，将其设置为不可枚举属性
            if (item.hasOwnProperty('Access')) {
              Object.defineProperty(newItem, 'Access', {
                value: Access,
                enumerable: false, // 不可枚举，避免传递给 DOM
                configurable: true,
                writable: true
              });
            }

            // 如果有 dataValidator 属性，将其设置为不可枚举属性
            if (item.hasOwnProperty('dataValidator')) {
              Object.defineProperty(newItem, 'dataValidator', {
                value: dataValidator,
                enumerable: false, // 不可枚举，避免传递给 DOM
                configurable: true,
                writable: true
              });
            }

            // 如果有 accessible 属性，将其设置为不可枚举属性
            if (item.hasOwnProperty('accessible')) {
              Object.defineProperty(newItem, 'accessible', {
                value: accessible,
                enumerable: false, // 不可枚举，避免传递给 DOM
                configurable: true,
                writable: true
              });
            }

            return newItem;
          }

          return item;
        });
      } catch (err) {
        console.error('过滤权限数据出错', { error: err, list });
        return list;
      }
    },
    [IsAccessible, isArray, processChildren]
  );

  return useMemo(() => [toList, IsAccessible], [toList, IsAccessible]);
}
