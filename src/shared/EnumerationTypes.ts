//服务商type
// export const  ProviderType  = {
//   1:'国际快递打单渠道商',
//   2:'尾程卡派',
//   4:'TMS尾程快递',
//   8:'货贷/同行',
//   16:'商业快递',
//   32:'航司代理',
//   64:'船司代理',
//   128:'航/船司代理',
//   256:'拖车代理',
//   512:'报关行',
//   1024:'清关行',
//   2048:'客户',
//   0:'未知'
// }

// export const proViderTypesEnum = {
//   // AbroadTailExpress: {
//   //   name: 'AbroadTailExpress',
//   //   type: 5,
//   //   code: 1,
//   //   desc: '国际快递打单渠道商',
//   // },
//   Truck: { name: 'Truck', type: 10, code: 2, desc: '尾程卡派' },
//   TailChannel: { name: 'TailChannel', type: 20, code: 1, desc: 'TMS尾程快递' },
//   FreightForwarder: {
//     name: 'FreightForwarder',
//     type: 8,
//     code: 8,
//     desc: '货代/同行',
//   },
//   // Express: { name: 'Express', type: 40, code: 16, desc: '商业快递' },
//   AirTransport: { name: 'AirTransport', type: 50, code: 32, desc: '航司代理' },
//   ShipTransport: {
//     name: 'ShipTransport',
//     type: 60,
//     code: 64,
//     desc: '船司代理',
//   },
//   Transport: { name: 'Transport', type: 55, code: 96, desc: '航/船司代理' },
//   Trailer: { name: 'Trailer', type: 70, code: 128, desc: '拖车代理' },
//   Declaration: { name: 'Declaration', type: 80, code: 256, desc: '报关行' },
//   Clearance: { name: 'Clearance', type: 90, code: 512, desc: '清关行' },
// };

export enum ProviderTypeEnum {
  '国际快递打单渠道商' = 1,
  '尾程卡派' = 2,
  'TMS尾程快递' = 4,
  '货贷/同行' = 8,
  '商业快递' = 16,
  '航司代理' = 32,
  '船司代理' = 64,
  '航/船司代理' = 128,
  '拖车代理' = 256,
  '报关行' = 512,
  '清关行' = 1024,
  '客户' = 2048,
  '未知' = 0,
}

/* 分票规则枚举 */
export const SubTicketTypes: any = {
  10: {
    text: '按件数',
    color: '#7bed9f',
  },
  20: {
    text: '按重量',
    color: '#ff9ff3',
  },
};

/* 重量传输规则 重量类型 */
export const WeightTypes: any = {
  10: {
    text: '收货实重',
    color: '#7bed9f',
  },
  20: {
    text: '收货材积重',
    color: '#70a1ff',
  },
  30: {
    text: '收货计费重',
    color: '#fd79a8',
  },
  40: {
    text: '出货实重',
    color: '#a29bfe',
  },
  50: {
    text: '出货材积重',
    color: '#22a6b3',
  },
  60: {
    text: '出货计费重',
    color: '#4cd137',
  },
};

/* 代理商/服务商 数据 
enum CounterpartyType {
        Declaration("报关"),
        Clearance("清关"),
        Trailer("拖车"),
        AirTransport("空运"),
        ShipTransport("海运"),
        TailChannel("尾程渠道"),
        TransChannel("卖货应付"),
        AbroadOperate("后端操作"),
        ContainerLoading("装柜"),
        Truck("卡派"),
        Instance("实例");
*/
//应付用这个
export const CounterpartyType: any = [
  {
    value: 'Declaration',
    label: '报关',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|Declaration/.test(data.payableType.value);
    },
  },
  {
    value: 'Clearance',
    label: '清关',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|Clearance/.test(data.payableType.value);
    },
  },
  {
    value: 'Trailer',
    label: '拖车',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|Trailer/.test(data.payableType.value);
    },
  },
  {
    value: 'AirTransport',
    label: '空运',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|AirTransport/.test(data.payableType.value);
    },
  },
  {
    value: 'ShipTransport',
    label: '海运',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|ShipTransport/.test(data.payableType.value);
    },
  },
  {
    value: 'TailChannel',
    label: '尾程渠道',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|TailChannel/.test(data.payableType.value);
    },
  },
  {
    value: 'FreightForwarder',
    label: '卖货应付',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|FreightForwarder/.test(data.payableType.value);
    },
  },
  {
    value: 'AbroadOperate',
    label: '后端应付',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|AbroadOperate/.test(data.payableType.value);
    },
  },
  {
    value: 'ContainerLoading',
    label: '装柜应付',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|ContainerLoading/.test(data.payableType.value);
    },
  },
  {
    value: 'Truck',
    label: '卡派应付',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|Truck/.test(data.payableType.value);
    },
  },
];
//对账管理用这个
export const CounterpartyType1: any = [
  {
    value: 'Declaration',
    label: '报关',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|Declaration/.test(data.payableType.value);
    },
  },
  {
    value: 'Clearance',
    label: '清关',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|Clearance/.test(data.payableType.value);
    },
  },
  {
    value: 'Trailer',
    label: '拖车',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|Trailer/.test(data.payableType.value);
    },
  },
  {
    value: 'AirTransport',
    label: '空运',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|AirTransport/.test(data.payableType.value);
    },
  },
  {
    value: 'ShipTransport',
    label: '海运',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|ShipTransport/.test(data.payableType.value);
    },
  },
  {
    value: 'TailChannel',
    label: '尾程渠道',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|TailChannel/.test(data.payableType.value);
    },
  },
  {
    value: 'FreightForwarder',
    label: '卖货应付',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|FreightForwarder/.test(data.payableType.value);
    },
  },
  {
    value: 'AbroadOperate',
    label: '后端应付',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|AbroadOperate/.test(data.payableType.value);
    },
  },
  {
    value: 'ContainerLoading',
    label: '装柜应付',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|ContainerLoading/.test(data.payableType.value);
    },
  },
  {
    value: 'Truck',
    label: '卡派应付',
    isLeaf: false,
    accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
    dataValidator: (path: any, data: any) => {
      return /all|Truck/.test(data.payableType.value);
    },
  },
];

/* 结算状态 
NOT_WRITEOFF_OR_CHECK(0, "未结清/未核对"),
        WAIT_APPROVE(1, "待审核"),
        WAIT_PAY(2, "待支付"),
        CHECKED(5, "已核对"),
        PARTLY_WRITEOFF(10, "部分核销"),
        WRITE_OFF(20, "已结清");
*/
export const CostType: any = [
  { value: 0, label: '未结清/未核对', key: 'NOT_WRITEOFF_OR_CHECK' },
  { value: 5, label: '已核对', key: 'CHECKED' },
  { value: 1, label: '待审核', key: 'WAIT_APPROVE' },
  { value: 2, label: '待支付', key: 'WAIT_PAY' },
  { value: 10, label: '部分核销', key: 'PARTLY_WRITEOFF' },
  { value: 20, label: '已结清', key: 'WRITE_OFF' },
];

/* 币种 */
export const CurrencyType: any = [
  { value: 10, label: '人民币', key: 'CNY' },
  { value: 20, label: '美元', key: 'USD' },
  { value: 30, label: '加币', key: 'CAD' },
  { value: 40, label: '欧元', key: 'EUR' },
  { value: 50, label: '日元', key: 'JPY' },
];

/* 帐目类型
 */
export const CostSubjectType: any = [
  {
    label: '服务费',
    options: [
      {
        value: 'DeclarationDeputed',
        label: '非单独报关费',
        key: 'DeclarationDeputed',
      },
      {
        value: 'DeclarationMerged',
        label: '合并报关费',
        key: 'DeclarationMerged',
      },
      {
        value: 'DeclarationSingle',
        label: '单独报关费',
        key: 'DeclarationSingle',
      },
      { value: 'PickUp', label: '上门取件费', key: 'PickUp' },
      { value: 'SelfSend', label: '自行寄送费', key: 'SelfSend' },
      {
        value: 'MagneticInspection',
        label: '磁检费',
        key: 'MagneticInspection1',
      },
      { value: 'WarehouseCost', label: '仓库操作费', key: 'WarehouseCost' },
    ],
  },
  {
    label: '附加费',
    options: [
      {
        value: 'ExtraLongAndWeightFee',
        label: '超长超重费',
        key: 'ExtraLongAndWeightFee',
      },
      {
        value: 'RemoteAddressFee',
        label: '偏远地址费',
        key: 'RemoteAddressFee',
      },
      { value: 'ResidentialFee', label: '私人住宅费', key: 'ResidentialFee' },
      { value: 'AbnormalFee', label: '异形费', key: 'AbnormalFee' },
      { value: 'Declaration', label: '报关费', key: 'Declaration' },
      {
        value: 'MagneticInspection',
        label: '磁检费',
        key: 'MagneticInspection',
      },
      { value: 'Others', label: '其他', key: 'Others' },
      {
        value: 'WarehouseInspectionPenalty',
        label: '仓库验货罚款',
        key: 'WarehouseInspectionPenalty',
      },
      { value: 'NonFBA', label: '非FBA地址费', key: 'NonFBA' },
      {
        value: 'RemoteRegionAddressFee',
        label: '区间偏远费',
        key: 'RemoteRegionAddressFee',
      },
      { value: 'PeakSeason', label: '旺季附加费', key: 'PeakSeason' },
      {
        value: 'ClearanceCommission',
        label: '清关手续费',
        key: 'ClearanceCommission',
      },
    ],
  },
  {
    label: '运费',
    options: [
      { value: 'BasicFee', label: '基础运费', key: 'BasicFee' },
      { value: 'Discount', label: '运费优惠', key: 'Discount' },
    ],
  },
  {
    label: '燃油费',
    options: [{ value: 'FuelFee', label: '燃油费', key: 'FuelFee' }],
  },
];
