/* 币种 */
const currencyType: any = {
  10: '人民币',
  20: '美元',
  30: '加币',
  40: '欧元',
  50: '日元',
};

const accType: any = {
  10: '银行账号',
  20: '微信账号',
  30: '支付宝账号',
};
// 清提拆派状态
export const forecasterState: any = {
  '-3': {
    text: '失败',
    color: 'magenta',
  },

  '-2': {
    text: '异常：',
    color: 'red',
  },

  '-1': {
    text: '正在预报',
    color: 'volcano',
  },

  0: {
    text: '预报',
    color: 'orange',
  },

  '10': {
    text: '已入库',
    color: 'gold',
  },

  '20': {
    text: '已打托',
    color: 'lime',
  },

  '30': {
    text: '已上架',
    color: 'green',
  },

  '40': {
    text: '待装车',
    color: 'cyan',
  },

  '50': {
    text: '已出库',
    color: 'blue',
  },

  '60': {
    text: '部分送达',
    color: 'geekblue',
  },

  '70': {
    text: '已妥投',
    color: '#f50',
  },

  '300': {
    text: '拒收',
    color: '#2db7f5',
  },
};
/* 运单状态 */
const WaybillStateEnmu: any = {
  0: {
    text: '草稿',
    color: 'magenta',
  },
  10: {
    text: '待签入',
    color: 'red',
  },
  20: {
    text: '已签入',
    color: 'volcano',
  },
  40: {
    text: '已确认',
    color: 'orange',
  },
  60: {
    text: '已配舱',
    color: 'gold',
  },
  90: {
    text: '已签出',
    color: 'green',
  },
  200: {
    text: '运输中',
    color: 'cyan',
  },
  300: {
    text: '已签收',
    color: 'blue',
  },
  500: {
    text: '已结束',
    color: 'geekblue',
  },
};

const spaceStateType: any = {
  1: {
    text: '空闲',
    color: '#dfe4ea',
  },
  10: {
    text: '待出库',
    color: '#ff9f43',
  },
  20: {
    text: '已提柜',
    color: '#ff6b6b',
  },
  30: {
    text: '已出库',
    color: '#48dbfb',
  },
  40: {
    text: '已进港',
    color: '#1dd1a1',
  },
  50: {
    text: '已开船',
    color: '#3742fa',
  },
  60: {
    text: '到达目的港',
    color: '#2ed573',
  },
  70: {
    text: '清关查验',
    color: '#ff6348',
  },
  80: {
    text: '海外提柜',
    color: '#8e44ad',
  },
  90: {
    text: '已还柜',
    color: '#00b894',
  },
  '-1': {
    text: '已转卖',
    color: '#ff4757',
  },
  '-2': {
    text: '已退订',
    color: '#ff4757',
  },
  0: {
    text: '未知',
    color: '#95a5a6',
  },
};
/* 头像链接 */
const AVATA_URL = 'https://static.kmfba.com/'; // gravatar

/* 根据url字段 判断是否有http前缀 如果有 那么直接返回 如果没有那么拼接AVATA_URL */
const getAvataUrl = (url: string) => {
  if (url?.includes('http')) {
    return url;
  }
  return AVATA_URL + url;
};

const oneMonthAgo = () => {
  const date = new Date();
  date.setMonth(date.getMonth() - 1);
  date.setHours(0);
  date.setMinutes(0);
  date.setSeconds(0);
  date.setMilliseconds(0);
  return date;
};

export {
  currencyType,
  accType,
  AVATA_URL,
  getAvataUrl,
  WaybillStateEnmu,
  oneMonthAgo,
  spaceStateType,
};
