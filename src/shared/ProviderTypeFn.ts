/**
 * @服务商类型 根据要求 后面服务商类型需要根据此方法获取
 * reuturns {ProviderType}
 */
/* 静态映射 */
// export const proViderTypesEnum = {
//   AbroadTailExpress:{name: 'AbroadTailExpress',type: 5,code: 1,desc: '国际快递打单渠道商'},
//   TailTruck:{name: 'TailTruck',type: 10,code: 2,desc: '尾程卡派'},
//   TailExpress:{name: 'TailExpress',type: 20,code: 4,desc: 'TMS尾程快递'},
//   FreightForwarder:{name: 'FreightForwarder',type: 8,code: 8,desc: '货代/同行'},
//   Express:{name: 'Express',type: 40,code: 16,desc: '商业快递'},
//   AirTransport:{name: 'AirTransport',type: 50,code: 32,desc: '航司代理'},
//   ShipTransport:{name: 'ShipTransport',type: 60,code: 64,desc: '船司代理'},
//   // Transport:{name: 'Transport',type: 55,code: 96,desc: '航/船司代理'},
//   Trailer:{name: 'Trailer',type: 70,code: 128,desc: '拖车代理'},
//   Declaration:{name: 'Declaration',type: 80,code: 256,desc: '报关行'},
//   Clearance:{name: 'Clearance',type: 90,code: 512,desc: '清关行'},
// }

let pTypes: ProviderType[];
export class ProviderType {
  private type: number;
  private desc: string;
  private name: string;
  private code: number;
  private constructor(name: string, type: number, code: number, desc: string) {
    this.name = name;
    this.type = type;
    this.desc = desc;
    this.code = code;
  }
  getName() {
    return this.name;
  }
  getType() {
    return this.type;
  }
  getCode() {
    return this.type;
  }
  getDesc() {
    return this.desc;
  }
  // is(type: number): boolean {
  //   return this.code === type || (this.type & type) === this.type;
  // }
  is(type: number): boolean {
    return (this.type & type) === this.type;
  }
  static generate(...type: ProviderType[]): ProviderType {
    const ProType = new ProviderType('', 0, 0, '');
    type?.forEach((item) => {
      ProType.type |= item.type;
      ProType.desc += ' ' + item.desc;
    });
    return ProType;
    // return pTypes.filter(a=>{
    //   return a.is(type)
    // })
  }

  static convert(type: number) {
    // let result = pTypes.find(item => item.code===type);

    // if(!result)
    // result = pTypes.find(item=>item.is(type))

    // return result || null;
    return pTypes.filter((a) => {
      return a.is(type);
    });
  }

  static get(type: number): ProviderType | null {
    let result = pTypes.find((item) => item.code === type);

    if (!result) result = pTypes.find((item) => item.is(type));

    return result || null;
  }
  static generateDesc(type: number) {
    let arr: any = [];
    ProviderTypes.filter((ele: any) => ele.getType() !== -1).forEach((a) => {
      if ((a.getType() & type) != 0) {
        arr.push(a.getDesc());
      }
    });
    return arr.join('，');
  }
  // static AbroadTailExpress = new ProviderType("AbroadTailExpress",1,5,"国际快递打单");
  // static TailTruck = new ProviderType("TailTruck",2,10,"尾程卡派");
  // static TailExpress = new ProviderType("TailExpress",4,20,"TMS尾程快递");
  // static FreightForwarder = new ProviderType("FreightForwarder",8,30,"货代/同行");
  // static Express = new ProviderType("Express",16,40,"商业快递");
  // static AirTransport = new ProviderType("AirTransport",32,50,"空运");
  // static ShipTransport = new ProviderType("ShipTransport",64,60,"海运");
  // static Trailer = new ProviderType("Trailer",128,70,"拖车");
  // static Declaration = new ProviderType("Declaration",256,80,"报关");
  // static Clearance = new ProviderType("Clearance",512,90,"清关");
  // static AbroadOperate = new ProviderType("AbroadOperate",2048,110,"后端操作");
  // static ContainerLoading = new ProviderType("ContainerLoading",4096,120,"国内装柜");
  // static Logistics= new ProviderType('Logistics',8192,130,"国内物流")
  // static CPDD = new ProviderType("CPDD", 16384, 140, "清提拆派");
  // static AbroadTailExpress = new ProviderType("AbroadTailExpress",1,5,"国际快递打单");
  // static TailTruck = new ProviderType("TailTruck",2,10,"尾程卡派");
  // static TailExpress = new ProviderType("TailExpress",4,20,"TMS尾程快递");

  static FreightForwarder = new ProviderType('FreightForwarder', 8, 8, '货代');
  static AirTransport = new ProviderType('AirTransport', 32, 32, '空运');
  static ShipTransport = new ProviderType('ShipTransport', 64, 64, '海运');
  static Trailer = new ProviderType('Trailer', 128, 128, '拖车');
  static Declaration = new ProviderType('Declaration', 256, 256, '报关');
  static Clearance = new ProviderType('Clearance', 512, 512, '清关');
  static AbroadOperate = new ProviderType(
    'AbroadOperate',
    2048,
    2048,
    '后端操作',
  );
  static ContainerLoading = new ProviderType(
    'ContainerLoading',
    4096,
    4096,
    '国内装柜',
  );
  static Logistics = new ProviderType('Logistics', 8192, 8192, '国内物流');
  static CPDD = new ProviderType('CPDD', 16384, 16384, '清提拆派');
  static DropShipping = new ProviderType(
    'DropShipping',
    32768,
    32768,
    '一件代发',
  );
  static Picking = new ProviderType('Picking', 65536, 65536, '码头提柜');
  static Truck = new ProviderType('Truck', 2, 2, '卡派');
  static Unknown = new ProviderType('Unknown', -1, -1, '未知类型');
  static TailChannel = new ProviderType('TailChannel', 1, 1, '尾程渠道');
  static Material = new ProviderType('Material', 262144, 262144, '物料采购');
  static Responsibility = new ProviderType(
    'Responsibility',
    131072,
    131072,
    '员工责任',
  );
  static Insurance = new ProviderType('Insurance', 1048576, 1048576, '保险');
}
pTypes = [
  // ProviderType.AbroadTailExpress,
  // ProviderType.TailTruck,
  // ProviderType.TailExpress,
  // ProviderType.FreightForwarder,
  // ProviderType.Express,
  // ProviderType.AirTransport,
  // ProviderType.ShipTransport,
  // ProviderType.Trailer,
  // ProviderType.Declaration,
  // ProviderType.Clearance,
  // ProviderType.AbroadOperate,
  // ProviderType.ContainerLoading,
  // ProviderType.Logistics,
  // ProviderType.CPDD
  ProviderType.Declaration,
  ProviderType.Clearance,
  ProviderType.Trailer,
  ProviderType.AirTransport,
  ProviderType.ShipTransport,
  ProviderType.TailChannel,
  ProviderType.Material,
  ProviderType.Responsibility,
  ProviderType.Insurance,
  ProviderType.FreightForwarder,
  ProviderType.CPDD,
  ProviderType.DropShipping,
  ProviderType.Picking,
  ProviderType.AbroadOperate,
  ProviderType.ContainerLoading,
  ProviderType.Logistics,
  ProviderType.Truck,
  ProviderType.Unknown,
];
export const ProviderTypes = pTypes;
