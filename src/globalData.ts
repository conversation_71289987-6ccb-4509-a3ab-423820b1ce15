
// export const REQUESTADDRESS = 'https://dev-server-home.kmfba.com';

// export const REQUESTADDRESS_W = 'https://dev-server-waybill.kmfba.com';

// export const REQUESTADDRESS_UPLOAD = 'https://dev-server-waybill.kmfba.com/reconciliation/upload';

const { REACT_APP_ENV } = process.env;
let REQUESTADDRESS = '';
let REQUESTADDRESS_W = '';
let REQUESTADDRESS_UPLOAD = '';

if (REACT_APP_ENV === 'dev') {
  REQUESTADDRESS = 'https://dev-server-home.kmfba.com';
  REQUESTADDRESS_W = 'https://dev-server-waybill.kmfba.com';
  REQUESTADDRESS_UPLOAD = 'https://dev-server-waybill.kmfba.com/reconciliation/upload';
} else if (REACT_APP_ENV === 'pre') {
  REQUESTADDRESS = 'https://staging-server-home.kmfba.com';
  REQUESTADDRESS_W = 'https://staging-server-waybill.kmfba.com';
  REQUESTADDRESS_UPLOAD = 'https://staging-server-waybill.kmfba.com/reconciliation/upload';
} else if (REACT_APP_ENV === 'production') {
  REQUESTADDRESS = 'https://server-home.kmfba.com';
  REQUESTADDRESS_W = 'https://server-waybill.kmfba.com';
  REQUESTADDRESS_UPLOAD = 'https://server-waybill.kmfba.com/reconciliation/upload';
}

const TemporaryUrl = 'https://server-waybill.kmfba.com';

export { REQUESTADDRESS, REQUESTADDRESS_W, REQUESTADDRESS_UPLOAD,TemporaryUrl };