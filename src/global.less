
@import './styles/index.less';

html,
body,
#root {
  margin: 0;
  height: 100%;
}

* {
  margin: 0;
}
/* stylelint-disable-next-line rule-empty-line-before */
.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
/* stylelint-disable-next-line rule-empty-line-before */
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

// Compatible with IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}




// antd 

.ant-btn-primar {
  background-color: #4071FF;
}

.ant-btn.ant-btn-lg {
  border-radius: 4px ;
}

//调整 layout-content 区域的样式 比如内边距等
// .ant-pro-layout-content {
//   padding-inline:12px !important;
//   padding-block:12px !important;
// }
// //调整pageContainer容器  比如内边距等
// .ant-pro-page-container-children-content{
//   padding-inline:0 !important;
//   padding-block:0 !important;
// }

//调整线条
.ant-layout-sider-children{
  border-inline-end: none !important;
}

// .ant-pro-layout-header{
//   border-block-end: none !important;
// }

