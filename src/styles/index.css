.ant-layout-sider {
  border-radius: 0 40px 40px 0;
  padding: 8px;
  padding-right: 0;
}
.ant-menu {
  margin-left: 4px;
  padding-top: 10px;
}
.ant-menu .anticon {
  line-height: 50px !important;
}
.ant-menu-item {
  height: 50px !important;
  border-radius: 40px 0 0 40px !important;
  overflow: unset !important;
  transition: 0.2s !important;
  box-sizing: border-box !important;
}
.ant-layout-sider-children {
  border-radius: 0 40px 40px 0;
}
.ant-layout-sider-children ::-webkit-scrollbar {
  display: none;
}
.ant-menu-item-selected {
  border-radius: 100px 0 0 100px !important;
}
.ant-menu-item-active {
  border-radius: 100px 0 0 100px !important;
  background: #F6F6F8 !important;
}
.ant-menu-submenu-title {
  height: 50px !important;
  border-radius: 100px 0 0 100px !important;
}
.ant-menu-submenu-title:hover {
  background: #F6F6F8 !important;
}
.ant-menu-item::before,
.ant-menu-item::after {
  position: absolute !important;
  bottom: 0 !important;
  content: "" !important;
  width: 40px !important;
  height: 40px !important;
  display: block;
  border-radius: 100% !important;
  box-shadow: 0 0 0 40px transparent !important;
  transition: 0.2s !important;
}
.ant-menu-item::before {
  clip-path: inset(16px 0% 0% 50%);
  right: 0;
  top: -40px !important;
}
.ant-menu-item::after {
  right: 0 !important;
  clip-path: inset(0 0 50% 50%) !important;
  top: 50px !important;
  content: "" !important;
}
.item-menu-item:hover::before,
.item-menu-item:hover::after {
  box-shadow: 0 0 0 40px #F6F6F8 !important;
}
.ant-menu-item.ant-menu-item-active::before,
.ant-menu-item.ant-menu-item-active::after {
  box-shadow: 0 0 0 40px #F6F6F8 !important;
}
.ant-menu-item.ant-menu-item-selected::before,
.ant-menu-item.ant-menu-item-selected::after {
  box-shadow: 0 0 0 40px #F6F6F8 !important;
}
.ant-menu-title-content {
  transition: 0.2s !important;
}
.ant-menu-title-content::after {
  position: absolute !important;
  right: 0 !important;
  top: 50px !important;
  content: "";
  width: 40px ;
  height: 40px ;
  display: block;
  border-radius: 100% ;
  clip-path: inset(0 0 50% 50%) !important;
  box-shadow: 0 0 0 40px transparent ;
  transition: 0.2s;
}
.ant-menu-title-content:hover::after {
  box-shadow: 0 0 0 40px #F6F6F8 !important;
}
/* 设置滚动条的宽度、高度和背景色 */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #F5F5F5;
}
/* 滚动条轨道（包括滚动条上下按钮）的背景颜色 */
::-webkit-scrollbar-track {
  display: none;
}
/* 滚动条上、下按钮的背景颜色 */
/* 滚动条滑块的背景颜色 */
::-webkit-scrollbar-thumb {
  background-color: #BDBDBD;
  border-radius: 3px;
}
/* 鼠标悬停于滚动条上时的滑块背景颜色 */
::-webkit-scrollbar-thumb:hover {
  background-color: #9E9E9E;
}
.ant-layout-sider-children:hover ::-webkit-scrollbar {
  transform: 0.5s;
  display: inline-block;
}
