import { defineConfig, presetAttributify, presetUno } from 'unocss';

export function createConfig({ strict = true, dev = true } = {}) {
  return defineConfig({
    envMode: dev ? 'dev' : 'build',
    presets: [presetAttributify({ strict }), presetUno()],
    rules: [
      ['shadow-custom1', { 'box-shadow': '0px 8px 10px 0px #E5ECEF' }],
      ['shadow-custom2', { 'box-shadow': ' 0px 2px 10px 0px rgba(0,0,0,0.1)' }],
    ],
  });
}

export default createConfig();
