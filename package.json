{"name": "waybill-front", "private": true, "author": "", "description": "", "scripts": {"build": "cross-env REACT_APP_ENV=dev UMI_ENV=dev max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky install", "setup": "max setup", "start": "npm run dev", "test": "echo idiot, <PERSON><PERSON> to Run", "build:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=pre max build", "build:pro": "cross-env REACT_APP_ENV=production UMI_ENV=production max build"}, "dependencies": {"@ag-grid-community/locale": "^33.3.0", "ag-grid-enterprise": "^33.3.0", "@xiaoshengwpp/react-super-table": "0.2.5", "ag-grid-react": "^33.3.0", "@ant-design/icons": "^4.7.0", "@ant-design/pro-components": "^2.0.1", "@antv/g2plot": "^2.4.25", "@umijs/max": "^4.0.42", "ahooks": "^3.7.6", "antd": "^5.0.0", "antd-img-crop": "^4.9.0", "bizcharts": "^4.1.22", "classnames": "^2.3.2", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "echarts": "4.9.0", "file-saver": "^2.0.5", "github-markdown-css": "^5.2.0", "jsonp": "^0.2.1", "lodash": "^4.17.21", "rc-resize-observer": "^1.2.1", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "xlsx": "^0.18.5", "unocss": "^0.53.4"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "husky": "^8.0.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "prettier-plugin-organize-imports": "^2", "prettier-plugin-packagejson": "^2", "typescript": "^4.1.2"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}